import { useState } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Badge } from "@/components/ui/badge";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
  DialogFooter,
} from "@/components/ui/dialog";
import { Textarea } from "@/components/ui/textarea";
import {
  Plus,
  Search,
  Edit,
  Trash2,
  Download,
  Upload,
  MoreHorizontal,
  Filter,
  X,
} from "lucide-react";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Checkbox } from "@/components/ui/checkbox";

interface Shelf {
  id: string;
  name: string;
  shelfNumber: number;
  lightControlCardNumber: string;
  positionCount: number;
  status: "normal" | "maintenance" | "offline";
  location: string;
  createdAt: string;
  lastModified: string;
}

interface Position {
  id: string;
  shelfId: string;
  positionNumber: number;
  positionCode: string; // 货位编号，如 No.030205
  lightControlNumber: number; // 三色灯序号 1-28
  status: "empty" | "occupied" | "reserved" | "maintenance";
  productId?: string;
  productName?: string;
  currentDevice?: string; // 当前存放的设备/惯组编号
  lastOperation?: string;
  lastOperationTime?: string;
  row: number; // 行号
  column: number; // 列号
}

// 模拟货架数据
const mockShelves: Shelf[] = [
  {
    id: "S001",
    name: "A区货架01",
    shelfNumber: 1,
    lightControlCardNumber: "LC001",
    positionCount: 30,
    status: "normal",
    location: "A区-01",
    createdAt: "2023-01-15",
    lastModified: "2024-01-10",
  },
  {
    id: "S002",
    name: "A区货架02",
    shelfNumber: 2,
    lightControlCardNumber: "LC002",
    positionCount: 30,
    status: "normal",
    location: "A区-02",
    createdAt: "2023-01-15",
    lastModified: "2024-01-10",
  },
  {
    id: "S003",
    name: "B区货架01",
    shelfNumber: 3,
    lightControlCardNumber: "LC003",
    positionCount: 40,
    status: "maintenance",
    location: "B区-01",
    createdAt: "2023-01-15",
    lastModified: "2024-01-12",
  },
  {
    id: "S004",
    name: "B区货架02",
    shelfNumber: 4,
    lightControlCardNumber: "LC004",
    positionCount: 40,
    status: "normal",
    location: "B区-02",
    createdAt: "2023-01-15",
    lastModified: "2024-01-10",
  },
  {
    id: "S005",
    name: "C区货架01",
    shelfNumber: 5,
    lightControlCardNumber: "LC005",
    positionCount: 40,
    status: "offline",
    location: "C区-01",
    createdAt: "2023-01-15",
    lastModified: "2024-01-14",
  },
];

// 生成货位编号
const generatePositionCode = (shelfNumber: number, row: number, column: number): string => {
  return `No.${shelfNumber.toString().padStart(2, '0')}${row.toString().padStart(2, '0')}${column.toString().padStart(2, '0')}`;
};

// 模拟货位数据
const generatePositions = (shelfId: string): Position[] => {
  const shelf = mockShelves.find(s => s.id === shelfId);
  if (!shelf) return [];

  return Array.from({ length: 40 }, (_, i) => {
    const row = Math.floor(i / 10) + 1; // 4行，每行10个货位
    const column = (i % 10) + 1; // 10列
    const positionCode = generatePositionCode(shelf.shelfNumber, row, column);
    
    return {
      id: `${shelfId}-P${i + 1}`,
      shelfId,
      positionNumber: i + 1,
      positionCode,
      lightControlNumber: (i % 28) + 1, // 1-28循环
      status: Math.random() > 0.7 ? "occupied" : Math.random() > 0.5 ? "empty" : Math.random() > 0.3 ? "reserved" : "maintenance",
      productId: Math.random() > 0.7 ? `PROD-${Math.floor(Math.random() * 1000)}` : undefined,
      productName: Math.random() > 0.7 ? `测试产品-${Math.floor(Math.random() * 100)}` : undefined,
      currentDevice: Math.random() > 0.7 ? `IG-${Math.floor(Math.random() * 1000)}` : undefined,
      lastOperation: Math.random() > 0.5 ? "入库" : "出库",
      lastOperationTime: "2024-01-15 10:30:25",
      row,
      column,
    };
  });
};

export default function ShelfManagement() {
  const [shelves, setShelves] = useState<Shelf[]>(mockShelves);
  const [selectedShelf, setSelectedShelf] = useState<Shelf | null>(null);
  const [positions, setPositions] = useState<Position[]>([]);
  const [isAddDialogOpen, setIsAddDialogOpen] = useState(false);
  const [isEditPositionDialogOpen, setIsEditPositionDialogOpen] = useState(false);
  const [editingPosition, setEditingPosition] = useState<Position | null>(null);
  const [isAdvancedSearchOpen, setIsAdvancedSearchOpen] = useState(false);
  const [searchConditions, setSearchConditions] = useState<{field: string, operator: string, value: string}[]>([
    { field: "name", operator: "contains", value: "" }
  ]);
  const [conditionLogic, setConditionLogic] = useState<"and" | "or">("and");
  
  // 加载货位数据
  const loadPositions = (shelfId: string) => {
    const positionsData = generatePositions(shelfId);
    setPositions(positionsData);
  };

  const handleShelfSelect = (shelf: Shelf) => {
    setSelectedShelf(shelf);
    loadPositions(shelf.id);
  };

  const handleEditPosition = (position: Position) => {
    setEditingPosition(position);
    setIsEditPositionDialogOpen(true);
  };

  const handleSavePosition = () => {
    if (editingPosition) {
      // 更新货位数据
      setPositions(prev => prev.map(pos => 
        pos.id === editingPosition.id ? editingPosition : pos
      ));
      setIsEditPositionDialogOpen(false);
      setEditingPosition(null);
    }
  };

  const addSearchCondition = () => {
    setSearchConditions([...searchConditions, { field: "name", operator: "contains", value: "" }]);
  };

  const removeSearchCondition = (index: number) => {
    const newConditions = [...searchConditions];
    newConditions.splice(index, 1);
    setSearchConditions(newConditions);
  };

  const updateSearchCondition = (index: number, field: string, value: any) => {
    const newConditions = [...searchConditions];
    newConditions[index] = { ...newConditions[index], [field]: value };
    setSearchConditions(newConditions);
  };

  const getStatusBadge = (status: string) => {
    const statusConfig: Record<string, { label: string, variant: "default" | "secondary" | "destructive" | "outline" }> = {
      normal: { label: "正常", variant: "default" },
      maintenance: { label: "维护中", variant: "secondary" },
      offline: { label: "离线", variant: "destructive" },
      empty: { label: "空闲", variant: "outline" },
      occupied: { label: "已占用", variant: "default" },
      reserved: { label: "已预留", variant: "secondary" },
    };

    const config = statusConfig[status] || { label: status, variant: "outline" };
    return <Badge variant={config.variant}>{config.label}</Badge>;
  };

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h2 className="text-3xl font-bold tracking-tight">仓储系统管理</h2>
      </div>

      <Tabs defaultValue="shelves" className="space-y-4">
        <TabsList>
          <TabsTrigger value="shelves">货架管理</TabsTrigger>
          <TabsTrigger value="positions">货位管理</TabsTrigger>
        </TabsList>

        <TabsContent value="shelves">
          <div className="space-y-6">
            {/* Shelf Management Header */}
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-2">
                <h3 className="text-lg font-semibold">货架管理</h3>
              </div>
              <div className="flex space-x-2">
                <Dialog open={isAddDialogOpen} onOpenChange={setIsAddDialogOpen}>
                  <DialogTrigger asChild>
                    <Button className="bg-blue-600 hover:bg-blue-700">
                      <Plus className="mr-2 h-4 w-4" />
                      新增货架
                    </Button>
                  </DialogTrigger>
                  <DialogContent className="max-w-2xl">
                    <DialogHeader>
                      <DialogTitle>新增货架</DialogTitle>
                      <DialogDescription>添加新的仓储货架</DialogDescription>
                    </DialogHeader>
                    <div className="grid gap-4 py-4">
                      <div className="grid grid-cols-2 gap-4">
                        <div className="space-y-2">
                          <Label htmlFor="shelfName">货架名称</Label>
                          <Input id="shelfName" placeholder="输入货架名称" />
                        </div>
                        <div className="space-y-2">
                          <Label htmlFor="shelfNumber">货架编号</Label>
                          <Input 
                            id="shelfNumber" 
                            placeholder="输入数字编号" 
                            type="number"
                          />
                          <p className="text-xs text-gray-500">必须是数字格式，用于系统唯一识别</p>
                        </div>
                      </div>
                      <div className="space-y-2">
                        <Label htmlFor="lightControlCardNumber">三色灯控制卡卡号</Label>
                        <Input id="lightControlCardNumber" placeholder="输入控制卡卡号" />
                        <p className="text-xs text-gray-500">根据三色灯控制卡预置的卡号填写</p>
                      </div>
                      <div className="grid grid-cols-2 gap-4">
                        <div className="space-y-2">
                          <Label htmlFor="location">货架位置</Label>
                          <Input id="location" placeholder="输入货架位置" />
                        </div>
                        <div className="space-y-2">
                          <Label htmlFor="status">货架状态</Label>
                          <Select defaultValue="normal">
                            <SelectTrigger>
                              <SelectValue placeholder="选择状态" />
                            </SelectTrigger>
                            <SelectContent>
                              <SelectItem value="normal">正常</SelectItem>
                              <SelectItem value="maintenance">维护中</SelectItem>
                              <SelectItem value="offline">离线</SelectItem>
                            </SelectContent>
                          </Select>
                        </div>
                      </div>
                      <div className="space-y-2">
                        <Label htmlFor="notes">备注</Label>
                        <Textarea id="notes" placeholder="输入备注信息" />
                      </div>
                    </div>
                    <DialogFooter>
                      <Button variant="outline" onClick={() => setIsAddDialogOpen(false)}>取消</Button>
                      <Button className="bg-blue-600 hover:bg-blue-700">保存</Button>
                    </DialogFooter>
                  </DialogContent>
                </Dialog>

                <DropdownMenu>
                  <DropdownMenuTrigger asChild>
                    <Button variant="outline" className="border-blue-200 hover:bg-blue-50 hover:text-blue-700">
                      <MoreHorizontal className="h-4 w-4" />
                    </Button>
                  </DropdownMenuTrigger>
                  <DropdownMenuContent>
                    <DropdownMenuItem>
                      <Upload className="mr-2 h-4 w-4" />
                      导入
                    </DropdownMenuItem>
                    <DropdownMenuItem>
                      <Download className="mr-2 h-4 w-4" />
                      导出
                    </DropdownMenuItem>
                  </DropdownMenuContent>
                </DropdownMenu>
              </div>
            </div>

            {/* Search and Filter */}
            <Card>
              <CardHeader className="pb-3">
                <div className="flex justify-between items-center">
                  <CardTitle>货架查询</CardTitle>
                  <Dialog open={isAdvancedSearchOpen} onOpenChange={setIsAdvancedSearchOpen}>
                    <DialogTrigger asChild>
                      <Button variant="outline" size="sm" className="border-blue-200 hover:bg-blue-50 hover:text-blue-700">
                        <Filter className="mr-2 h-4 w-4" />
                        高级查询
                      </Button>
                    </DialogTrigger>
                    <DialogContent className="max-w-2xl">
                      <DialogHeader>
                        <DialogTitle>高级查询</DialogTitle>
                        <DialogDescription>设置查询条件</DialogDescription>
                      </DialogHeader>
                      <div className="space-y-4">
                        {searchConditions.map((condition, index) => (
                          <div key={index} className="flex items-end space-x-2">
                            <div className="flex-1">
                              <Label className="text-xs">字段</Label>
                              <Select
                                value={condition.field}
                                onValueChange={(value) => updateSearchCondition(index, "field", value)}
                              >
                                <SelectTrigger>
                                  <SelectValue />
                                </SelectTrigger>
                                <SelectContent>
                                  <SelectItem value="name">货架名称</SelectItem>
                                  <SelectItem value="shelfNumber">货架编号</SelectItem>
                                  <SelectItem value="lightControlCardNumber">三色灯控制卡卡号</SelectItem>
                                  <SelectItem value="location">位置</SelectItem>
                                  <SelectItem value="status">状态</SelectItem>
                                </SelectContent>
                              </Select>
                            </div>
                            <div className="flex-1">
                              <Label className="text-xs">条件</Label>
                              <Select
                                value={condition.operator}
                                onValueChange={(value) => updateSearchCondition(index, "operator", value)}
                              >
                                <SelectTrigger>
                                  <SelectValue />
                                </SelectTrigger>
                                <SelectContent>
                                  <SelectItem value="equals">等于</SelectItem>
                                  <SelectItem value="contains">包含</SelectItem>
                                  <SelectItem value="startsWith">开头是</SelectItem>
                                  <SelectItem value="endsWith">结尾是</SelectItem>
                                </SelectContent>
                              </Select>
                            </div>
                            <div className="flex-1">
                              <Label className="text-xs">值</Label>
                              <Input
                                value={condition.value}
                                onChange={(e) => updateSearchCondition(index, "value", e.target.value)}
                                placeholder="输入值"
                              />
                            </div>
                            <Button
                              variant="ghost"
                              size="icon"
                              onClick={() => removeSearchCondition(index)}
                              disabled={searchConditions.length === 1}
                              className="hover:bg-red-50 hover:text-red-700"
                            >
                              <X className="h-4 w-4" />
                            </Button>
                            {index === searchConditions.length - 1 && (
                              <Button
                                variant="outline"
                                size="icon"
                                onClick={addSearchCondition}
                                className="border-blue-200 hover:bg-blue-50 hover:text-blue-700"
                              >
                                <Plus className="h-4 w-4" />
                              </Button>
                            )}
                          </div>
                        ))}

                        {searchConditions.length > 1 && (
                          <div className="flex items-center space-x-2">
                            <Label>条件关系</Label>
                            <div className="flex border rounded-md">
                              <Button
                                type="button"
                                variant={conditionLogic === "and" ? "default" : "ghost"}
                                className={conditionLogic === "and" 
                                  ? "rounded-r-none bg-blue-600 hover:bg-blue-700" 
                                  : "rounded-r-none hover:bg-blue-50 hover:text-blue-700"}
                                onClick={() => setConditionLogic("and")}
                              >
                                与
                              </Button>
                              <Button
                                type="button"
                                variant={conditionLogic === "or" ? "default" : "ghost"}
                                className={conditionLogic === "or" 
                                  ? "rounded-l-none bg-blue-600 hover:bg-blue-700" 
                                  : "rounded-l-none hover:bg-blue-50 hover:text-blue-700"}
                                onClick={() => setConditionLogic("or")}
                              >
                                或
                              </Button>
                            </div>
                          </div>
                        )}
                      </div>
                      <DialogFooter>
                        <Button variant="outline" onClick={() => setIsAdvancedSearchOpen(false)}>关闭</Button>
                        <Button className="bg-blue-600 hover:bg-blue-700">查询</Button>
                      </DialogFooter>
                    </DialogContent>
                  </Dialog>
                </div>
              </CardHeader>
              <CardContent>
                <div className="grid gap-4 md:grid-cols-4">
                  <div className="space-y-2">
                    <Label htmlFor="searchShelf">货架名称/编号</Label>
                    <Input id="searchShelf" placeholder="搜索货架" />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="filterLocation">位置</Label>
                    <Select>
                      <SelectTrigger>
                        <SelectValue placeholder="选择位置" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="all">全部</SelectItem>
                        <SelectItem value="A">A区</SelectItem>
                        <SelectItem value="B">B区</SelectItem>
                        <SelectItem value="C">C区</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="filterStatus">状态</Label>
                    <Select>
                      <SelectTrigger>
                        <SelectValue placeholder="选择状态" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="all">全部</SelectItem>
                        <SelectItem value="normal">正常</SelectItem>
                        <SelectItem value="maintenance">维护中</SelectItem>
                        <SelectItem value="offline">离线</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                  <div className="flex items-end">
                    <Button className="w-full bg-blue-600 hover:bg-blue-700">
                      <Search className="mr-2 h-4 w-4" />
                      查询
                    </Button>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Shelves Table */}
            <Card>
              <CardHeader>
                <CardTitle>货架列表</CardTitle>
              </CardHeader>
              <CardContent>
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead className="w-[50px]">
                        <Checkbox />
                      </TableHead>
                      <TableHead>货架名称</TableHead>
                      <TableHead>货架编号</TableHead>
                      <TableHead>三色灯控制卡</TableHead>
                      <TableHead>货位数量</TableHead>
                      <TableHead>状态</TableHead>
                      <TableHead>位置</TableHead>
                      <TableHead>操作</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {shelves.map((shelf) => (
                      <TableRow key={shelf.id}>
                        <TableCell>
                          <Checkbox />
                        </TableCell>
                        <TableCell>
                          <div className="font-medium">{shelf.name}</div>
                          <div className="text-sm text-gray-500">{shelf.id}</div>
                        </TableCell>
                        <TableCell>{shelf.shelfNumber}</TableCell>
                        <TableCell>{shelf.lightControlCardNumber}</TableCell>
                        <TableCell>{shelf.positionCount}</TableCell>
                        <TableCell>{getStatusBadge(shelf.status)}</TableCell>
                        <TableCell>{shelf.location}</TableCell>
                        <TableCell>
                          <div className="flex space-x-2">
                            <Button 
                              size="sm" 
                              variant="outline" 
                              onClick={() => handleShelfSelect(shelf)}
                              className="border-blue-200 hover:bg-blue-50 hover:text-blue-700"
                            >
                              查看货位
                            </Button>
                            <Button size="sm" variant="outline" className="border-blue-200 hover:bg-blue-50 hover:text-blue-700">
                              <Edit className="h-3 w-3" />
                            </Button>
                            <Button size="sm" variant="outline" className="border-red-200 hover:bg-red-50 hover:text-red-700">
                              <Trash2 className="h-3 w-3" />
                            </Button>
                          </div>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="positions">
          <div className="space-y-6">
            {/* Position Management Header */}
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-2">
                <h3 className="text-lg font-semibold">
                  {selectedShelf ? `${selectedShelf.name} - 货位管理` : "货位管理"}
                </h3>
              </div>
              {selectedShelf && (
                <div className="flex space-x-2">
                  <Button variant="outline" className="border-blue-200 hover:bg-blue-50 hover:text-blue-700">
                    <Download className="mr-2 h-4 w-4" />
                    导出货位数据
                  </Button>
                </div>
              )}
            </div>

            {selectedShelf ? (
              <Card>
                <CardHeader>
                  <CardTitle>货位列表 - {selectedShelf.name}</CardTitle>
                </CardHeader>
                <CardContent>
                  <Table>
                    <TableHeader>
                      <TableRow>
                        <TableHead>货位编号</TableHead>
                        <TableHead>状态</TableHead>
                        <TableHead>三色灯序号</TableHead>
                        <TableHead>当前存放设备</TableHead>
                        <TableHead>最近操作</TableHead>
                        <TableHead>操作时间</TableHead>
                        <TableHead>操作</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {positions.map((position) => (
                        <TableRow key={position.id}>
                          <TableCell>
                            <div className="font-medium">
                              {position.positionCode}
                            </div>
                            <div className="text-sm text-gray-500">
                              {position.id}
                            </div>
                          </TableCell>
                          <TableCell>{getStatusBadge(position.status)}</TableCell>
                          <TableCell>{position.lightControlNumber}</TableCell>
                          <TableCell>{position.currentDevice || "-"}</TableCell>
                          <TableCell>{position.lastOperation || "-"}</TableCell>
                          <TableCell>{position.lastOperationTime || "-"}</TableCell>
                          <TableCell>
                            <div className="flex space-x-2">
                              <Button 
                                size="sm" 
                                variant="outline" 
                                className="border-blue-200 hover:bg-blue-50 hover:text-blue-700"
                                onClick={() => handleEditPosition(position)}
                              >
                                <Edit className="h-3 w-3" />
                              </Button>
                            </div>
                          </TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                </CardContent>
              </Card>
            ) : (
              <Card>
                <CardContent className="flex flex-col items-center justify-center py-12">
                  <p className="text-gray-500 mb-4">请先在货架管理中选择一个货架</p>
                  <Button variant="outline" onClick={() => {
                    const shelvesTab = document.querySelector('[value="shelves"]') as HTMLElement;
                    if (shelvesTab) shelvesTab.click();
                  }} className="border-blue-200 hover:bg-blue-50 hover:text-blue-700">
                    返回货架管理
                  </Button>
                </CardContent>
              </Card>
            )}
          </div>
        </TabsContent>
      </Tabs>

      {/* 编辑货位对话框 */}
      <Dialog open={isEditPositionDialogOpen} onOpenChange={setIsEditPositionDialogOpen}>
        <DialogContent className="max-w-2xl">
          <DialogHeader>
            <DialogTitle>编辑货位</DialogTitle>
            <DialogDescription>修改货位信息</DialogDescription>
          </DialogHeader>
          {editingPosition && (
            <div className="grid gap-4 py-4">
              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="positionCode">货位编号</Label>
                  <Input 
                    id="positionCode" 
                    value={editingPosition.positionCode}
                    onChange={(e) => setEditingPosition({
                      ...editingPosition,
                      positionCode: e.target.value
                    })}
                    placeholder="如：No.030205"
                  />
                  <p className="text-xs text-gray-500">格式：No.货架编号行号列号（如：No.030205）</p>
                </div>
                <div className="space-y-2">
                  <Label htmlFor="lightControlNumber">三色灯序号</Label>
                  <Select 
                    value={editingPosition.lightControlNumber.toString()}
                    onValueChange={(value) => setEditingPosition({
                      ...editingPosition,
                      lightControlNumber: parseInt(value)
                    })}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="选择三色灯序号" />
                    </SelectTrigger>
                    <SelectContent>
                      {Array.from({ length: 28 }, (_, i) => (
                        <SelectItem key={i + 1} value={(i + 1).toString()}>
                          {i + 1}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                  <p className="text-xs text-gray-500">1-28，根据控制卡连线位置确定</p>
                </div>
              </div>
              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="currentDevice">当前存放设备</Label>
                  <Input 
                    id="currentDevice" 
                    value={editingPosition.currentDevice || ""}
                    onChange={(e) => setEditingPosition({
                      ...editingPosition,
                      currentDevice: e.target.value
                    })}
                    placeholder="惯组编号"
                  />
                  <p className="text-xs text-gray-500">当前货位上放置的惯组编号</p>
                </div>
                <div className="space-y-2">
                  <Label htmlFor="positionStatus">货位状态</Label>
                  <Select 
                    value={editingPosition.status}
                    onValueChange={(value) => setEditingPosition({
                      ...editingPosition,
                      status: value as "empty" | "occupied" | "reserved" | "maintenance"
                    })}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="选择状态" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="empty">空闲</SelectItem>
                      <SelectItem value="occupied">已占用</SelectItem>
                      <SelectItem value="reserved">已预留</SelectItem>
                      <SelectItem value="maintenance">维护中</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>
              <div className="space-y-2">
                <Label htmlFor="positionNotes">备注</Label>
                <Textarea 
                  id="positionNotes" 
                  placeholder="货位备注信息"
                />
              </div>
            </div>
          )}
          <DialogFooter>
            <Button variant="outline" onClick={() => setIsEditPositionDialogOpen(false)}>取消</Button>
            <Button className="bg-blue-600 hover:bg-blue-700" onClick={handleSavePosition}>保存</Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
} 