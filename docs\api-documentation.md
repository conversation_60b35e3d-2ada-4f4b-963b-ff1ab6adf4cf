# 惯组产品智能化存储及月稳测试系统 - API接口文档

## 1. 接口概述

### 1.1 基础信息
- **API版本**: v1.0
- **基础URL**: `http://api.example.com/v1`
- **数据格式**: JSON
- **字符编码**: UTF-8
- **认证方式**: JWT Token

### 1.2 响应格式
```json
{
    "code": 200,
    "message": "success",
    "data": {},
    "timestamp": "2024-01-15T10:30:00Z"
}
```

### 1.3 状态码说明
- `200`: 成功
- `400`: 请求参数错误
- `401`: 未授权
- `403`: 禁止访问
- `404`: 资源不存在
- `500`: 服务器内部错误

### 1.4 分页格式
```json
{
    "code": 200,
    "message": "success",
    "data": {
        "list": [],
        "pagination": {
            "page": 1,
            "page_size": 20,
            "total": 100,
            "total_pages": 5
        }
    }
}
```

## 2. 认证接口

### 2.1 用户登录
**POST** `/auth/login`

**请求参数:**
```json
{
    "username": "admin",
    "password": "password123"
}
```

**响应示例:**
```json
{
    "code": 200,
    "message": "登录成功",
    "data": {
        "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
        "refresh_token": "refresh_token_here",
        "user": {
            "id": 1,
            "username": "admin",
            "real_name": "管理员",
            "email": "<EMAIL>",
            "role": {
                "id": 1,
                "name": "系统管理员",
                "permissions": ["*"]
            }
        }
    }
}
```

### 2.2 刷新Token
**POST** `/auth/refresh`

**请求参数:**
```json
{
    "refresh_token": "refresh_token_here"
}
```

### 2.3 用户登出
**POST** `/auth/logout`

**请求头:**
```
Authorization: Bearer {token}
```

## 3. 用户管理接口

### 3.1 获取用户列表
**GET** `/users`

**查询参数:**
- `page`: 页码 (默认: 1)
- `page_size`: 每页数量 (默认: 20)
- `username`: 用户名 (可选)
- `real_name`: 真实姓名 (可选)
- `department_id`: 部门ID (可选)
- `role_id`: 角色ID (可选)
- `status`: 状态 (可选)

**响应示例:**
```json
{
    "code": 200,
    "message": "success",
    "data": {
        "list": [
            {
                "id": 1,
                "username": "admin",
                "real_name": "管理员",
                "email": "<EMAIL>",
                "phone": "13800138000",
                "department": {
                    "id": 1,
                    "name": "技术部"
                },
                "role": {
                    "id": 1,
                    "name": "系统管理员"
                },
                "status": "active",
                "last_login_at": "2024-01-15T10:30:00Z",
                "created_at": "2024-01-01T00:00:00Z"
            }
        ],
        "pagination": {
            "page": 1,
            "page_size": 20,
            "total": 1,
            "total_pages": 1
        }
    }
}
```

### 3.2 创建用户
**POST** `/users`

**请求参数:**
```json
{
    "username": "newuser",
    "password": "password123",
    "real_name": "新用户",
    "email": "<EMAIL>",
    "phone": "13800138001",
    "department_id": 1,
    "role_id": 2
}
```

### 3.3 更新用户
**PUT** `/users/{id}`

**请求参数:**
```json
{
    "real_name": "更新后的姓名",
    "email": "<EMAIL>",
    "phone": "13800138002",
    "department_id": 2,
    "role_id": 3
}
```

### 3.4 删除用户
**DELETE** `/users/{id}`

### 3.5 重置用户密码
**POST** `/users/{id}/reset-password`

## 4. 产品管理接口

### 4.1 获取产品列表
**GET** `/products`

**查询参数:**
- `page`: 页码
- `page_size`: 每页数量
- `product_code`: 产品编码
- `product_type`: 产品类型
- `status`: 产品状态
- `batch_number`: 批次号
- `location_id`: 库位ID

**响应示例:**
```json
{
    "code": 200,
    "message": "success",
    "data": {
        "list": [
            {
                "id": 1,
                "product_code": "P12345",
                "product_name": "惯组产品A",
                "product_type": "type_a",
                "specification": "规格说明",
                "manufacturer": "制造商",
                "model_number": "MODEL-001",
                "batch_number": "BATCH-2024-001",
                "production_date": "2024-01-01",
                "expiry_date": "2025-01-01",
                "status": "new",
                "current_location": {
                    "id": 1,
                    "location_code": "A-01-01",
                    "location_name": "A区1排1列"
                },
                "test_count": 0,
                "last_test_at": null,
                "created_at": "2024-01-01T00:00:00Z"
            }
        ],
        "pagination": {
            "page": 1,
            "page_size": 20,
            "total": 1,
            "total_pages": 1
        }
    }
}
```

### 4.2 创建产品
**POST** `/products`

**请求参数:**
```json
{
    "product_code": "P12346",
    "product_name": "惯组产品B",
    "product_type": "type_b",
    "specification": "规格说明",
    "manufacturer": "制造商",
    "model_number": "MODEL-002",
    "batch_number": "BATCH-2024-002",
    "production_date": "2024-01-01",
    "expiry_date": "2025-01-01"
}
```

### 4.3 更新产品
**PUT** `/products/{id}`

### 4.4 删除产品
**DELETE** `/products/{id}`

### 4.5 获取产品详情
**GET** `/products/{id}`

## 5. 设备管理接口

### 5.1 获取设备列表
**GET** `/equipment`

**查询参数:**
- `page`: 页码
- `page_size`: 每页数量
- `equipment_type`: 设备类型
- `status`: 设备状态
- `location_id`: 位置ID

**响应示例:**
```json
{
    "code": 200,
    "message": "success",
    "data": {
        "list": [
            {
                "id": 1,
                "equipment_code": "TRUSS-01",
                "equipment_name": "桁架1号",
                "equipment_type": "truss",
                "model": "TRUSS-MODEL-001",
                "manufacturer": "制造商",
                "ip_address": "*************",
                "port": 8080,
                "status": "online",
                "location": {
                    "id": 1,
                    "location_code": "TRUSS-AREA",
                    "location_name": "桁架区域"
                },
                "parameters": {
                    "max_weight": 1000,
                    "speed": 2.5
                },
                "last_heartbeat_at": "2024-01-15T10:30:00Z",
                "created_at": "2024-01-01T00:00:00Z"
            }
        ],
        "pagination": {
            "page": 1,
            "page_size": 20,
            "total": 1,
            "total_pages": 1
        }
    }
}
```

### 5.2 获取AGV设备详情
**GET** `/equipment/agv/{id}`

**响应示例:**
```json
{
    "code": 200,
    "message": "success",
    "data": {
        "id": 2,
        "equipment_code": "AGV-01",
        "equipment_name": "AGV小车1号",
        "equipment_type": "agv",
        "status": "online",
        "battery_level": 85.5,
        "battery_voltage": 48.2,
        "battery_current": 2.1,
        "charging_status": "idle",
        "current_task_id": 1,
        "speed": 1.5,
        "position_x": 100.5,
        "position_y": 200.3,
        "orientation": 45.0,
        "location": {
            "id": 2,
            "location_code": "AGV-AREA",
            "location_name": "AGV区域"
        }
    }
}
```

### 5.3 更新设备状态
**PUT** `/equipment/{id}/status`

**请求参数:**
```json
{
    "status": "maintenance",
    "notes": "设备维护"
}
```

### 5.4 获取设备心跳数据
**GET** `/equipment/{id}/heartbeat`

**查询参数:**
- `start_time`: 开始时间
- `end_time`: 结束时间
- `limit`: 限制数量

## 6. 测试管理接口

### 6.1 获取测试任务列表
**GET** `/test-tasks`

**查询参数:**
- `page`: 页码
- `page_size`: 每页数量
- `task_type`: 任务类型
- `status`: 任务状态
- `product_id`: 产品ID
- `vehicle_id`: AGV设备ID
- `start_time`: 开始时间
- `end_time`: 结束时间

**响应示例:**
```json
{
    "code": 200,
    "message": "success",
    "data": {
        "list": [
            {
                "id": 1,
                "task_code": "TASK-001",
                "task_type": "outbound_test",
                "product": {
                    "id": 1,
                    "product_code": "P12345",
                    "product_name": "惯组产品A"
                },
                "vehicle": {
                    "id": 2,
                    "equipment_code": "AGV-01",
                    "equipment_name": "AGV小车1号"
                },
                "priority": 1,
                "status": "running",
                "current_step": 3,
                "total_steps": 6,
                "progress": 50.0,
                "estimated_duration": 300,
                "actual_duration": 150,
                "start_time": "2024-01-15T10:00:00Z",
                "end_time": null,
                "created_by": {
                    "id": 1,
                    "username": "admin",
                    "real_name": "管理员"
                },
                "created_at": "2024-01-15T09:55:00Z"
            }
        ],
        "pagination": {
            "page": 1,
            "page_size": 20,
            "total": 1,
            "total_pages": 1
        }
    }
}
```

### 6.2 创建测试任务
**POST** `/test-tasks`

**请求参数:**
```json
{
    "task_type": "outbound_test",
    "product_id": 1,
    "priority": 1,
    "estimated_duration": 300
}
```

### 6.3 获取任务详情
**GET** `/test-tasks/{id}`

**响应示例:**
```json
{
    "code": 200,
    "message": "success",
    "data": {
        "id": 1,
        "task_code": "TASK-001",
        "task_type": "outbound_test",
        "product": {
            "id": 1,
            "product_code": "P12345",
            "product_name": "惯组产品A"
        },
        "priority": 1,
        "status": "running",
        "current_step": 3,
        "total_steps": 6,
        "progress": 50.0,
        "steps": [
            {
                "id": 1,
                "step_number": 1,
                "step_name": "通知MES产品出库",
                "step_description": "ICS通知MES产品P12345开始出库测试",
                "status": "completed",
                "start_time": "2024-01-15T10:00:00Z",
                "end_time": "2024-01-15T10:00:02Z",
                "duration": 2,
                "result_data": {
                    "mes_response": "success"
                }
            },
            {
                "id": 2,
                "step_number": 2,
                "step_name": "桁架执行出库操作",
                "step_description": "桁架系统将产品P12345从货位A-01搬运到接驳台-02",
                "status": "completed",
                "start_time": "2024-01-15T10:00:02Z",
                "end_time": "2024-01-15T10:00:48Z",
                "duration": 46,
                "result_data": {
                    "truss_id": "TRUSS-01",
                    "from_location": "A-01",
                    "to_location": "CONVEYOR-02"
                }
            },
            {
                "id": 3,
                "step_number": 3,
                "step_name": "AGV小车执行出库操作",
                "step_description": "AGV-03将产品P12345从接驳台-02搬运到测试台-01",
                "status": "running",
                "start_time": "2024-01-15T10:00:48Z",
                "end_time": null,
                "duration": null,
                "result_data": {
                    "agv_id": "AGV-03",
                    "current_position": "走廊B"
                }
            }
        ]
    }
}
```

### 6.4 一键还原任务
**POST** `/test-tasks/{id}/restore`

**响应示例:**
```json
{
    "code": 200,
    "message": "任务还原成功",
    "data": {
        "task_id": 1,
        "restore_time": "2024-01-15T10:30:00Z",
        "status": "running"
    }
}
```

### 6.5 暂停任务
**POST** `/test-tasks/{id}/pause`

### 6.6 继续任务
**POST** `/test-tasks/{id}/resume`

### 6.7 取消任务
**POST** `/test-tasks/{id}/cancel`

## 7. 测试计划管理接口

### 7.1 获取测试计划列表
**GET** `/test-plans`

**查询参数:**
- `page`: 页码
- `page_size`: 每页数量
- `plan_type`: 计划类型
- `status`: 计划状态
- `start_date`: 开始日期
- `end_date`: 结束日期

### 7.2 创建测试计划
**POST** `/test-plans`

**请求参数:**
```json
{
    "plan_name": "2024年1月测试计划",
    "plan_type": "monthly",
    "start_date": "2024-01-01",
    "end_date": "2024-01-31",
    "details": [
        {
            "product_type": "type_a",
            "quantity": 100,
            "priority": 1,
            "scheduled_date": "2024-01-15"
        },
        {
            "product_type": "type_b",
            "quantity": 50,
            "priority": 2,
            "scheduled_date": "2024-01-20"
        }
    ]
}
```

### 7.3 审批测试计划
**POST** `/test-plans/{id}/approve`

**请求参数:**
```json
{
    "approved": true,
    "comments": "计划审核通过"
}
```

## 8. 出入库管理接口

### 8.1 获取出入库记录
**GET** `/storage-records`

**查询参数:**
- `page`: 页码
- `page_size`: 每页数量
- `record_type`: 记录类型
- `product_id`: 产品ID
- `reason`: 原因
- `status`: 状态
- `start_time`: 开始时间
- `end_time`: 结束时间

### 8.2 创建出库记录
**POST** `/storage-records/out`

**请求参数:**
```json
{
    "product_id": 1,
    "from_location_id": 1,
    "reason": "test_start",
    "quantity": 1,
    "notes": "开始测试"
}
```

### 8.3 创建入库记录
**POST** `/storage-records/in`

**请求参数:**
```json
{
    "product_id": 1,
    "to_location_id": 2,
    "reason": "test_complete",
    "quantity": 1,
    "notes": "测试完成"
}
```

## 9. 三色灯控制接口

### 9.1 获取三色灯控制板列表
**GET** `/light-controls`

### 9.2 控制单个灯
**POST** `/light-controls/{id}/lights/{light_number}`

**请求参数:**
```json
{
    "color": "green",
    "duration": 5000
}
```

### 9.3 批量控制灯
**POST** `/light-controls/{id}/lights/batch`

**请求参数:**
```json
{
    "lights": [
        {
            "light_number": 1,
            "color": "green"
        },
        {
            "light_number": 2,
            "color": "red"
        }
    ]
}
```

### 9.4 获取灯状态
**GET** `/light-controls/{id}/lights`

## 10. 系统监控接口

### 10.1 获取系统状态
**GET** `/system/status`

**响应示例:**
```json
{
    "code": 200,
    "message": "success",
    "data": {
        "system": {
            "status": "running",
            "uptime": 86400,
            "version": "v1.0.0",
            "last_restart": "2024-01-01T00:00:00Z"
        },
        "database": {
            "status": "connected",
            "connections": 10,
            "slow_queries": 0
        },
        "equipment": {
            "total": 20,
            "online": 18,
            "offline": 1,
            "maintenance": 1
        },
        "tasks": {
            "running": 5,
            "pending": 10,
            "completed_today": 50
        }
    }
}
```

### 10.2 获取设备统计
**GET** `/system/equipment-stats`

### 10.3 获取任务统计
**GET** `/system/task-stats`

### 10.4 获取系统日志
**GET** `/system/logs`

**查询参数:**
- `page`: 页码
- `page_size`: 每页数量
- `level`: 日志级别
- `module`: 模块名称
- `start_time`: 开始时间
- `end_time`: 结束时间

## 11. 数据导出接口

### 11.1 导出测试数据
**GET** `/export/test-data`

**查询参数:**
- `start_date`: 开始日期
- `end_date`: 结束日期
- `product_type`: 产品类型
- `format`: 导出格式 (excel, csv, pdf)

### 11.2 导出设备数据
**GET** `/export/equipment-data`

### 11.3 导出出入库记录
**GET** `/export/storage-records`

## 12. WebSocket接口

### 12.1 实时数据推送
**WebSocket** `/ws/realtime`

**连接参数:**
```
ws://api.example.com/v1/ws/realtime?token={jwt_token}
```

**推送数据类型:**
```json
{
    "type": "equipment_status",
    "data": {
        "equipment_id": 1,
        "status": "online",
        "timestamp": "2024-01-15T10:30:00Z"
    }
}
```

### 12.2 任务进度推送
**WebSocket** `/ws/task-progress`

### 12.3 告警推送
**WebSocket** `/ws/alerts`

## 13. 错误码说明

### 13.1 通用错误码
- `10001`: 参数验证失败
- `10002`: 资源不存在
- `10003`: 权限不足
- `10004`: 操作失败
- `10005`: 系统错误

### 13.2 业务错误码
- `20001`: 设备离线
- `20002`: 任务已存在
- `20003`: 库位已满
- `20004`: 产品不存在
- `20005`: 测试参数错误

## 14. 接口限流

### 14.1 限流规则
- **认证接口**: 100次/分钟
- **查询接口**: 1000次/分钟
- **操作接口**: 100次/分钟
- **导出接口**: 10次/小时

### 14.2 限流响应
```json
{
    "code": 429,
    "message": "请求过于频繁，请稍后重试",
    "data": {
        "retry_after": 60
    }
}
```

## 15. 接口测试

### 15.1 测试环境
- **测试URL**: `http://test-api.example.com/v1`
- **测试账号**: `test_user`
- **测试密码**: `test_password`

### 15.2 测试工具
- Postman
- curl
- 系统内置测试工具

### 15.3 测试用例
详细的测试用例请参考 `docs/test-cases.md` 文档。 