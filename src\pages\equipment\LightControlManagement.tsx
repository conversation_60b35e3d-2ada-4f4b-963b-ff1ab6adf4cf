import { useState, useEffect } from "react";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { <PERSON><PERSON>, <PERSON>bsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import {
  AlertCircle,
  Settings,
  Wifi,
  WifiOff,
  Circle,
  MoreHorizontal,
  Edit,
  RefreshCw,
  Save,
  X,
} from "lucide-react";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
  DialogFooter,
} from "@/components/ui/dialog";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";

// 控制板信息接口
interface ControlBoard {
  id: string;
  name: string;
  ipAddress: string;
  port: number;
  status: "online" | "offline" | "error";
  connectedRacks: string[];
  lightCount: number;
  lastHeartbeat: string;
  firmware: string;
}

// 三色灯状态接口
interface LightStatus {
  position: string;
  rack: string;
  color: "red" | "yellow" | "green";
  mode: "solid" | "blinking" | "off";
  controlBoard: string;
  lastUpdated: string;
}

// 货架信息接口
interface RackInfo {
  id: string;
  name: string;
  positions: number;
  zone: string;
}

// 模拟数据
const mockControlBoards: ControlBoard[] = [
  {
    id: "CB-001",
    name: "控制板01",
    ipAddress: "*************",
    port: 8001,
    status: "online",
    connectedRacks: ["A区-货架01", "A区-货架02"],
    lightCount: 50,
    lastHeartbeat: "2024-01-10 14:23:15",
    firmware: "v2.1.3",
  },
  {
    id: "CB-002",
    name: "控制板02",
    ipAddress: "*************",
    port: 8001,
    status: "online",
    connectedRacks: ["A区-货架03", "A区-货架04"],
    lightCount: 50,
    lastHeartbeat: "2024-01-10 14:23:12",
    firmware: "v2.1.3",
  },
  {
    id: "CB-003",
    name: "控制板03",
    ipAddress: "*************",
    port: 8001,
    status: "offline",
    connectedRacks: ["A区-货架05", "A区-货架06"],
    lightCount: 50,
    lastHeartbeat: "2024-01-10 14:18:45",
    firmware: "v2.1.2",
  },
  {
    id: "CB-004",
    name: "控制板04",
    ipAddress: "*************",
    port: 8001,
    status: "online",
    connectedRacks: ["B区-货架01", "B区-货架02"],
    lightCount: 50,
    lastHeartbeat: "2024-01-10 14:23:18",
    firmware: "v2.1.3",
  },
  {
    id: "CB-005",
    name: "控制板05",
    ipAddress: "*************",
    port: 8001,
    status: "online",
    connectedRacks: ["B区-货架03", "B区-货架04"],
    lightCount: 50,
    lastHeartbeat: "2024-01-10 14:23:20",
    firmware: "v2.1.3",
  },
  {
    id: "CB-006",
    name: "控制板06",
    ipAddress: "*************",
    port: 8001,
    status: "error",
    connectedRacks: ["B区-货架05", "B区-货架06"],
    lightCount: 50,
    lastHeartbeat: "2024-01-10 14:20:33",
    firmware: "v2.1.1",
  },
  {
    id: "CB-007",
    name: "控制板07",
    ipAddress: "*************",
    port: 8001,
    status: "online",
    connectedRacks: ["C区-货架01", "C区-货架02"],
    lightCount: 50,
    lastHeartbeat: "2024-01-10 14:23:16",
    firmware: "v2.1.3",
  },
  {
    id: "CB-008",
    name: "控制板08",
    ipAddress: "*************",
    port: 8001,
    status: "online",
    connectedRacks: ["C区-货架03", "C区-货架04"],
    lightCount: 50,
    lastHeartbeat: "2024-01-10 14:23:14",
    firmware: "v2.1.3",
  },
  {
    id: "CB-009",
    name: "控制板09",
    ipAddress: "*************",
    port: 8001,
    status: "online",
    connectedRacks: ["C区-货架05", "C区-货架06"],
    lightCount: 50,
    lastHeartbeat: "2024-01-10 14:23:19",
    firmware: "v2.1.3",
  },
  {
    id: "CB-010",
    name: "控制板10",
    ipAddress: "*************",
    port: 8001,
    status: "online",
    connectedRacks: ["D区-货架01", "D区-货架02"],
    lightCount: 50,
    lastHeartbeat: "2024-01-10 14:23:17",
    firmware: "v2.1.3",
  },
  {
    id: "CB-011",
    name: "控制板11",
    ipAddress: "*************",
    port: 8001,
    status: "online",
    connectedRacks: ["D区-货架03", "D区-货架04"],
    lightCount: 50,
    lastHeartbeat: "2024-01-10 14:23:21",
    firmware: "v2.1.3",
  },
  {
    id: "CB-012",
    name: "控制板12",
    ipAddress: "*************",
    port: 8001,
    status: "online",
    connectedRacks: ["D区-货架05", "D区-货架06"],
    lightCount: 50,
    lastHeartbeat: "2024-01-10 14:23:13",
    firmware: "v2.1.3",
  },
];

const mockRacks: RackInfo[] = [
  { id: "A01", name: "A区-货架01", positions: 25, zone: "A区" },
  { id: "A02", name: "A区-货架02", positions: 25, zone: "A区" },
  { id: "A03", name: "A区-货架03", positions: 25, zone: "A区" },
  { id: "A04", name: "A区-货架04", positions: 25, zone: "A区" },
  { id: "A05", name: "A区-货架05", positions: 25, zone: "A区" },
  { id: "A06", name: "A区-货架06", positions: 25, zone: "A区" },
  { id: "B01", name: "B区-货架01", positions: 25, zone: "B区" },
  { id: "B02", name: "B区-货架02", positions: 25, zone: "B区" },
  { id: "B03", name: "B区-货架03", positions: 25, zone: "B区" },
  { id: "B04", name: "B区-货架04", positions: 25, zone: "B区" },
  { id: "B05", name: "B区-货架05", positions: 25, zone: "B区" },
  { id: "B06", name: "B区-货架06", positions: 25, zone: "B区" },
  { id: "C01", name: "C区-货架01", positions: 25, zone: "C区" },
  { id: "C02", name: "C区-货架02", positions: 25, zone: "C区" },
  { id: "C03", name: "C区-货架03", positions: 25, zone: "C区" },
  { id: "C04", name: "C区-货架04", positions: 25, zone: "C区" },
  { id: "C05", name: "C区-货架05", positions: 25, zone: "C区" },
  { id: "C06", name: "C区-货架06", positions: 25, zone: "C区" },
  { id: "D01", name: "D区-货架01", positions: 25, zone: "D区" },
  { id: "D02", name: "D区-货架02", positions: 25, zone: "D区" },
  { id: "D03", name: "D区-货架03", positions: 25, zone: "D区" },
  { id: "D04", name: "D区-货架04", positions: 25, zone: "D区" },
  { id: "D05", name: "D区-货架05", positions: 25, zone: "D区" },
  { id: "D06", name: "D区-货架06", positions: 25, zone: "D区" },
];

export default function LightControlManagement() {
  const [controlBoards, setControlBoards] =
    useState<ControlBoard[]>(mockControlBoards);
  const [selectedBoard, setSelectedBoard] = useState<ControlBoard | null>(null);
  const [editingBoard, setEditingBoard] = useState<ControlBoard | null>(null);
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);

  // 状态统计
  const onlineBoards = controlBoards.filter(
    (board) => board.status === "online",
  ).length;
  const offlineBoards = controlBoards.filter(
    (board) => board.status === "offline",
  ).length;
  const errorBoards = controlBoards.filter(
    (board) => board.status === "error",
  ).length;
  const totalLights = controlBoards.reduce(
    (sum, board) => sum + board.lightCount,
    0,
  );

  // 获取状态徽章样式
  const getStatusBadge = (status: string) => {
    switch (status) {
      case "online":
        return (
          <Badge className="bg-green-100 text-green-800 hover:bg-green-100">
            在线
          </Badge>
        );
      case "offline":
        return (
          <Badge className="bg-gray-100 text-gray-800 hover:bg-gray-100">
            离线
          </Badge>
        );
      case "error":
        return (
          <Badge className="bg-red-100 text-red-800 hover:bg-red-100">
            故障
          </Badge>
        );
      default:
        return <Badge>未知</Badge>;
    }
  };

  // 获取状态图标
  const getStatusIcon = (status: string) => {
    switch (status) {
      case "online":
        return <Wifi className="h-4 w-4 text-green-600" />;
      case "offline":
        return <WifiOff className="h-4 w-4 text-gray-600" />;
      case "error":
        return <AlertCircle className="h-4 w-4 text-red-600" />;
      default:
        return <Circle className="h-4 w-4" />;
    }
  };

  // 处理编辑控制板
  const handleEditBoard = (board: ControlBoard) => {
    setEditingBoard({ ...board });
    setIsEditDialogOpen(true);
  };

  // 保存编辑
  const handleSaveEdit = () => {
    if (editingBoard) {
      setControlBoards((prev) =>
        prev.map((board) =>
          board.id === editingBoard.id ? editingBoard : board,
        ),
      );
      setIsEditDialogOpen(false);
      setEditingBoard(null);
    }
  };

  // 刷新控制板状态
  const handleRefreshBoard = (boardId: string) => {
    console.log(`刷新控制板 ${boardId} 状态`);
    // 这里可以添加实际的网络请求
  };

  // 生成三色灯状态示例数据
  const generateLightStatus = (boardId: string): LightStatus[] => {
    const board = controlBoards.find((b) => b.id === boardId);
    if (!board) return [];

    const lights: LightStatus[] = [];
    board.connectedRacks.forEach((rack, rackIndex) => {
      for (let i = 1; i <= 25; i++) {
        const position = `${rack}-${i.toString().padStart(2, "0")}`;
        const random = Math.random();
        let color: "red" | "yellow" | "green";
        let mode: "solid" | "blinking" | "off";

        if (random < 0.6) {
          color = "green";
          mode = "solid";
        } else if (random < 0.8) {
          color = "yellow";
          mode = "blinking";
        } else {
          color = "red";
          mode = "solid";
        }

        lights.push({
          position,
          rack,
          color,
          mode,
          controlBoard: boardId,
          lastUpdated: new Date().toLocaleString(),
        });
      }
    });
    return lights;
  };

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <div>
          <h2 className="text-3xl font-bold tracking-tight">
            三色灯控制板管理
          </h2>
          <p className="text-muted-foreground">
            管理仓储系统中的三色灯控制板，监控600个货位的状态指示灯
          </p>
        </div>
        <Button>
          <RefreshCw className="mr-2 h-4 w-4" />
          刷新全部
        </Button>
      </div>

      {/* 概览统计 */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">控制板总数</CardTitle>
            <Settings className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{controlBoards.length}</div>
            <p className="text-xs text-muted-foreground">
              管理 {totalLights} 个三色灯
            </p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">在线设备</CardTitle>
            <Wifi className="h-4 w-4 text-green-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-green-600">
              {onlineBoards}
            </div>
            <p className="text-xs text-muted-foreground">正常运行中</p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">离线设备</CardTitle>
            <WifiOff className="h-4 w-4 text-gray-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-gray-600">
              {offlineBoards}
            </div>
            <p className="text-xs text-muted-foreground">需要检查连接</p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">故障设备</CardTitle>
            <AlertCircle className="h-4 w-4 text-red-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-red-600">{errorBoards}</div>
            <p className="text-xs text-muted-foreground">需要维修处理</p>
          </CardContent>
        </Card>
      </div>

      <Tabs defaultValue="overview" className="space-y-4">
        <TabsList>
          <TabsTrigger value="overview">控制板概览</TabsTrigger>
          <TabsTrigger value="lights">三色灯状态</TabsTrigger>
          <TabsTrigger value="mapping">货架映射</TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-4">
          <div className="grid gap-4">
            {controlBoards.map((board) => (
              <Card key={board.id}>
                <CardHeader>
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-4">
                      {getStatusIcon(board.status)}
                      <div>
                        <CardTitle className="text-lg">{board.name}</CardTitle>
                        <CardDescription>
                          {board.ipAddress}:{board.port} | 固件版本:{" "}
                          {board.firmware}
                        </CardDescription>
                      </div>
                    </div>
                    <div className="flex items-center space-x-2">
                      {getStatusBadge(board.status)}
                      <DropdownMenu>
                        <DropdownMenuTrigger asChild>
                          <Button variant="ghost" size="sm">
                            <MoreHorizontal className="h-4 w-4" />
                          </Button>
                        </DropdownMenuTrigger>
                        <DropdownMenuContent>
                          <DropdownMenuItem
                            onClick={() => handleEditBoard(board)}
                          >
                            <Edit className="mr-2 h-4 w-4" />
                            编辑配置
                          </DropdownMenuItem>
                          <DropdownMenuItem
                            onClick={() => handleRefreshBoard(board.id)}
                          >
                            <RefreshCw className="mr-2 h-4 w-4" />
                            刷新状态
                          </DropdownMenuItem>
                        </DropdownMenuContent>
                      </DropdownMenu>
                    </div>
                  </div>
                </CardHeader>
                <CardContent>
                  <div className="grid gap-4 md:grid-cols-3">
                    <div>
                      <Label className="text-sm font-medium">连接货架</Label>
                      <div className="mt-1">
                        {board.connectedRacks.map((rack, index) => (
                          <Badge
                            key={index}
                            variant="outline"
                            className="mr-1 mb-1"
                          >
                            {rack}
                          </Badge>
                        ))}
                      </div>
                    </div>
                    <div>
                      <Label className="text-sm font-medium">三色灯数量</Label>
                      <p className="text-lg font-semibold">
                        {board.lightCount} 个
                      </p>
                    </div>
                    <div>
                      <Label className="text-sm font-medium">最后心跳</Label>
                      <p className="text-sm text-muted-foreground">
                        {board.lastHeartbeat}
                      </p>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </TabsContent>

        <TabsContent value="lights" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>三色灯状态监控</CardTitle>
              <CardDescription>
                实时显示所有货位的三色灯状态：绿色(有产品)、黄色闪烁(测试中)、红色(空货位)
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid gap-4">
                {controlBoards.slice(0, 3).map((board) => {
                  const lights = generateLightStatus(board.id);
                  return (
                    <div key={board.id} className="border rounded-lg p-4">
                      <div className="flex items-center justify-between mb-4">
                        <h4 className="font-semibold">{board.name}</h4>
                        {getStatusBadge(board.status)}
                      </div>
                      <div className="grid grid-cols-10 gap-2">
                        {lights.slice(0, 50).map((light, index) => (
                          <div
                            key={index}
                            className={`w-6 h-6 rounded-full border-2 ${
                              light.color === "green"
                                ? "bg-green-500 border-green-600"
                                : light.color === "yellow"
                                  ? "bg-yellow-500 border-yellow-600 animate-pulse"
                                  : "bg-red-500 border-red-600"
                            }`}
                            title={`${light.position} - ${light.color} ${light.mode}`}
                          />
                        ))}
                      </div>
                    </div>
                  );
                })}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="mapping" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>货架映射配置</CardTitle>
              <CardDescription>
                配置控制板与货架的对应关系，确保三色灯控制准确性
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid gap-6 md:grid-cols-2">
                {["A区", "B区", "C区", "D区"].map((zone) => (
                  <div key={zone} className="border rounded-lg p-4">
                    <h4 className="font-semibold mb-4">{zone}</h4>
                    <div className="space-y-2">
                      {mockRacks
                        .filter((rack) => rack.zone === zone)
                        .map((rack) => {
                          const controlBoard = controlBoards.find((board) =>
                            board.connectedRacks.includes(rack.name),
                          );
                          return (
                            <div
                              key={rack.id}
                              className="flex items-center justify-between py-2 px-3 bg-gray-50 rounded"
                            >
                              <span className="font-medium">{rack.name}</span>
                              <div className="flex items-center space-x-2">
                                <span className="text-sm text-muted-foreground">
                                  {rack.positions} 位
                                </span>
                                <Badge variant="outline">
                                  {controlBoard?.name || "未分配"}
                                </Badge>
                              </div>
                            </div>
                          );
                        })}
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>

      {/* 编辑控制板对话框 */}
      <Dialog open={isEditDialogOpen} onOpenChange={setIsEditDialogOpen}>
        <DialogContent className="sm:max-w-[525px]">
          <DialogHeader>
            <DialogTitle>编辑控制板配置</DialogTitle>
            <DialogDescription>
              修改控制板的网络配置和连接设置
            </DialogDescription>
          </DialogHeader>
          {editingBoard && (
            <div className="grid gap-4 py-4">
              <div className="grid grid-cols-4 items-center gap-4">
                <Label htmlFor="name" className="text-right">
                  名称
                </Label>
                <Input
                  id="name"
                  value={editingBoard.name}
                  onChange={(e) =>
                    setEditingBoard({ ...editingBoard, name: e.target.value })
                  }
                  className="col-span-3"
                />
              </div>
              <div className="grid grid-cols-4 items-center gap-4">
                <Label htmlFor="ip" className="text-right">
                  IP地址
                </Label>
                <Input
                  id="ip"
                  value={editingBoard.ipAddress}
                  onChange={(e) =>
                    setEditingBoard({
                      ...editingBoard,
                      ipAddress: e.target.value,
                    })
                  }
                  className="col-span-3"
                />
              </div>
              <div className="grid grid-cols-4 items-center gap-4">
                <Label htmlFor="port" className="text-right">
                  端口
                </Label>
                <Input
                  id="port"
                  type="number"
                  value={editingBoard.port}
                  onChange={(e) =>
                    setEditingBoard({
                      ...editingBoard,
                      port: parseInt(e.target.value),
                    })
                  }
                  className="col-span-3"
                />
              </div>
              <div className="grid grid-cols-4 items-center gap-4">
                <Label htmlFor="firmware" className="text-right">
                  固件版本
                </Label>
                <Input
                  id="firmware"
                  value={editingBoard.firmware}
                  onChange={(e) =>
                    setEditingBoard({
                      ...editingBoard,
                      firmware: e.target.value,
                    })
                  }
                  className="col-span-3"
                />
              </div>
            </div>
          )}
          <DialogFooter>
            <Button
              variant="outline"
              onClick={() => setIsEditDialogOpen(false)}
            >
              <X className="mr-2 h-4 w-4" />
              取消
            </Button>
            <Button onClick={handleSaveEdit}>
              <Save className="mr-2 h-4 w-4" />
              保存
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
}
