# A产品智能化存储及月稳测试系统 项目设置指南

## 📋 项目概述

这是一个基于 React + TypeScript + Vite 的智能仓储管理系统，包含以下主要功能：

- 🎨 工业大屏 3D 可视化
- 🔧 测试控制中心 (含一键还原功能)
- 💡 三色灯控制板管理
- 📦 产品管理系统
- ⚙️ 设备管理 (桁架、AGV、测试台)
- 📊 数据分析与统计

## 🚀 快速开始

### 1. 创建项目

```bash
npm create vite@latest a-product-storage-system -- --template react-ts
cd a-product-storage-system
```

### 2. 安装依赖

```bash
npm install @hookform/resolvers @radix-ui/react-accordion @radix-ui/react-alert-dialog @radix-ui/react-aspect-ratio @radix-ui/react-avatar @radix-ui/react-checkbox @radix-ui/react-collapsible @radix-ui/react-context-menu @radix-ui/react-dialog @radix-ui/react-dropdown-menu @radix-ui/react-hover-card @radix-ui/react-label @radix-ui/react-menubar @radix-ui/react-navigation-menu @radix-ui/react-popover @radix-ui/react-progress @radix-ui/react-radio-group @radix-ui/react-scroll-area @radix-ui/react-select @radix-ui/react-separator @radix-ui/react-slider @radix-ui/react-slot @radix-ui/react-switch @radix-ui/react-tabs @radix-ui/react-toast @radix-ui/react-toggle @radix-ui/react-toggle-group @radix-ui/react-tooltip @react-three/fiber @tanstack/react-query @types/three class-variance-authority clsx cmdk date-fns embla-carousel-react framer-motion input-otp lucide-react next-themes react-day-picker react-hook-form react-resizable-panels react-router-dom recharts sonner tailwind-merge tailwindcss-animate three vaul zod

npm install -D @tailwindcss/typography autoprefixer postcss prettier tailwindcss
```

### 3. 配置 TailwindCSS

创建 `tailwind.config.js`:

```javascript
/** @type {import('tailwindcss').Config} */
export default {
  content: ["./index.html", "./src/**/*.{js,ts,jsx,tsx}"],
  theme: {
    extend: {},
  },
  plugins: [require("tailwindcss-animate")],
};
```

## 📁 项目结构

```
src/
├── components/
│   ├── ui/                     # UI 组件库
│   ├── Layout.tsx              # 主布局组件
│   ├── DashboardCard.tsx       # 仪表板卡片
│   ├── StatusChart.tsx         # 状态图表
│   └── WarehouseVisualization.tsx
├── pages/
│   ├── Dashboard.tsx           # 管理首页
│   ├── IndustrialDashboard.tsx # 工业大屏
│   ├── test/
│   │   ├── TestControl.tsx     # 测试控制中心
│   │   ├── TestPlanning.tsx    # 测试计划管理
│   │   ├── DataAnalysis.tsx    # 数据分析查询
│   │   ├── ComprehensiveDashboard.tsx
│   │   └── ProductManagement.tsx
│   ├── equipment/
│   │   ├── BaseInfo.tsx        # 设备基准信息
│   │   ├── ChargingManagement.tsx # AGV充电管理
│   │   └── LightControlManagement.tsx # 三色灯控制
│   ├── warehouse/
│   │   └── InOutOperations.tsx # 出入库操作
│   └── system/
│       └── UserManagement.tsx  # 用户管理
├── lib/
│   └── utils.ts                # 工具函数
└── App.tsx                     # 主应用组件
```

## 🔧 主要功能特性

### 工业大屏

- 3D Canvas 仓储可视化
- 深蓝工业主题
- 实时设备状态监控
- 环境数据展示
- 报警信息管理

### 测试控制中心

- 自动/手动模式切换
- 设备控制 (桁架、AGV)
- 一键故障还原功能
- 实时操作监控

### 三色灯控制管理

- 12个控制板管理 (每板50灯)
- IP 地址配置
- 货架映射关系
- 实时状态监控

## 🎨 主题配色

- **主色调**: 深蓝工业系列
- **背景**: slate-950, blue-950, indigo-950
- **边框**: blue-400/30, indigo-400/40
- **文字**: blue-100, blue-200, blue-300
- **状态**: 绿色(运行), 黄色(暂停), 红色(故障)

## 📊 数据模拟

项目包含完整的模拟数据：

- 设备状态数据
- 测试记录数据
- 环境监控数据
- 报警信息数据
- 统计图表数据

## 🚀 启动项目

```bash
npm run dev
```

访问 `http://localhost:5173` 查看应用。

## 📝 文件获取

由于项目文件较多，建议：

1. 按照上述步骤创建基础项目
2. 逐个复制下面提供的文件内容
3. 确保所有依赖正确安装

## 🤝 贡献

欢迎提交 Issue 和 Pull Request！
