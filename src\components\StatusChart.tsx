import {
  <PERSON><PERSON><PERSON>,
  <PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  CartesianGrid,
  Tooltip,
  ResponsiveContainer,
  <PERSON><PERSON>hart,
  Pie,
  Cell,
  LineChart,
  Line,
} from "recharts";

interface StatusChartProps {
  type: "bar" | "pie" | "line";
  data: any[];
  dataKey?: string;
  nameKey?: string;
  colors?: string[];
  barWidth?: number;
  pieRadius?: number;
  multipleBars?: boolean;
  multipleLines?: boolean;
}

const COLORS = [
  "#3b82f6", // 蓝色
  "#60a5fa", // 浅蓝色
  "#1d4ed8", // 深蓝色
  "#1e40af", // 更深的蓝色
  "#0ea5e9", // 天蓝色
  "#0284c7", // 中蓝色
];

export function StatusChart({
  type,
  data,
  dataKey = "value",
  nameKey = "name",
  colors = COLORS,
  barWidth,
  pieRadius,
  multipleBars = false,
  multipleLines = false,
}: StatusChartProps) {
  if (type === "bar") {
    if (multipleBars) {
      return (
        <ResponsiveContainer width="100%" height={300}>
          <BarChart data={data}>
            <CartesianGrid strokeDasharray="3 3" stroke="#1e40af" opacity={0.3} />
            <XAxis dataKey={nameKey} tick={{ fill: '#e2e8f0', fontSize: 12 }} />
            <YAxis tick={{ fill: '#e2e8f0', fontSize: 12 }} />
            <Tooltip 
              contentStyle={{ 
                backgroundColor: 'rgba(15, 23, 42, 0.9)', 
                border: '1px solid rgba(59, 130, 246, 0.3)',
                borderRadius: '8px',
                color: '#e2e8f0'
              }} 
            />
            <Bar dataKey="inbound" fill={colors[0]} maxBarSize={barWidth} />
            <Bar dataKey="outbound" fill={colors[1]} maxBarSize={barWidth} />
          </BarChart>
        </ResponsiveContainer>
      );
    } else {
      return (
        <ResponsiveContainer width="100%" height={300}>
          <BarChart data={data}>
            <CartesianGrid strokeDasharray="3 3" stroke="#1e40af" opacity={0.3} />
            <XAxis dataKey={nameKey} tick={{ fill: '#e2e8f0', fontSize: 12 }} />
            <YAxis tick={{ fill: '#e2e8f0', fontSize: 12 }} />
            <Tooltip 
              contentStyle={{ 
                backgroundColor: 'rgba(15, 23, 42, 0.9)', 
                border: '1px solid rgba(59, 130, 246, 0.3)',
                borderRadius: '8px',
                color: '#e2e8f0'
              }} 
            />
            <Bar dataKey={dataKey} fill={colors[0]} maxBarSize={barWidth} />
          </BarChart>
        </ResponsiveContainer>
      );
    }
  }

  if (type === "pie") {
    return (
      <ResponsiveContainer width="100%" height={300}>
        <PieChart>
          <Pie
            data={data}
            cx="50%"
            cy="50%"
            labelLine={false}
            label={({ name, percent }) =>
              `${name} ${(percent * 100).toFixed(0)}%`
            }
            outerRadius={pieRadius || 80}
            fill="#8884d8"
            dataKey={dataKey}
          >
            {data.map((entry, index) => (
              <Cell
                key={`cell-${index}`}
                fill={colors[index % colors.length]}
              />
            ))}
          </Pie>
          <Tooltip 
            contentStyle={{ 
              backgroundColor: 'rgba(15, 23, 42, 0.9)', 
              border: '1px solid rgba(59, 130, 246, 0.3)',
              borderRadius: '8px',
              color: '#e2e8f0'
            }} 
          />
        </PieChart>
      </ResponsiveContainer>
    );
  }

  if (type === "line") {
    if (multipleLines) {
      return (
        <ResponsiveContainer width="100%" height={300}>
          <LineChart data={data}>
            <CartesianGrid strokeDasharray="3 3" stroke="#1e40af" opacity={0.3} />
            <XAxis dataKey={nameKey} tick={{ fill: '#e2e8f0', fontSize: 12 }} />
            <YAxis tick={{ fill: '#e2e8f0', fontSize: 12 }} />
            <Tooltip 
              contentStyle={{ 
                backgroundColor: 'rgba(15, 23, 42, 0.9)', 
                border: '1px solid rgba(59, 130, 246, 0.3)',
                borderRadius: '8px',
                color: '#e2e8f0'
              }} 
            />
            <Line
              type="monotone"
              dataKey="inboundEfficiency"
              stroke={colors[0]}
              strokeWidth={3}
              dot={{ fill: colors[0], strokeWidth: 2, r: 4 }}
            />
            <Line
              type="monotone"
              dataKey="outboundEfficiency"
              stroke={colors[1]}
              strokeWidth={3}
              dot={{ fill: colors[1], strokeWidth: 2, r: 4 }}
            />
          </LineChart>
        </ResponsiveContainer>
      );
    } else {
      return (
        <ResponsiveContainer width="100%" height={300}>
          <LineChart data={data}>
            <CartesianGrid strokeDasharray="3 3" stroke="#1e40af" opacity={0.3} />
            <XAxis dataKey={nameKey} tick={{ fill: '#e2e8f0', fontSize: 12 }} />
            <YAxis tick={{ fill: '#e2e8f0', fontSize: 12 }} />
            <Tooltip 
              contentStyle={{ 
                backgroundColor: 'rgba(15, 23, 42, 0.9)', 
                border: '1px solid rgba(59, 130, 246, 0.3)',
                borderRadius: '8px',
                color: '#e2e8f0'
              }} 
            />
            <Line
              type="monotone"
              dataKey={dataKey}
              stroke={colors[0]}
              strokeWidth={3}
              dot={{ fill: colors[0], strokeWidth: 2, r: 4 }}
            />
          </LineChart>
        </ResponsiveContainer>
      );
    }
  }

  return null;
}
