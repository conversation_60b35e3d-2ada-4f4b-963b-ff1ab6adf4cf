import React from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { Badge } from "@/components/ui/badge";
import { Search, Download, Upload, RefreshCw, AlertTriangle } from "lucide-react";

const ProductInventory = () => {
  // 模拟惯组盘点数据 - 每个编号的惯组产品是唯一的，按批次管理
  const inventoryData = [
    {
      id: "IG001-2024-001",
      productName: "光纤惯导组件A型",
      batchNumber: "2024-001",
      registeredLocation: "A区-01-01",
      actualLocation: "A区-01-01",
      inStock: true,
      locationMatch: true,
      status: "正常",
      lastCheck: "2024-01-15 14:30:00",
      checker: "张三"
    },
    {
      id: "IG002-2024-002",
      productName: "激光惯导组件B型", 
      batchNumber: "2024-002",
      registeredLocation: "A区-01-02",
      actualLocation: "A区-01-05",
      inStock: true,
      locationMatch: false,
      status: "位置不符",
      lastCheck: "2024-01-15 16:20:00",
      checker: "李四"
    },
    {
      id: "IG003-2024-003",
      productName: "MEMS惯导组件C型",
      batchNumber: "2024-003",
      registeredLocation: "A区-02-01",
      actualLocation: "",
      inStock: false,
      locationMatch: false,
      status: "未找到",
      lastCheck: "2024-01-15 10:15:00",
      checker: "王五"
    },
    {
      id: "IG004-2024-004",
      productName: "高精度惯导组件D型",
      batchNumber: "2024-004",
      registeredLocation: "A区-02-02",
      actualLocation: "A区-02-02",
      inStock: true,
      locationMatch: true,
      status: "正常",
      lastCheck: "2024-01-15 11:45:00",
      checker: "赵六"
    },
    {
      id: "IG005-2024-005",
      productName: "捷联惯导组件E型",
      batchNumber: "2024-005",
      registeredLocation: "A区-03-03",
      actualLocation: "A区-03-03",
      inStock: true,
      locationMatch: true,
      status: "正常",
      lastCheck: "2024-01-15 13:20:00",
      checker: "钱七"
    }
  ];

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">产品盘点</h1>
          <p className="text-muted-foreground">
            管理惯组数量核对，核对系统数量与实际数量的一致性
          </p>
        </div>
        <div className="flex gap-2">
          <Button variant="outline" className="flex items-center gap-2">
            <Download className="h-4 w-4" />
            导出盘点表
          </Button>
          <Button variant="outline" className="flex items-center gap-2">
            <Upload className="h-4 w-4" />
            导入盘点结果
          </Button>
          <Button className="flex items-center gap-2">
            <RefreshCw className="h-4 w-4" />
            开始盘点
          </Button>
        </div>
      </div>

      {/* 统计卡片 */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">总惯组数</p>
                <p className="text-2xl font-bold">553</p>
              </div>
              <div className="h-8 w-8 bg-blue-100 rounded-lg flex items-center justify-center">
                <Search className="h-4 w-4 text-blue-600" />
              </div>
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">已盘点</p>
                <p className="text-2xl font-bold text-green-600">300</p>
              </div>
              <div className="h-8 w-8 bg-green-100 rounded-lg flex items-center justify-center">
                <Search className="h-4 w-4 text-green-600" />
              </div>
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">位置不符</p>
                <p className="text-2xl font-bold text-orange-600">12</p>
              </div>
              <div className="h-8 w-8 bg-orange-100 rounded-lg flex items-center justify-center">
                <AlertTriangle className="h-4 w-4 text-orange-600" />
              </div>
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">未找到</p>
                <p className="text-2xl font-bold text-red-600">3</p>
              </div>
              <div className="h-8 w-8 bg-red-100 rounded-lg flex items-center justify-center">
                <AlertTriangle className="h-4 w-4 text-red-600" />
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      <div className="grid gap-6">
        {/* 搜索筛选 */}
        <Card>
          <CardHeader>
            <CardTitle>核对筛选</CardTitle>
            <CardDescription>
              根据条件筛选需要核对的惯组产品
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
                             <div className="space-y-2">
                 <Label htmlFor="productCode">惯组产品编码</Label>
                 <Input id="productCode" placeholder="请输入惯组产品编码" />
               </div>
              <div className="space-y-2">
                <Label htmlFor="location">存储位置</Label>
                <Select>
                  <SelectTrigger>
                    <SelectValue placeholder="请选择存储位置" />
                  </SelectTrigger>
                  <SelectContent>
                                                                  <SelectItem value="A">A区</SelectItem>
                         <SelectItem value="B">B区</SelectItem>
                         <SelectItem value="C">C区</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <div className="space-y-2">
                <Label htmlFor="status">核对状态</Label>
                <Select>
                  <SelectTrigger>
                    <SelectValue placeholder="请选择状态" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">全部</SelectItem>
                    <SelectItem value="normal">正常</SelectItem>
                    <SelectItem value="location-mismatch">位置不符</SelectItem>
                    <SelectItem value="not-found">未找到</SelectItem>
                    <SelectItem value="unchecked">未核对</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <div className="space-y-2">
                <Label>&nbsp;</Label>
                <Button className="w-full flex items-center gap-2">
                  <Search className="h-4 w-4" />
                  搜索
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* 核对结果表格 */}
        <Card>
          <CardHeader>
            <CardTitle>核对结果</CardTitle>
            <CardDescription>
              显示当前核对进度和结果，主要对产品是否在库、产品的存储位置是否与登记的信息一致进行统计
            </CardDescription>
          </CardHeader>
          <CardContent>
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>惯组产品编码</TableHead>
                  <TableHead>惯组产品名称</TableHead>
                  <TableHead>批次号</TableHead>
                  <TableHead>登记位置</TableHead>
                  <TableHead>实际位置</TableHead>
                  <TableHead>在库状态</TableHead>
                  <TableHead>位置匹配</TableHead>
                  <TableHead>状态</TableHead>
                  <TableHead>核对时间</TableHead>
                  <TableHead>核对人</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {inventoryData.map((item) => (
                  <TableRow key={item.id}>
                    <TableCell className="font-medium">{item.id}</TableCell>
                    <TableCell>{item.productName}</TableCell>
                    <TableCell>{item.batchNumber}</TableCell>
                    <TableCell>{item.registeredLocation}</TableCell>
                    <TableCell>{item.actualLocation || '-'}</TableCell>
                    <TableCell>
                      <Badge variant={item.inStock ? 'default' : 'destructive'}>
                        {item.inStock ? '在库' : '未找到'}
                      </Badge>
                    </TableCell>
                    <TableCell>
                      <Badge variant={item.locationMatch ? 'default' : 'destructive'}>
                        {item.locationMatch ? '匹配' : '不符'}
                      </Badge>
                    </TableCell>
                    <TableCell>
                      <Badge variant={
                        item.status === '正常' ? 'default' : 
                        item.status === '位置不符' ? 'destructive' : 
                        item.status === '未找到' ? 'secondary' : 'outline'
                      }>
                        {item.status}
                      </Badge>
                    </TableCell>
                    <TableCell>{item.lastCheck}</TableCell>
                    <TableCell>{item.checker}</TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </CardContent>
        </Card>
      </div>
    </div>
  );
};

export default ProductInventory; 