// 仓储位置API
export interface PositionData {
  file: string;
  positions: { [key: string]: number };
}

// 保存位置数据
export const savePositions = async (data: PositionData): Promise<boolean> => {
  try {
    const response = await fetch('/api/save-positions', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(data),
    });
    return response.ok;
  } catch (error) {
    console.error('Failed to save positions:', error);
    return false;
  }
};

// 加载位置数据
export const loadPositions = async (file: string): Promise<{ [key: string]: number } | null> => {
  try {
    const response = await fetch(`/api/load-positions?file=${file}`);
    if (response.ok) {
      return await response.json();
    }
    return null;
  } catch (error) {
    console.error('Failed to load positions:', error);
    return null;
  }
};

// 本地存储版本（作为备选方案）
export const savePositionsLocal = (data: PositionData): boolean => {
  try {
    localStorage.setItem(data.file, JSON.stringify(data.positions));
    return true;
  } catch (error) {
    console.error('Failed to save positions to localStorage:', error);
    return false;
  }
};

export const loadPositionsLocal = (file: string): { [key: string]: number } | null => {
  try {
    const data = localStorage.getItem(file);
    return data ? JSON.parse(data) : null;
  } catch (error) {
    console.error('Failed to load positions from localStorage:', error);
    return null;
  }
};
