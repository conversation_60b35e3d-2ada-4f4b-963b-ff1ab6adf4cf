# 惯组产品智能化存储及月稳测试系统 - 用户手册

## 目录

1. [系统登录](#1-系统登录)
2. [系统主界面](#2-系统主界面)
3. [工业大屏](#3-工业大屏)
4. [测试管理](#4-测试管理)
5. [设备管理](#5-设备管理)
6. [出入库管理](#6-出入库管理)
7. [系统管理](#7-系统管理)
8. [常见问题](#8-常见问题)

---

## 1. 系统登录

### 1.1 登录界面

1. 打开浏览器，访问系统地址：`http://[系统IP地址]`
2. 在登录界面输入用户名和密码
3. 点击"登录"按钮

![登录界面](../public/placeholder.svg)

### 1.2 首次登录

首次登录系统时，请使用管理员提供的初始账号和密码。出于安全考虑，系统会要求您修改初始密码。

1. 输入初始账号和密码
2. 按照提示设置新密码
3. 完成密码修改后重新登录

### 1.3 忘记密码

如果忘记密码，请按照以下步骤操作：

1. 在登录页面点击"忘记密码"
2. 输入您的用户名和注册邮箱
3. 系统会向您的邮箱发送重置密码链接
4. 按照邮件中的指引重置密码

---

## 2. 系统主界面

### 2.1 界面布局

系统主界面由以下几个部分组成：

- **顶部导航栏**：包含系统标题、快捷菜单和用户信息
- **左侧菜单栏**：包含系统所有功能模块的导航菜单
- **主内容区**：显示当前选中功能的内容
- **状态栏**：显示系统状态信息和通知

### 2.2 快捷菜单

顶部快捷菜单提供对常用功能的快速访问：

- **管理首页**：返回系统首页
- **测试管理**：快速进入测试控制中心
- **出入库管理**：快速进入出入库操作页面
- **排产管理**：快速进入测试计划管理
- **设备管理**：快速进入设备基准信息
- **系统管理**：快速进入系统设置

### 2.3 首页仪表板

首页仪表板提供系统关键指标的概览：

- **今日测试任务**：显示当日测试任务数量和完成情况
- **设备状态**：显示关键设备的运行状态
- **库存状态**：显示当前库存情况
- **告警信息**：显示最近的系统告警
- **任务进度**：显示正在执行的任务进度

---

## 3. 工业大屏

工业大屏模块提供仓库和设备的可视化监控界面，适合在大屏幕上展示。

### 3.1 访问工业大屏

1. 在左侧菜单中点击"工业大屏"
2. 系统将加载工业大屏界面

### 3.2 3D仓库可视化

3D仓库可视化展示了仓库的实时状态：

1. **视角控制**：
   - 鼠标左键拖动：旋转视角
   - 鼠标右键拖动：平移视角
   - 鼠标滚轮：缩放视角

2. **图层控制**：
   - 点击右上角的图层按钮可以控制显示/隐藏不同类型的设备和区域
   - 可选图层包括：货架、AGV、桁架、测试台、充电桩等

3. **设备状态**：
   - 绿色：正常运行
   - 黄色：警告状态
   - 红色：故障状态
   - 灰色：离线状态

### 3.3 实时数据展示

工业大屏右侧面板显示实时数据：

1. **设备运行状态**：显示各类设备的运行状态统计
2. **测试任务进度**：显示当前测试任务的执行进度
3. **环境数据**：显示仓库环境数据（温度、湿度等）
4. **告警信息**：显示最近的告警信息

---

## 4. 测试管理

测试管理模块包含测试控制中心、测试计划管理、测试监控和数据分析等功能。

### 4.1 测试控制中心

测试控制中心用于管理和控制测试任务：

1. **创建测试任务**：
   - 点击"新建测试任务"按钮
   - 选择测试类型
   - 选择测试产品
   - 设置测试参数
   - 点击"创建"按钮

2. **任务控制**：
   - 启动任务：点击任务卡片上的"启动"按钮
   - 暂停任务：点击任务卡片上的"暂停"按钮
   - 终止任务：点击任务卡片上的"终止"按钮
   - 查看详情：点击任务卡片上的"详情"按钮

3. **模式切换**：
   - 自动模式：系统自动执行测试流程
   - 手动模式：需要人工确认每个步骤

### 4.2 测试计划管理

测试计划管理用于排程和管理测试计划：

1. **创建测试计划**：
   - 点击"新建计划"按钮
   - 选择测试产品和数量
   - 设置计划开始时间
   - 设置优先级
   - 点击"创建"按钮

2. **计划管理**：
   - 编辑计划：点击计划列表中的"编辑"按钮
   - 删除计划：点击计划列表中的"删除"按钮
   - 调整优先级：拖动计划项调整顺序

3. **资源分配**：
   - 查看资源占用情况
   - 调整资源分配
   - 解决资源冲突

### 4.3 测试监控

测试监控页面用于实时监控测试任务的执行状态：

1. **查看任务列表**：
   - 页面显示当前所有执行中的任务
   - 可以按类型、状态等筛选任务

2. **查看任务详情**：
   - 点击任务卡片上的"查看详情"按钮
   - 查看任务的详细执行步骤和状态

3. **一键还原**：
   - 当任务出现故障时，点击"一键还原"按钮
   - 系统会自动执行故障恢复流程
   - 恢复完成后，任务将继续执行

4. **任务控制**：
   - 暂停任务：点击"暂停任务"按钮
   - 继续任务：点击"继续任务"按钮
   - 终止任务：点击"终止任务"按钮

### 4.4 数据分析查询

数据分析查询页面用于查询和分析历史测试数据：

1. **数据查询**：
   - 设置查询条件（时间范围、产品型号等）
   - 点击"查询"按钮
   - 查看查询结果

2. **数据分析**：
   - 查看测试数据统计图表
   - 导出数据报表
   - 自定义分析维度

3. **趋势分析**：
   - 查看测试参数的历史趋势
   - 设置告警阈值
   - 预测未来趋势

---

## 5. 设备管理

设备管理模块用于管理和监控系统中的各类设备。

### 5.1 设备基准信息

设备基准信息页面用于管理设备的基本信息：

1. **查看设备列表**：
   - 页面显示所有设备的列表
   - 可以按类型、状态等筛选设备

2. **设备详情**：
   - 点击设备列表中的设备名称查看详情
   - 查看设备的基本信息、技术参数、维护记录等

3. **添加设备**：
   - 点击"添加设备"按钮
   - 填写设备信息
   - 点击"保存"按钮

4. **编辑设备信息**：
   - 点击设备详情页的"编辑"按钮
   - 修改设备信息
   - 点击"保存"按钮

### 5.2 AGV充电管理

AGV充电管理页面用于管理AGV的充电策略和状态：

1. **查看AGV状态**：
   - 页面显示所有AGV的状态和电量
   - 可以查看AGV的当前位置和任务

2. **充电策略设置**：
   - 设置自动充电的电量阈值
   - 设置充电优先级
   - 设置充电时间段

3. **手动调度充电**：
   - 选择需要充电的AGV
   - 点击"调度充电"按钮
   - 系统会安排AGV前往充电桩

### 5.3 三色灯控制

三色灯控制页面用于管理仓库中的三色灯指示系统：

1. **查看三色灯状态**：
   - 页面显示所有三色灯控制板的状态
   - 可以查看每个灯的当前颜色

2. **控制板配置**：
   - 设置控制板的IP地址
   - 配置控制板的通信参数
   - 测试控制板连接

3. **货位映射**：
   - 配置三色灯与货位的映射关系
   - 设置不同状态对应的颜色
   - 保存配置

### 5.4 设备维护计划

设备维护计划页面用于管理设备的维护计划：

1. **查看维护计划**：
   - 页面显示所有设备的维护计划
   - 可以按时间、设备类型等筛选

2. **创建维护计划**：
   - 点击"新建维护计划"按钮
   - 选择设备和维护类型
   - 设置计划时间和周期
   - 点击"创建"按钮

3. **执行维护**：
   - 点击计划列表中的"执行"按钮
   - 记录维护内容和结果
   - 点击"完成"按钮

### 5.5 设备数据分析

设备数据分析页面用于分析设备的运行数据：

1. **查看设备运行数据**：
   - 选择设备和时间范围
   - 查看设备的运行参数图表

2. **故障分析**：
   - 查看设备故障历史
   - 分析故障原因和频率
   - 生成故障报告

3. **性能分析**：
   - 查看设备性能指标
   - 分析性能变化趋势
   - 预测设备寿命

---

## 6. 出入库管理

出入库管理模块用于管理产品的出入库操作和记录。

### 6.1 出入库操作

出入库操作页面用于执行产品的出入库操作：

1. **产品出库**：
   - 点击"产品出库"按钮
   - 扫描产品条码或输入产品ID
   - 选择出库原因（测试、维修等）
   - 点击"确认出库"按钮

2. **产品入库**：
   - 点击"产品入库"按钮
   - 扫描产品条码或输入产品ID
   - 选择入库类型（新品、测试完成等）
   - 选择存储位置
   - 点击"确认入库"按钮

3. **库位查询**：
   - 输入产品ID或库位编号
   - 点击"查询"按钮
   - 查看产品所在位置或库位状态

### 6.2 出入库记录

出入库记录页面用于查询历史出入库记录：

1. **记录查询**：
   - 设置查询条件（时间范围、产品ID等）
   - 点击"查询"按钮
   - 查看查询结果

2. **记录导出**：
   - 点击"导出"按钮
   - 选择导出格式（Excel、PDF等）
   - 下载导出文件

3. **统计分析**：
   - 查看出入库统计图表
   - 分析出入库趋势
   - 生成库存周转报表

---

## 7. 系统管理

系统管理模块用于管理系统的用户、角色、权限和设置。

### 7.1 用户管理

用户管理页面用于管理系统用户：

1. **查看用户列表**：
   - 页面显示所有用户的列表
   - 可以按部门、角色等筛选用户

2. **创建用户**：
   - 点击"新增用户"按钮
   - 填写用户信息（姓名、账号、部门等）
   - 选择用户角色
   - 设置初始密码
   - 点击"创建"按钮

3. **编辑用户**：
   - 点击用户列表中的"编辑"按钮
   - 修改用户信息
   - 点击"保存"按钮

4. **重置密码**：
   - 点击用户列表中的"重置密码"按钮
   - 确认重置操作
   - 系统会生成新的初始密码

### 7.2 角色管理

角色管理页面用于管理系统角色和权限：

1. **查看角色列表**：
   - 页面显示所有角色的列表
   - 可以查看每个角色的权限

2. **创建角色**：
   - 点击"新增角色"按钮
   - 填写角色名称和描述
   - 配置角色权限
   - 点击"创建"按钮

3. **编辑角色**：
   - 点击角色列表中的"编辑"按钮
   - 修改角色信息和权限
   - 点击"保存"按钮

### 7.3 系统设置

系统设置页面用于配置系统参数：

1. **基本设置**：
   - 系统名称
   - 公司名称
   - 管理员邮箱
   - 维护模式开关
   - 自动备份设置

2. **安全设置**：
   - 会话超时时间
   - 密码复杂度要求
   - 最大登录尝试次数
   - 双因素认证开关
   - 登录日志记录开关

3. **数据备份**：
   - 备份策略设置
   - 备份保留天数
   - 手动备份操作
   - 数据恢复操作

### 7.4 数字孪生

数字孪生页面用于管理系统的数字孪生模型：

1. **查看模型**：
   - 查看仓库和设备的3D模型
   - 查看模型与实际设备的数据映射

2. **配置模型**：
   - 更新模型参数
   - 配置数据源
   - 设置刷新频率

3. **模拟测试**：
   - 在虚拟环境中模拟测试流程
   - 验证系统行为
   - 优化流程参数

---

## 8. 常见问题

### 8.1 登录问题

**问题**：无法登录系统
**解决方案**：
1. 确认用户名和密码是否正确
2. 检查账号是否被锁定
3. 清除浏览器缓存后重试
4. 联系系统管理员重置密码

### 8.2 测试任务问题

**问题**：测试任务卡在某个步骤不动
**解决方案**：
1. 查看任务详情，确认当前步骤状态
2. 检查相关设备是否正常
3. 使用"一键还原"功能恢复任务
4. 如果问题持续，联系技术支持

### 8.3 设备连接问题

**问题**：某设备显示离线状态
**解决方案**：
1. 检查设备电源是否正常
2. 检查网络连接是否正常
3. 重启设备
4. 检查设备IP配置是否正确
5. 联系设备维护人员

### 8.4 系统性能问题

**问题**：系统响应缓慢
**解决方案**：
1. 检查网络连接是否稳定
2. 清除浏览器缓存
3. 关闭不必要的应用程序
4. 使用推荐的浏览器和版本
5. 联系系统管理员检查服务器状态

---

## 联系支持

如需技术支持，请联系：

- **技术支持邮箱**：<EMAIL>
- **技术支持电话**：400-123-4567
- **工作时间**：周一至周五 9:00-18:00 