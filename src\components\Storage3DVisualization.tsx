import { useRef, useMemo, useState, useEffect } from "react";
import { <PERSON><PERSON>, use<PERSON>rame, useThree } from "@react-three/fiber";
import { OrbitControls, Text, Html } from "@react-three/drei";
import * as THREE from "three";

interface StorageItem {
  id: string;
  shelf: number;
  level: number; // 1, 2, 3
  position: number; // 1-7
  status: "empty" | "stored" | "testing";
  productId?: string;
}

interface Storage3DVisualizationProps {
  storageData?: StorageItem[];
  selectedArea?: string;
  className?: string;
}

// 生成模拟数据
const generateMockStorageData = (area: string): StorageItem[] => {
  const data: StorageItem[] = [];
  
  // 计算需要的货架数量：200个货位 ÷ (3层 × 7个货位) = 9.52，向上取整为10个货架
  // 10个货架 × 3层 × 7个货位 = 210个货位，满足200个货位的要求
  const totalShelves = 10;
  
  for (let shelf = 1; shelf <= totalShelves; shelf++) {
    for (let level = 1; level <= 3; level++) {
      for (let pos = 1; pos <= 7; pos++) {
        const status = Math.random() > 0.3 ? "empty" : 
                      Math.random() > 0.5 ? "stored" : "testing";
        
        data.push({
          id: `${area}-${shelf}-${level}-${pos}`,
          shelf,
          level,
          position: pos,
          status,
          productId: status !== "empty" ? `PROD${Math.floor(Math.random() * 10000).toString().padStart(4, '0')}` : undefined
        });
      }
    }
  }
  
  return data;
};

// 单个货位组件
function StorageSlot({
  position,
  status,
  productId,
  onSlotClick,
  slotInfo,
  isSelected,
  onSlotSelect,
  onContextMenu
}: {
  position: [number, number, number];
  status: "empty" | "stored" | "testing";
  productId?: string;
  onSlotClick?: () => void;
  slotInfo?: { shelf: number; level: number; position: number };
  isSelected?: boolean;
  onSlotSelect?: () => void;
  onContextMenu?: (event: any, item: any) => void;
}) {
  const meshRef = useRef<THREE.Mesh>(null);
  const [hovered, setHovered] = useState(false);

  // 根据状态设置颜色
  const getColor = () => {
    switch (status) {
      case "empty":
        return "#e5e7eb"; // 灰色
      case "stored":
        return "#10b981"; // 绿色
      case "testing":
        return "#f59e0b"; // 橙色
      default:
        return "#e5e7eb";
    }
  };

  // 悬停效果
  useFrame(() => {
    if (meshRef.current) {
      if (hovered) {
        meshRef.current.scale.setScalar(1.1);
      } else {
        meshRef.current.scale.setScalar(1.0);
      }
    }
  });

  return (
    <group position={position}>
      {/* 选中高亮边框 */}
      {isSelected && (
        <mesh position={[0, 0, 0]}>
          <boxGeometry args={[0.9, 0.9, 1.3]} />
          <meshStandardMaterial 
            color="#3B82F6" 
            transparent
            opacity={0.6}
            wireframe={true}
          />
        </mesh>
      )}
      
             <mesh
         ref={meshRef}
         onClick={() => {
           onSlotSelect?.();
           onSlotClick?.();
         }}
         onPointerOver={() => setHovered(true)}
         onPointerOut={() => setHovered(false)}
         onPointerDown={(event) => {
           console.log("PointerDown事件:", event.button, slotInfo); // 调试信息
           // 检测右键点击
           if (event.button === 2) { // 右键
             event.stopPropagation();
             console.log("右键点击检测到:", slotInfo); // 调试信息
             if (slotInfo) {
               const item = {
                 ...slotInfo,
                 status,
                 productId
               };
               console.log("准备调用onContextMenu:", item); // 调试信息
               // 直接设置菜单状态进行测试
               if (onContextMenu) {
                 onContextMenu(event, item);
               } else {
                 console.log("onContextMenu函数未定义");
               }
             }
           }
         }}
       >
         <boxGeometry args={[0.8, 0.8, 1.2]} />
         <meshStandardMaterial 
           color={getColor()} 
           transparent
           opacity={status === "empty" ? 0.3 : 0.8}
         />
       </mesh>


      
             {productId && (
         <Text
           position={[0, 0, 0.7]}
           fontSize={0.08}
           color="#000000"
           anchorX="center"
           anchorY="middle"
         >
           {productId.substring(0, 4)}
         </Text>
       )}

      {/* 气泡提示 */}
      {hovered && slotInfo && (
        <Html position={[0, 1.2, 0]} center>
          <div className="bg-black text-white text-xs px-2 py-1 rounded shadow-lg whitespace-nowrap">
            <div>货架{slotInfo.shelf}-{slotInfo.level}层-{slotInfo.position}号</div>
            {productId && <div>产品: {productId}</div>}
            <div>状态: {status === "empty" ? "空货位" : status === "stored" ? "已存储" : "测试中"}</div>
          </div>
        </Html>
      )}
    </group>
  );
}

// 单个货架组件
function Shelf({
  shelfNumber,
  position,
  storageData,
  onSlotClick,
  selectedSlot,
  onSlotSelect,
  onContextMenu
}: {
  shelfNumber: number;
  position: [number, number, number];
  storageData: StorageItem[];
  onSlotClick?: (item: StorageItem) => void;
  selectedSlot?: { shelf: number; level: number; position: number };
  onSlotSelect?: (slot: { shelf: number; level: number; position: number }) => void;
  onContextMenu?: (event: any, item: any) => void;
}) {
  const shelfRef = useRef<THREE.Group>(null);
  const [lightStates, setLightStates] = useState<{[key: string]: boolean}>({});

  // 获取当前货架的数据
  const shelfData = storageData.filter(item => item.shelf === shelfNumber);

  // 指示灯闪烁逻辑
  useFrame((state) => {
    const time = state.clock.getElapsedTime();
    const shouldBeOn = Math.floor(time) % 2 === 0; // 每秒切换一次
    
    const newLightStates: {[key: string]: boolean} = {};
    shelfData.forEach(item => {
      if (item.status === "testing") {
        const key = `${item.level}-${item.position}`;
        newLightStates[key] = shouldBeOn;
      }
    });
    
    setLightStates(newLightStates);
  });

  return (
    <group ref={shelfRef} position={position}>
      {/* 货架主体结构 - 天蓝色 */}
     

      {/* 立柱 - 每五个货位一个立柱，支撑整个货架结构 */}
      {/* 前立柱 - 通到三层上面 */}
      {[-1.5, 1.5].map((xPos, pillarIndex) => (
        <mesh key={`front-pillar-${pillarIndex}`} position={[xPos, 2.1, 1.2]}>
          <boxGeometry args={[0.1, 4, 0.1]} />
          <meshStandardMaterial 
            color="#87CEEB" 
            metalness={0.5}
            roughness={0.1}
            emissive="#4A90E2"
            emissiveIntensity={0.2}
          />
        </mesh>
      ))}
      
      {/* 后立柱 - 通到三层上面 */}
      {[-1.5, 1.5].map((xPos, pillarIndex) => (
        <mesh key={`back-pillar-${pillarIndex}`} position={[xPos, 2.1, -1.2]}>
          <boxGeometry args={[0.1, 4, 0.1]} />
          <meshStandardMaterial 
            color="#87CEEB" 
            metalness={0.5}
            roughness={0.1}
            emissive="#4A90E2"
            emissiveIntensity={0.2}
          />
        </mesh>
      ))}

      {/* 三层货位 */}
      {[1, 2, 3].map((level) => (
        <group key={level} position={[0, (level-0.7) * 1.3, 0]}>
          {/* 层板 - 银白色铝合金 */}
          <mesh position={[0, 0, 0]}>
            <boxGeometry args={[8, 0.1, 3]} />
            <meshStandardMaterial 
              color="#a0abc0" 
              metalness={0.85}
              roughness={0.15}
              emissive="#a0abcF"
              emissiveIntensity={0.2}
            />
          </mesh>
          
          {/* 左右挡板 */}
          {/* 左挡板 */}
          <mesh position={[-4.05, 0, 0]}>
            <boxGeometry args={[0.1, 0.1, 3]} />
            <meshStandardMaterial 
              color="#a0abc0" 
              metalness={0.85}
              roughness={0.15}
              emissive="#a0abcF"
              emissiveIntensity={0.2}
            />
          </mesh>
          
          {/* 右挡板 */}
          <mesh position={[4.05, 0, 0]}>
            <boxGeometry args={[0.1, 0.1, 3]} />
            <meshStandardMaterial 
              color="#a0abc0" 
              metalness={0.85}
              roughness={0.15}
              emissive="#a0abcF"
              emissiveIntensity={0.2}
            />
          </mesh>
          
          {/* 7个货位 */}
          {Array.from({ length: 7 }, (_, pos) => {
            const slotPosition: [number, number, number] = [
              -3 + pos * 1.0, // 水平分布，间距1.0
              0.5, // 在层板上方
              0, // 居中
            ];

            const storageItem = shelfData.find(
              item => item.level === level && item.position === pos + 1
            );

                         const currentSlot = { shelf: shelfNumber, level: level, position: pos + 1 };
             const isSelected = selectedSlot && 
               selectedSlot.shelf === currentSlot.shelf && 
               selectedSlot.level === currentSlot.level && 
               selectedSlot.position === currentSlot.position;
             
             return (
               <StorageSlot
                 key={`${shelfNumber}-${level}-${pos + 1}`}
                 position={slotPosition}
                 status={storageItem?.status || "empty"}
                 productId={storageItem?.productId}
                 onSlotClick={() => storageItem && onSlotClick?.(storageItem)}
                 slotInfo={currentSlot}
                 isSelected={isSelected}
                 onSlotSelect={() => onSlotSelect?.(currentSlot)}
                 onContextMenu={onContextMenu}
               />
             );
          })}
        </group>
      ))}

             {/* 货架前端指示灯 */}
       {shelfData.filter(item => item.status === "testing").map((item) => {
         const key = `${item.level}-${item.position}`;
         const isLightOn = lightStates[key] || false;
         const slotX = -3 + (item.position - 1) * 1.0; // 与货位位置对应
         const slotY = (item.level - 0.7) * 1.3; // 与货位层数对应
         
         return (
           <mesh 
             key={`light-${key}`} 
             position={[slotX, slotY, 1.55]} // 镶嵌在货架前端
             rotation={[0, 0, 0]} // 立起来
           >
             <cylinderGeometry args={[0.03, 0.03, 0.08, 8]} />
             <meshStandardMaterial 
               color={isLightOn ? "#ff0000" : "#330000"}
               emissive={isLightOn ? "#ff0000" : "#000000"}
               emissiveIntensity={isLightOn ? 0.8 : 0}
               transparent
               opacity={isLightOn ? 1 : 0.3}
             />
           </mesh>
         );
       })}

       {/* 货架编号 */}
       <Text
         position={[0, 4.2, 0]}
         fontSize={0.3}
         color="#1e40af"
         anchorX="center"
         anchorY="middle"
       >
         货架{shelfNumber}
       </Text>
    </group>
  );
}

// 自定义相机控制器组件
function CameraController() {
  const { camera } = useThree();
  const [keys, setKeys] = useState<{ [key: string]: boolean }>({});
  const moveSpeed = 0.5;

  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      setKeys(prev => ({ ...prev, [event.key.toLowerCase()]: true }));
    };

    const handleKeyUp = (event: KeyboardEvent) => {
      setKeys(prev => ({ ...prev, [event.key.toLowerCase()]: false }));
    };

    document.addEventListener('keydown', handleKeyDown);
    document.addEventListener('keyup', handleKeyUp);

    return () => {
      document.removeEventListener('keydown', handleKeyDown);
      document.removeEventListener('keyup', handleKeyUp);
    };
  }, []);

  useFrame(() => {
    const direction = new THREE.Vector3();
    
    if (keys['w']) {
      direction.z -= 1; // 向前
    }
    if (keys['s']) {
      direction.z += 1; // 向后
    }
    if (keys['a']) {
      direction.x -= 1; // 向左
    }
    if (keys['d']) {
      direction.x += 1; // 向右
    }
    if (keys['q']) {
      direction.y += 1; // 向上
    }
    if (keys['e']) {
      direction.y -= 1; // 向下
    }

    if (direction.length() > 0) {
      direction.normalize();
      direction.multiplyScalar(moveSpeed);
      
      // 将方向向量转换到相机坐标系
      direction.applyQuaternion(camera.quaternion);
      
      camera.position.add(direction);
    }
  });

  return null;
}

// 3D场景组件
function StorageScene({ 
  storageData, 
  onSlotClick,
  selectedSlot,
  onSlotSelect,
  onContextMenu
}: { 
  storageData: StorageItem[];
  onSlotClick?: (item: StorageItem) => void;
  selectedSlot?: { shelf: number; level: number; position: number };
  onSlotSelect?: (slot: { shelf: number; level: number; position: number }) => void;
  onContextMenu?: (event: any, item: any) => void;
}) {
  const sceneRef = useRef<THREE.Group>(null);

  // 计算货架位置 - 10个货架，每行5个，共2行
  const shelfPositions: [number, number, number][] = [
    // 第一行货架
    [-12, 0, 0], // 货架1
    [-6, 0, 0],  // 货架2
    [0, 0, 0],   // 货架3
    [6, 0, 0],   // 货架4
    [12, 0, 0],  // 货架5
    // 第二行货架 - 增加距离到-12
    [-12, 0, -12], // 货架6
    [-6, 0, -12],  // 货架7
    [0, 0, -12],   // 货架8
    [6, 0, -12],   // 货架9
    [12, 0, -12],  // 货架10
  ];

  return (
    <group ref={sceneRef}>
      {/* 地面 */}
      <mesh position={[0, -0.5, 0]} rotation={[-Math.PI / 2, 0, 0]}>
        <planeGeometry args={[50, 50]} />
        <meshStandardMaterial color="#f3f4f6" />
      </mesh>

             {/* 10个货架 */}
       {shelfPositions.map((position, index) => (
         <Shelf
           key={index + 1}
           shelfNumber={index + 1}
           position={position}
           storageData={storageData}
           onSlotClick={onSlotClick}
           selectedSlot={selectedSlot}
           onSlotSelect={onSlotSelect}
           onContextMenu={onContextMenu}
         />
       ))}

      {/* 环境光 */}
      <ambientLight intensity={0.4} />
      
      {/* 主方向光 */}
      <directionalLight
        position={[10, 15, 10]}
        intensity={1.2}
        castShadow
        shadow-mapSize-width={2048}
        shadow-mapSize-height={2048}
      />
      
      {/* 补光 */}
      <directionalLight
        position={[-10, 10, -5]}
        intensity={0.7}
        castShadow
      />
      
      {/* 顶部光 */}
      <directionalLight
        position={[0, 20, 0]}
        intensity={0.8}
        castShadow
      />
    </group>
  );
}

// 主组件
export function Storage3DVisualization({
  storageData = [],
  selectedArea = "A",
  className = "",
}: Storage3DVisualizationProps) {
  const [data, setData] = useState<StorageItem[]>(() => 
    storageData.length > 0 ? storageData : generateMockStorageData(selectedArea)
  );
  const [selectedSlot, setSelectedSlot] = useState<{ shelf: number; level: number; position: number } | null>(null);
  const [contextMenu, setContextMenu] = useState<{ visible: boolean; x: number; y: number; item: any } | null>(null);
  const [isAutoMode, setIsAutoMode] = useState(true);
  const [cameraPosition, setCameraPosition] = useState<[number, number, number]>([0, 10, 15]);
  const [cameraTarget, setCameraTarget] = useState<[number, number, number]>([0, 2, 0]);

  const handleSlotClick = (item: StorageItem) => {
    console.log("点击货位:", item);
    // 这里可以添加点击处理逻辑
  };

  const handleSlotSelect = (slot: { shelf: number; level: number; position: number }) => {
    setSelectedSlot(slot);
  };

  // 获取选中货位的产品信息
  const getSelectedItem = () => {
    if (!selectedSlot) return null;
    return data.find(item => 
      item.shelf === selectedSlot.shelf && 
      item.level === selectedSlot.level && 
      item.position === selectedSlot.position
    );
  };

  const handleContextMenu = (event: any, item: any) => {
    event.preventDefault();
    console.log("右键菜单触发:", item); // 调试信息
    console.log("事件坐标:", event.clientX, event.clientY); // 调试信息
    setContextMenu({
      visible: true,
      x: event.clientX || 100,
      y: event.clientY || 100,
      item
    });
  };

  const handleContextMenuAction = (action: string) => {
    if (!contextMenu) return;
    
    const { item } = contextMenu;
    let message = "";
    
    switch (action) {
      case "outbound_test":
        message = `已经安排产品 ${item.productId} 进行出库测试`;
        break;
      case "outbound":
        message = `开始出厂 - 产品 ${item.productId}`;
        break;
      case "end_test":
        message = `产品 ${item.productId} 正在结束测试，请稍后`;
        break;
      default:
        message = "操作完成";
    }
    
    alert(message);
    setContextMenu(null);
  };

  const handleModeToggle = () => {
    setIsAutoMode(!isAutoMode);
  };

  const handleOutboundTest = () => {
    if (selectedSlot) {
      const selectedItem = getSelectedItem();
      
      if (selectedItem && selectedItem.productId) {
        alert(`存储于货架${selectedSlot.shelf}-${selectedSlot.level}层-${selectedSlot.position}号货位上的编号为${selectedItem.productId}的产品开始启动出库测试`);
      } else {
        alert(`货架${selectedSlot.shelf}-${selectedSlot.level}层-${selectedSlot.position}号货位为空，无法进行出库测试`);
      }
    } else {
      alert("请先选择一个货位");
    }
  };

  const handleEndTest = () => {
    if (selectedSlot) {
      const selectedItem = getSelectedItem();
      
      if (selectedItem && selectedItem.productId) {
        alert(`正在结束存储于货架${selectedSlot.shelf}-${selectedSlot.level}层-${selectedSlot.position}号货位上的编号为${selectedItem.productId}的产品的测试`);
      } else {
        alert(`货架${selectedSlot.shelf}-${selectedSlot.level}层-${selectedSlot.position}号货位为空，无法结束测试`);
      }
    } else {
      alert("请先选择一个货位");
    }
  };

  const handleProductOutbound = () => {
    if (selectedSlot) {
      const selectedItem = getSelectedItem();
      
      if (selectedItem && selectedItem.productId) {
        alert(`存储于货架${selectedSlot.shelf}-${selectedSlot.level}层-${selectedSlot.position}号货位上的编号为${selectedItem.productId}的产品开始出厂`);
      } else {
        alert(`货架${selectedSlot.shelf}-${selectedSlot.level}层-${selectedSlot.position}号货位为空，无法进行出厂操作`);
      }
    } else {
      alert("请先选择一个货位");
    }
  };

  const handleProductRegister = () => {
    if (selectedSlot) {
      const selectedItem = getSelectedItem();
      
      if (selectedItem && selectedItem.productId) {
        alert(`产品登记 - 存储于货架${selectedSlot.shelf}-${selectedSlot.level}层-${selectedSlot.position}号货位上的编号为${selectedItem.productId}的产品`);
      } else {
        alert(`货架${selectedSlot.shelf}-${selectedSlot.level}层-${selectedSlot.position}号货位为空，开始进行产品登记`);
      }
    } else {
      alert("请先选择一个货位");
    }
  };

  // 相机位置控制函数
  const handleCameraToFirstRow = () => {
    setCameraPosition([0, 10, 15]);
    setCameraTarget([0, 2, 0]);
  };

  const handleCameraToSecondRow = () => {
    setCameraPosition([0, 10, -3]);
    setCameraTarget([0, 2, -12]);
  };

  const handleCameraToOverview = () => {
    setCameraPosition([0, 15, 20]);
    setCameraTarget([0, 2, -6]);
  };

  const handleClickOutside = () => {
    setContextMenu(null);
  };

  // 添加全局右键事件监听器
  useEffect(() => {
    const handleGlobalContextMenu = (event: MouseEvent) => {
      console.log("全局右键事件:", event.target);
      // 这里可以添加全局右键菜单逻辑
    };

    document.addEventListener('contextmenu', handleGlobalContextMenu);
    
    return () => {
      document.removeEventListener('contextmenu', handleGlobalContextMenu);
    };
  }, []);



  const stats = useMemo(() => {
    const total = data.length;
    const empty = data.filter(item => item.status === "empty").length;
    const stored = data.filter(item => item.status === "stored").length;
    const testing = data.filter(item => item.status === "testing").length;
    
    return { total, empty, stored, testing };
  }, [data]);

  return (
    <div 
      className={`w-full h-full ${className}`} 
      onClick={handleClickOutside}
      onContextMenu={(e) => e.preventDefault()}
    >
      <div className="space-y-4">
                 {/* 控制面板 */}
         <div className="flex items-center justify-between p-4 bg-white rounded-lg shadow-sm">
           <div className="flex items-center space-x-4">
             <h3 className="text-lg font-semibold text-gray-800">请选择区域</h3>
             <select 
               value={selectedArea} 
               onChange={(e) => {
                 const newArea = e.target.value;
                 setData(generateMockStorageData(newArea));
               }}
               className="px-3 py-1 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
             >
               <option value="A">A区域</option>
               <option value="B">B区域</option>
               <option value="C">C区域</option>
             </select>
             <div className="text-sm text-gray-600">
               共 {stats.total} 个货位
             </div>
                          {selectedSlot && (
                <div className="text-sm text-blue-600 font-medium">
                  已选中: 货架{selectedSlot.shelf}-{selectedSlot.level}层-{selectedSlot.position}号
                  {(() => {
                    const selectedItem = getSelectedItem();
                    if (selectedItem && selectedItem.productId) {
                      return ` - 产品: ${selectedItem.productId}`;
                    } else {
                      return ' - 空货位';
                    }
                  })()}
                </div>
              )}
           </div>
          
          <div className="flex items-center space-x-6 text-sm">
            <div className="flex items-center space-x-2">
              <div className="w-4 h-4 bg-gray-300 rounded"></div>
              <span>空货位 ({stats.empty})</span>
            </div>
            <div className="flex items-center space-x-2">
              <div className="w-4 h-4 bg-green-500 rounded"></div>
              <span>已存储 ({stats.stored})</span>
            </div>
            <div className="flex items-center space-x-2">
              <div className="w-4 h-4 bg-orange-500 rounded"></div>
              <span>测试中 ({stats.testing})</span>
            </div>
          </div>
        </div>

        {/* 相机位置控制 */}
        <div className="flex items-center justify-center space-x-3 p-3 bg-white rounded-lg shadow-sm">
          <span className="text-sm font-medium text-gray-700">视角控制:</span>
          <button
            onClick={handleCameraToFirstRow}
            className="px-3 py-1 bg-blue-500 hover:bg-blue-600 text-white rounded text-sm font-medium transition-all duration-200"
          >
            第一排货架
          </button>
          <button
            onClick={handleCameraToSecondRow}
            className="px-3 py-1 bg-green-500 hover:bg-green-600 text-white rounded text-sm font-medium transition-all duration-200"
          >
            第二排货架
          </button>
          <button
            onClick={handleCameraToOverview}
            className="px-3 py-1 bg-purple-500 hover:bg-purple-600 text-white rounded text-sm font-medium transition-all duration-200"
          >
            全景视角
          </button>
        </div>

        {/* 3D画布 */}
        <div 
          className="w-full h-96 bg-gradient-to-br from-blue-50 to-indigo-100 rounded-lg overflow-hidden relative"
          onContextMenu={(e) => e.preventDefault()}
        >
                     <Canvas
             camera={{ 
               position: cameraPosition, 
               fov: 45,
               near: 0.1,
               far: 1000
             }}
             shadows
             onContextMenu={(e) => {
               e.preventDefault();
               console.log("Canvas右键事件被阻止");
             }}
           >
                         <StorageScene 
               storageData={data} 
               onSlotClick={handleSlotClick}
               selectedSlot={selectedSlot}
               onSlotSelect={handleSlotSelect}
               onContextMenu={handleContextMenu}
             />
             
             {/* 自定义相机控制器 */}
             <CameraController />
             
                           {/* 控制器 */}
              <OrbitControls
                enablePan={true}
                enableZoom={true}
                enableRotate={true}
                maxPolarAngle={Math.PI / 2}
                minPolarAngle={0}
                maxDistance={50}
                minDistance={2} // 允许更近距离观察
                target={cameraTarget}
                defaultPolarAngle={Math.PI / 2 - Math.PI / 9} // 20度仰角 (90度 - 20度 = 70度)
                enableDamping={true}
                dampingFactor={0.05}
              />
          </Canvas>

          {/* 右键菜单 */}
          {contextMenu && (
            <div 
              className="absolute bg-white border border-gray-200 rounded-lg shadow-lg py-2 z-50"
              style={{ 
                left: contextMenu.x, 
                top: contextMenu.y,
                minWidth: '150px'
              }}
              onClick={(e) => e.stopPropagation()}
              onContextMenu={(e) => e.preventDefault()}
            >
              <div className="text-xs text-gray-500 px-2 py-1 border-b">
                货架{contextMenu.item.shelf}-{contextMenu.item.level}层-{contextMenu.item.position}号
              </div>
              {contextMenu.item.status === "stored" && (
                <>
                  <button
                    className="w-full text-left px-4 py-2 hover:bg-gray-100 text-sm"
                    onClick={() => handleContextMenuAction("outbound_test")}
                  >
                    出库测试
                  </button>
                  <button
                    className="w-full text-left px-4 py-2 hover:bg-gray-100 text-sm"
                    onClick={() => handleContextMenuAction("outbound")}
                  >
                    出厂
                  </button>
                </>
              )}
              {contextMenu.item.status === "testing" && (
                <button
                  className="w-full text-left px-4 py-2 hover:bg-gray-100 text-sm"
                  onClick={() => handleContextMenuAction("end_test")}
                >
                  结束测试
                </button>
              )}
            </div>
                     )}
         </div>

         {/* 控制按钮区域 */}
         <div className="bg-white p-4 rounded-lg shadow-sm">
           <div className="flex items-center justify-between mb-4">
             <h4 className="text-md font-semibold text-gray-800">操作控制</h4>
             <div className="flex items-center space-x-2">
               <span className="text-sm text-gray-600">当前模式:</span>
               <span className={`text-sm font-medium px-2 py-1 rounded ${
                 isAutoMode 
                   ? 'bg-green-100 text-green-800' 
                   : 'bg-blue-100 text-blue-800'
               }`}>
                 {isAutoMode ? '自动模式' : '手动模式'}
               </span>
             </div>
           </div>
           
           <div className="grid grid-cols-2 md:grid-cols-5 gap-3">
             {/* 模式切换按钮 */}
             <button
               onClick={handleModeToggle}
               className={`px-4 py-2 rounded-lg text-sm font-medium transition-all duration-200 ${
                 isAutoMode
                   ? 'bg-green-500 hover:bg-green-600 text-white'
                   : 'bg-blue-500 hover:bg-blue-600 text-white'
               }`}
             >
               {isAutoMode ? '切换到手动' : '切换到自动'}
             </button>

             {/* 出库测试按钮 */}
             <button
               onClick={handleOutboundTest}
               className="px-4 py-2 bg-orange-500 hover:bg-orange-600 text-white rounded-lg text-sm font-medium transition-all duration-200"
             >
               出库测试
             </button>

             {/* 结束测试按钮 */}
             <button
               onClick={handleEndTest}
               className="px-4 py-2 bg-red-500 hover:bg-red-600 text-white rounded-lg text-sm font-medium transition-all duration-200"
             >
               结束测试
             </button>

             {/* 产品出厂按钮 */}
             <button
               onClick={handleProductOutbound}
               className="px-4 py-2 bg-purple-500 hover:bg-purple-600 text-white rounded-lg text-sm font-medium transition-all duration-200"
             >
               产品出厂
             </button>

             {/* 产品登记按钮 */}
             <button
               onClick={handleProductRegister}
               className="px-4 py-2 bg-indigo-500 hover:bg-indigo-600 text-white rounded-lg text-sm font-medium transition-all duration-200"
             >
               产品登记
             </button>
           </div>

           {/* 操作提示 */}
           <div className="mt-3 text-xs text-gray-500">
             <p>• 点击货位选中后，可使用上方按钮进行操作</p>
             <p>• 自动模式：系统自动管理货位状态 | 手动模式：需要手动确认操作</p>
           </div>
         </div>

                   {/* 说明文字 */}
          <div className="text-sm text-gray-600 bg-white p-3 rounded-lg">
            <p>• 每个区域包含10个货架，每个货架3层，每层7个货位，共210个货位</p>
            <p>• A、B、C三个区域，每个区域200个货位，总共600个货位</p>
            <p>• 灰色立方体：空货位 | 绿色立方体：已存储惯组 | 橙色立方体：测试中惯组</p>
            <p>• 点击货位可选中并查看详细信息</p>
            <p>• 右键点击有产品的货位可进行出库测试、出厂等操作</p>
            <p>• <strong>视角控制：</strong>使用上方按钮快速定位到第一排货架、第二排货架或全景视角</p>
            <p>• <strong>漫游控制：</strong>W(前进) S(后退) A(左移) D(右移) Q(上升) E(下降) | 鼠标拖拽旋转视角 | 滚轮缩放</p>
          </div>
      </div>
    </div>
  );
} 