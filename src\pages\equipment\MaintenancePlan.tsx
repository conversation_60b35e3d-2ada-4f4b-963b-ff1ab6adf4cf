import { useState, useEffect } from "react";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import { Progress } from "@/components/ui/progress";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { StatusChart } from "@/components/StatusChart";
import { Calendar } from "@/components/ui/calendar";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import {
  Wrench,
  Calendar as CalendarIcon,
  Clock,
  User,
  Alert<PERSON>riangle,
  CheckCircle,
  Settings,
  FileText,
  DollarSign,
  TrendingUp,
  Bot,
  Truck,
  TestTube2,
  Plus,
  Edit,
  Trash2,
  Eye,
  Download,
} from "lucide-react";

interface MaintenancePlan {
  id: string;
  equipmentId: string;
  equipmentName: string;
  equipmentType: "truss" | "amr" | "test" | "other";
  maintenanceType: "preventive" | "corrective" | "emergency" | "upgrade";
  priority: "low" | "medium" | "high" | "critical";
  scheduledDate: string;
  estimatedDuration: number;
  assignedTechnician: string;
  description: string;
  status: "scheduled" | "in_progress" | "completed" | "cancelled" | "overdue";
  actualDuration?: number;
  cost?: number;
  notes?: string;
  nextScheduledDate?: string;
}

interface MaintenanceHistory {
  id: string;
  equipmentId: string;
  date: string;
  type: string;
  cost: number;
  duration: number;
  technician: string;
  result: "success" | "partial" | "failed";
}

export default function MaintenancePlan() {
  const [selectedEquipmentType, setSelectedEquipmentType] = useState("all");
  const [selectedStatus, setSelectedStatus] = useState("all");
  const [selectedPlan, setSelectedPlan] = useState<MaintenancePlan | null>(
    null,
  );
  const [isNewPlanDialogOpen, setIsNewPlanDialogOpen] = useState(false);
  const [selectedDate, setSelectedDate] = useState<Date | undefined>(
    new Date(),
  );

  // 年度维护计划数据
  const [maintenancePlans] = useState<MaintenancePlan[]>([
    {
      id: "MP-2024-001",
      equipmentId: "TRUSS-01",
      equipmentName: "桁架系统-01",
      equipmentType: "truss",
      maintenanceType: "preventive",
      priority: "high",
      scheduledDate: "2024-01-15",
      estimatedDuration: 4,
      assignedTechnician: "张工程师",
      description: "季度保养：润滑系统检查、传动机构保养、安全系统测试",
      status: "scheduled",
      cost: 8500,
      nextScheduledDate: "2024-04-15",
    },
    {
      id: "MP-2024-002",
          equipmentId: "AMR-01",
    equipmentName: "AMR运输车-01",
    equipmentType: "amr",
      maintenanceType: "preventive",
      priority: "medium",
      scheduledDate: "2024-01-12",
      estimatedDuration: 2,
      assignedTechnician: "李技师",
      description: "月度保养：电池检测、导航系统校准、轮胎检查",
      status: "completed",
      actualDuration: 2.5,
      cost: 3200,
      nextScheduledDate: "2024-02-12",
    },
    {
      id: "MP-2024-003",
      equipmentId: "TEST-01",
      equipmentName: "测试台-01",
      equipmentType: "test",
      maintenanceType: "corrective",
      priority: "critical",
      scheduledDate: "2024-01-08",
      estimatedDuration: 6,
      assignedTechnician: "王技师",
      description: "故障维修：温控系统异常，需要更换温度传感器和控制模块",
      status: "in_progress",
      cost: 15000,
      notes: "已订购配件，预计明日到货",
    },
    {
      id: "MP-2024-004",
          equipmentId: "AMR-03",
    equipmentName: "AMR运输车-03",
    equipmentType: "amr",
      maintenanceType: "upgrade",
      priority: "low",
      scheduledDate: "2024-01-20",
      estimatedDuration: 8,
      assignedTechnician: "赵工程师",
      description: "系统升级：更新导航软件，安装新的安全传感器",
      status: "scheduled",
      cost: 12000,
      nextScheduledDate: "2024-07-20",
    },
    {
      id: "MP-2024-006",
      equipmentId: "TEST-03",
      equipmentName: "测试台-03",
      equipmentType: "test",
      maintenanceType: "emergency",
      priority: "critical",
      scheduledDate: "2024-01-10",
      estimatedDuration: 3,
      assignedTechnician: "周技师",
      description: "紧急维修：设备突然停机，疑似电路板故障",
      status: "completed",
      actualDuration: 4,
      cost: 9500,
      nextScheduledDate: "2024-03-10",
    },
  ]);

  // 维护历史数据
  const [maintenanceHistory] = useState<MaintenanceHistory[]>([
    {
      id: "MH-001",
      equipmentId: "TRUSS-01",
      date: "2024-01-05",
      type: "预防性维护",
      cost: 8500,
      duration: 4,
      technician: "张工程师",
      result: "success",
    },
    {
      id: "MH-002",
      equipmentId: "AMR-02",
      date: "2024-01-03",
      type: "纠正性维护",
      cost: 12000,
      duration: 6,
      technician: "李技师",
      result: "success",
    },
    {
      id: "MH-003",
      equipmentId: "TEST-02",
      date: "2023-12-28",
      type: "紧急维护",
      cost: 18000,
      duration: 8,
      technician: "王技师",
      result: "partial",
    },
    {
      id: "MH-004",
      equipmentId: "TRUSS-03",
      date: "2023-12-20",
      type: "升级改造",
      cost: 25000,
      duration: 12,
      technician: "赵工程师",
      result: "success",
    },
  ]);

  const filteredPlans = maintenancePlans.filter((plan) => {
    const typeMatch = selectedEquipmentType === "all" || plan.equipmentType === selectedEquipmentType;
    const statusMatch = selectedStatus === "all" || plan.status === selectedStatus;
    return typeMatch && statusMatch;
  });

  const getStatusBadge = (status: string) => {
    const statusConfig = {
      scheduled: {
        label: "已计划",
        className: "bg-blue-100 text-blue-800 border-blue-200",
      },
      in_progress: {
        label: "进行中",
        className: "bg-yellow-100 text-yellow-800 border-yellow-200",
      },
      completed: {
        label: "已完成",
        className: "bg-green-100 text-green-800 border-green-200",
      },
      cancelled: {
        label: "已取消",
        className: "bg-gray-100 text-gray-800 border-gray-200",
      },
      overdue: {
        label: "已逾期",
        className: "bg-red-100 text-red-800 border-red-200",
      },
    };

    const config = statusConfig[status as keyof typeof statusConfig];
    return (
      <Badge className={`${config.className} border`}>{config.label}</Badge>
    );
  };

  const getPriorityBadge = (priority: string) => {
    const priorityConfig = {
      low: {
        label: "低",
        className: "bg-gray-100 text-gray-800 border-gray-200",
      },
      medium: {
        label: "中",
        className: "bg-blue-100 text-blue-800 border-blue-200",
      },
      high: {
        label: "高",
        className: "bg-orange-100 text-orange-800 border-orange-200",
      },
      critical: {
        label: "紧急",
        className: "bg-red-100 text-red-800 border-red-200",
      },
    };

    const config = priorityConfig[priority as keyof typeof priorityConfig];
    return (
      <Badge className={`${config.className} border`}>{config.label}</Badge>
    );
  };

  const getEquipmentIcon = (type: string) => {
    const icons = {
      truss: <Bot className="h-4 w-4 text-blue-600" />,
              amr: <Truck className="h-4 w-4 text-green-600" />,
      test: <TestTube2 className="h-4 w-4 text-purple-600" />,
      other: <Settings className="h-4 w-4 text-gray-600" />,
    };
    return icons[type as keyof typeof icons] || <Settings className="h-4 w-4 text-gray-600" />;
  };

  const getMaintenanceTypeLabel = (type: string) => {
    const types = {
      preventive: "预防性维护",
      corrective: "纠正性维护",
      emergency: "紧急维护",
      upgrade: "升级改造",
    };
    return types[type as keyof typeof types] || type;
  };

  // 统计数据
  const totalPlans = maintenancePlans.length;
  const completedPlans = maintenancePlans.filter(
    (p) => p.status === "completed",
  ).length;
  const overduePlans = maintenancePlans.filter(
    (p) => p.status === "overdue",
  ).length;
  const inProgressPlans = maintenancePlans.filter(
    (p) => p.status === "in_progress",
  ).length;
  const totalCost = maintenancePlans.reduce(
    (sum, plan) => sum + (plan.cost || 0),
    0,
  );

  // 月度统计数据
  const monthlyData = [
    { name: "1月", preventive: 15, corrective: 8, emergency: 3, upgrade: 2 },
    { name: "2月", preventive: 18, corrective: 6, emergency: 2, upgrade: 4 },
    { name: "3月", preventive: 22, corrective: 10, emergency: 4, upgrade: 1 },
    { name: "4月", preventive: 16, corrective: 5, emergency: 1, upgrade: 3 },
    { name: "5月", preventive: 20, corrective: 7, emergency: 2, upgrade: 2 },
    { name: "6月", preventive: 14, corrective: 9, emergency: 5, upgrade: 1 },
  ];

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <div>
          <h2 className="text-3xl font-bold tracking-tight">
            年度保养计划
          </h2>
          <p className="text-muted-foreground">
            设备预防性维护计划管理与执行跟踪
          </p>
        </div>
        <div className="flex items-center space-x-3">
          <Button
            onClick={() => setIsNewPlanDialogOpen(true)}
          >
            <Plus className="mr-2 h-4 w-4" />
            新建计划
          </Button>

          <Button
            variant="outline"
            size="sm"
          >
            <Download className="mr-2 h-4 w-4" />
            导出计划
          </Button>
        </div>
      </div>

      {/* Statistics Cards */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-5">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">总计划数</CardTitle>
            <FileText className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{totalPlans}</div>
            <p className="text-xs text-muted-foreground">2024年度计划</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">已完成</CardTitle>
            <CheckCircle className="h-4 w-4 text-green-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-green-600">
              {completedPlans}
            </div>
            <p className="text-xs text-muted-foreground">
              完成率 {Math.round((completedPlans / totalPlans) * 100)}%
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">进行中</CardTitle>
            <Clock className="h-4 w-4 text-yellow-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-yellow-600">
              {inProgressPlans}
            </div>
            <p className="text-xs text-muted-foreground">正在执行</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">逾期计划</CardTitle>
            <AlertTriangle className="h-4 w-4 text-red-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-red-600">
              {overduePlans}
            </div>
            <p className="text-xs text-muted-foreground">需要处理</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">维护成本</CardTitle>
            <DollarSign className="h-4 w-4 text-cyan-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-cyan-600">
              ¥{(totalCost / 10000).toFixed(1)}万
            </div>
            <p className="text-xs text-muted-foreground">年度预算</p>
          </CardContent>
        </Card>
      </div>

      {/* Main Content */}
      <Tabs defaultValue="plans" className="space-y-4">
        <TabsList>
          <TabsTrigger value="plans">维护计划</TabsTrigger>
          <TabsTrigger value="calendar">日历视图</TabsTrigger>
          <TabsTrigger value="statistics">统计分析</TabsTrigger>
          <TabsTrigger value="history">历史记录</TabsTrigger>
        </TabsList>

        {/* Plans Tab */}
        <TabsContent value="plans" className="space-y-4">
          {/* Filters */}
          <Card>
            <CardHeader>
              <CardTitle>筛选条件</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid gap-4 md:grid-cols-4">
                <div className="space-y-2">
                  <label className="text-sm font-medium">设备类型</label>
                  <Select
                    value={selectedEquipmentType}
                    onValueChange={setSelectedEquipmentType}
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">全部设备</SelectItem>
                      <SelectItem value="truss">桁架系统</SelectItem>
                      <SelectItem value="amr">AMR运输车</SelectItem>
                      <SelectItem value="test">测试台</SelectItem>
                      <SelectItem value="other">其他设备</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div className="space-y-2">
                  <label className="text-sm font-medium">维护状态</label>
                  <Select
                    value={selectedStatus}
                    onValueChange={setSelectedStatus}
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">全部状态</SelectItem>
                      <SelectItem value="scheduled">已计划</SelectItem>
                      <SelectItem value="in_progress">进行中</SelectItem>
                      <SelectItem value="completed">已完成</SelectItem>
                      <SelectItem value="cancelled">已取消</SelectItem>
                      <SelectItem value="overdue">已逾期</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div className="space-y-2">
                  <label className="text-sm font-medium">优先级</label>
                  <Select>
                    <SelectTrigger>
                      <SelectValue placeholder="选择优先级" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">全部优先级</SelectItem>
                      <SelectItem value="low">低</SelectItem>
                      <SelectItem value="medium">中</SelectItem>
                      <SelectItem value="high">高</SelectItem>
                      <SelectItem value="critical">紧急</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div className="flex items-end">
                  <Button className="w-full">
                    <Settings className="mr-2 h-4 w-4" />
                    筛选
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Plans Table */}
          <Card>
            <CardHeader>
              <CardTitle>维护计划列表</CardTitle>
              <CardDescription>
                显示 {filteredPlans.length} 条维护计划
              </CardDescription>
            </CardHeader>
            <CardContent>
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>设备信息</TableHead>
                    <TableHead>维护类型</TableHead>
                    <TableHead>优先级</TableHead>
                    <TableHead>计划日期</TableHead>
                    <TableHead>预计时长</TableHead>
                    <TableHead>负责人</TableHead>
                    <TableHead>状态</TableHead>
                    <TableHead>成本</TableHead>
                    <TableHead>操作</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {filteredPlans.map((plan) => (
                    <TableRow key={plan.id}>
                      <TableCell>
                        <div className="flex items-center space-x-2">
                          {getEquipmentIcon(plan.equipmentType)}
                          <div>
                            <div className="font-medium">{plan.equipmentName}</div>
                            <div className="text-sm text-muted-foreground">
                              {plan.equipmentId}
                            </div>
                          </div>
                        </div>
                      </TableCell>
                      <TableCell>
                        <div className="space-y-1">
                          <div className="font-medium">
                            {getMaintenanceTypeLabel(plan.maintenanceType)}
                          </div>
                          <div className="text-sm text-muted-foreground">
                            {plan.description}
                          </div>
                        </div>
                      </TableCell>
                      <TableCell>{getPriorityBadge(plan.priority)}</TableCell>
                      <TableCell>{plan.scheduledDate}</TableCell>
                      <TableCell>{plan.estimatedDuration}小时</TableCell>
                      <TableCell>
                        <div className="flex items-center space-x-1">
                          <User className="h-3 w-3 text-muted-foreground" />
                          <span>{plan.assignedTechnician}</span>
                        </div>
                      </TableCell>
                      <TableCell>{getStatusBadge(plan.status)}</TableCell>
                      <TableCell>¥{plan.cost?.toLocaleString()}</TableCell>
                      <TableCell>
                        <div className="flex items-center space-x-2">
                          <Dialog>
                            <DialogTrigger asChild>
                              <Button
                                variant="outline"
                                size="sm"
                                onClick={() => setSelectedPlan(plan)}
                              >
                                <Eye className="h-3 w-3" />
                              </Button>
                            </DialogTrigger>
                            <DialogContent>
                              <DialogHeader>
                                <DialogTitle>维护计划详情</DialogTitle>
                                <DialogDescription>
                                  查看维护计划的详细信息
                                </DialogDescription>
                              </DialogHeader>
                              {selectedPlan && (
                                <div className="space-y-4">
                                  <div className="grid grid-cols-2 gap-4">
                                    <div>
                                      <label className="text-sm font-medium">设备名称</label>
                                      <p>{selectedPlan.equipmentName}</p>
                                    </div>
                                    <div>
                                      <label className="text-sm font-medium">设备ID</label>
                                      <p>{selectedPlan.equipmentId}</p>
                                    </div>
                                    <div>
                                      <label className="text-sm font-medium">维护类型</label>
                                      <p>{getMaintenanceTypeLabel(selectedPlan.maintenanceType)}</p>
                                    </div>
                                    <div>
                                      <label className="text-sm font-medium">优先级</label>
                                      <div className="mt-1">{getPriorityBadge(selectedPlan.priority)}</div>
                                    </div>
                                    <div>
                                      <label className="text-sm font-medium">计划日期</label>
                                      <p>{selectedPlan.scheduledDate}</p>
                                    </div>
                                    <div>
                                      <label className="text-sm font-medium">预计时长</label>
                                      <p>{selectedPlan.estimatedDuration}小时</p>
                                    </div>
                                    <div>
                                      <label className="text-sm font-medium">负责人</label>
                                      <p>{selectedPlan.assignedTechnician}</p>
                                    </div>
                                    <div>
                                      <label className="text-sm font-medium">状态</label>
                                      <div className="mt-1">{getStatusBadge(selectedPlan.status)}</div>
                                    </div>
                                    <div>
                                      <label className="text-sm font-medium">成本</label>
                                      <p>¥{selectedPlan.cost?.toLocaleString()}</p>
                                    </div>
                                    {selectedPlan.actualDuration && (
                                      <div>
                                        <label className="text-sm font-medium">实际时长</label>
                                        <p>{selectedPlan.actualDuration}小时</p>
                                      </div>
                                    )}
                                  </div>
                                  <div>
                                    <label className="text-sm font-medium">维护描述</label>
                                    <p className="mt-1">{selectedPlan.description}</p>
                                  </div>
                                  {selectedPlan.notes && (
                                    <div>
                                      <label className="text-sm font-medium">备注</label>
                                      <p className="mt-1">{selectedPlan.notes}</p>
                                    </div>
                                  )}
                                  {selectedPlan.nextScheduledDate && (
                                    <div>
                                      <label className="text-sm font-medium">下次计划日期</label>
                                      <p className="mt-1">{selectedPlan.nextScheduledDate}</p>
                                    </div>
                                  )}
                                </div>
                              )}
                            </DialogContent>
                          </Dialog>
                          <Button variant="outline" size="sm">
                            <Edit className="h-3 w-3" />
                          </Button>
                        </div>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Calendar Tab */}
        <TabsContent value="calendar" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>维护计划日历</CardTitle>
              <CardDescription>
                以日历形式查看维护计划安排
              </CardDescription>
            </CardHeader>
            <CardContent>
              <Calendar
                mode="single"
                selected={selectedDate}
                onSelect={setSelectedDate}
                className="rounded-md border"
              />
            </CardContent>
          </Card>
        </TabsContent>

        {/* Statistics Tab */}
        <TabsContent value="statistics" className="space-y-4">
          <div className="grid gap-4 md:grid-cols-2">
            <Card>
              <CardHeader>
                <CardTitle>月度维护统计</CardTitle>
                <CardDescription>
                  按维护类型统计的月度数据
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="h-80">
                  <StatusChart
                    type="bar"
                    data={monthlyData}
                    dataKey="preventive"
                    nameKey="name"
                    colors={["#22d3ee", "#3b82f6", "#ef4444", "#f59e0b"]}
                  />
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>维护成本趋势</CardTitle>
                <CardDescription>
                  维护成本变化趋势分析
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="h-80">
                  <StatusChart
                    type="line"
                    data={monthlyData.map((item, index) => ({
                      name: item.name,
                      cost: (item.preventive + item.corrective + item.emergency + item.upgrade) * 1000,
                    }))}
                    dataKey="cost"
                    nameKey="name"
                    colors={["#10b981"]}
                  />
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        {/* History Tab */}
        <TabsContent value="history" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>维护历史记录</CardTitle>
              <CardDescription>
                查看历史维护记录和结果
              </CardDescription>
            </CardHeader>
            <CardContent>
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>设备ID</TableHead>
                    <TableHead>维护日期</TableHead>
                    <TableHead>维护类型</TableHead>
                    <TableHead>维护时长</TableHead>
                    <TableHead>维护成本</TableHead>
                    <TableHead>负责人</TableHead>
                    <TableHead>结果</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {maintenanceHistory.map((record) => (
                    <TableRow key={record.id}>
                      <TableCell>{record.equipmentId}</TableCell>
                      <TableCell>{record.date}</TableCell>
                      <TableCell>{record.type}</TableCell>
                      <TableCell>{record.duration}小时</TableCell>
                      <TableCell>¥{record.cost.toLocaleString()}</TableCell>
                      <TableCell>{record.technician}</TableCell>
                      <TableCell>
                        <Badge
                          className={
                            record.result === "success"
                              ? "bg-green-100 text-green-800 border-green-200"
                              : record.result === "partial"
                                ? "bg-yellow-100 text-yellow-800 border-yellow-200"
                                : "bg-red-100 text-red-800 border-red-200"
                          }
                        >
                          {record.result === "success"
                            ? "成功"
                            : record.result === "partial"
                              ? "部分成功"
                              : "失败"}
                        </Badge>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>

      {/* New Plan Dialog */}
      <Dialog open={isNewPlanDialogOpen} onOpenChange={setIsNewPlanDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>新建维护计划</DialogTitle>
            <DialogDescription>
              创建新的设备维护计划
            </DialogDescription>
          </DialogHeader>
          <div className="space-y-4">
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <label className="text-sm font-medium">设备类型</label>
                <Select>
                  <SelectTrigger>
                    <SelectValue placeholder="选择设备类型" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="truss">桁架系统</SelectItem>
                    <SelectItem value="amr">AMR运输车</SelectItem>
                    <SelectItem value="test">测试台</SelectItem>
                    <SelectItem value="other">其他设备</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <div className="space-y-2">
                <label className="text-sm font-medium">维护类型</label>
                <Select>
                  <SelectTrigger>
                    <SelectValue placeholder="选择维护类型" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="preventive">预防性维护</SelectItem>
                    <SelectItem value="corrective">纠正性维护</SelectItem>
                    <SelectItem value="emergency">紧急维护</SelectItem>
                    <SelectItem value="upgrade">升级改造</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>
            <div className="space-y-2">
              <label className="text-sm font-medium">维护描述</label>
              <Input placeholder="请输入维护描述" />
            </div>
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <label className="text-sm font-medium">计划日期</label>
                <Input type="date" />
              </div>
              <div className="space-y-2">
                <label className="text-sm font-medium">预计时长(小时)</label>
                <Input type="number" placeholder="4" />
              </div>
            </div>
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <label className="text-sm font-medium">负责人</label>
                <Input placeholder="请输入负责人姓名" />
              </div>
              <div className="space-y-2">
                <label className="text-sm font-medium">优先级</label>
                <Select>
                  <SelectTrigger>
                    <SelectValue placeholder="选择优先级" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="low">低</SelectItem>
                    <SelectItem value="medium">中</SelectItem>
                    <SelectItem value="high">高</SelectItem>
                    <SelectItem value="critical">紧急</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>
          </div>
        </DialogContent>
      </Dialog>
    </div>
  );
}
