import { useState, useEffect } from "react";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Progress } from "@/components/ui/progress";
import { Tabs, <PERSON><PERSON><PERSON>ontent, <PERSON>bsList, TabsTrigger } from "@/components/ui/tabs";
import { StatusChart } from "@/components/StatusChart";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Bot,
  Truck,
  TestTube2,
  Activity,
  TrendingUp,
  TrendingDown,
  Minus,
  AlertTriangle,
  CheckCircle,
  Clock,
  Gauge,
  Zap,
  Settings,
  Download,
  RefreshCw,
  BarChart3,
} from "lucide-react";

// 设备运行数据接口
interface EquipmentRunningData {
  equipmentId: string;
  equipmentName: string;
  type: "truss" | "amr" | "test";
  status: "running" | "idle" | "maintenance" | "error";
  currentSpeed: number;
  maxSpeed: number;
  powerConsumption: number;
  efficiency: number;
  temperature: number;
  vibration: number;
  uptime: number;
  lastMaintenance: string;
  totalDistance?: number; // AMR专用
  currentLoad?: number; // AMR和桁架专用
  testCount?: number; // 测试系统专用
  errorCount: number;
}

// 历史性能数据接口
interface PerformanceData {
  timestamp: string;
  equipmentId: string;
  speed: number;
  efficiency: number;
  powerConsumption: number;
  temperature: number;
}

// 错误日志接口
interface ErrorLog {
  id: string;
  equipmentId: string;
  timestamp: string;
  errorCode: string;
  errorMessage: string;
  severity: "low" | "medium" | "high" | "critical";
  status: "active" | "resolved";
  resolution?: string;
}

export default function EquipmentData() {
  const [selectedTimeRange, setSelectedTimeRange] = useState("24h");
  const [selectedEquipment, setSelectedEquipment] = useState("all");
  const [autoRefresh, setAutoRefresh] = useState(true);

  // 模拟实时设备数据
  const [equipmentData, setEquipmentData] = useState<EquipmentRunningData[]>([
    {
      equipmentId: "TRUSS-01",
      equipmentName: "桁架系统-01",
      type: "truss",
      status: "running",
      currentSpeed: 75,
      maxSpeed: 100,
      powerConsumption: 18.5,
      efficiency: 92,
      temperature: 45,
      vibration: 0.8,
      uptime: 23.5,
      lastMaintenance: "2024-01-05",
      currentLoad: 85,
      errorCount: 2,
    },
    {
          equipmentId: "AMR-01",
    equipmentName: "AMR运输车-01",
    type: "amr",
      status: "running",
      currentSpeed: 85,
      maxSpeed: 120,
      powerConsumption: 12.3,
      efficiency: 88,
      temperature: 38,
      vibration: 0.5,
      uptime: 22.8,
      lastMaintenance: "2024-01-08",
      totalDistance: 2450,
      currentLoad: 70,
      errorCount: 1,
    },
    {
          equipmentId: "AMR-02",
    equipmentName: "AMR运输车-02",
    type: "amr",
      status: "maintenance",
      currentSpeed: 0,
      maxSpeed: 120,
      powerConsumption: 0,
      efficiency: 0,
      temperature: 25,
      vibration: 0,
      uptime: 0,
      lastMaintenance: "2024-01-10",
      totalDistance: 1890,
      currentLoad: 0,
      errorCount: 3,
    },
    {
      equipmentId: "TEST-01",
      equipmentName: "测试台-01",
      type: "test",
      status: "running",
      currentSpeed: 100,
      maxSpeed: 100,
      powerConsumption: 25.8,
      efficiency: 95,
      temperature: 65,
      vibration: 0.3,
      uptime: 23.2,
      lastMaintenance: "2024-01-07",
      testCount: 45,
      errorCount: 0,
    },
    {
      equipmentId: "TEST-02",
      equipmentName: "测试台-02",
      type: "test",
      status: "error",
      currentSpeed: 0,
      maxSpeed: 100,
      powerConsumption: 5.2,
      efficiency: 0,
      temperature: 85,
      vibration: 2.1,
      uptime: 18.5,
      lastMaintenance: "2024-01-02",
      testCount: 32,
      errorCount: 5,
    },
  ]);

  // 历史性能数据
  const [performanceHistory] = useState<PerformanceData[]>([
    {
      timestamp: "00:00",
      equipmentId: "TRUSS-01",
      speed: 80,
      efficiency: 85,
      powerConsumption: 15,
      temperature: 42,
    },
    {
      timestamp: "04:00",
      equipmentId: "TRUSS-01",
      speed: 75,
      efficiency: 90,
      powerConsumption: 18,
      temperature: 44,
    },
    {
      timestamp: "08:00",
      equipmentId: "TRUSS-01",
      speed: 85,
      efficiency: 88,
      powerConsumption: 20,
      temperature: 46,
    },
    {
      timestamp: "12:00",
      equipmentId: "TRUSS-01",
      speed: 90,
      efficiency: 92,
      powerConsumption: 22,
      temperature: 48,
    },
    {
      timestamp: "16:00",
      equipmentId: "TRUSS-01",
      speed: 75,
      efficiency: 91,
      powerConsumption: 19,
      temperature: 45,
    },
    {
      timestamp: "20:00",
      equipmentId: "TRUSS-01",
      speed: 70,
      efficiency: 89,
      powerConsumption: 17,
      temperature: 43,
    },
  ]);

  // 错误日志数据
  const [errorLogs] = useState<ErrorLog[]>([
    {
      id: "ERR-001",
      equipmentId: "TEST-02",
      timestamp: "2024-01-10 14:23:15",
      errorCode: "TEMP_HIGH",
      errorMessage: "测试台温度过高，超过安全阈值",
      severity: "critical",
      status: "active",
    },
    {
      id: "ERR-002",
      equipmentId: "AMR-02",
      timestamp: "2024-01-10 13:45:22",
      errorCode: "COMM_LOSS",
      errorMessage: "通讯连接中断",
      severity: "high",
      status: "resolved",
      resolution: "重启通讯模块",
    },
    {
      id: "ERR-003",
      equipmentId: "TRUSS-01",
      timestamp: "2024-01-10 11:30:08",
      errorCode: "VIBRATION",
      errorMessage: "异常振动检测",
      severity: "medium",
      status: "resolved",
      resolution: "调整运行参数",
    },
    {
      id: "ERR-004",
      equipmentId: "AMR-01",
      timestamp: "2024-01-10 09:15:33",
      errorCode: "BATTERY_LOW",
      errorMessage: "电池电量低",
      severity: "low",
      status: "resolved",
      resolution: "充电完成",
    },
  ]);

  // 实时数据更新
  useEffect(() => {
    if (!autoRefresh) return;

    const interval = setInterval(() => {
      setEquipmentData((prev) =>
        prev.map((equipment) => ({
          ...equipment,
          currentSpeed:
            equipment.status === "running"
              ? Math.max(0, equipment.currentSpeed + (Math.random() - 0.5) * 10)
              : 0,
          powerConsumption:
            equipment.status === "running"
              ? Math.max(
                  0,
                  equipment.powerConsumption + (Math.random() - 0.5) * 2,
                )
              : equipment.type === "amr" && equipment.status === "maintenance"
                ? 0
                : Math.min(5, equipment.powerConsumption),
          temperature: Math.max(
            20,
            equipment.temperature + (Math.random() - 0.5) * 3,
          ),
          efficiency:
            equipment.status === "running"
              ? Math.min(
                  100,
                  Math.max(0, equipment.efficiency + (Math.random() - 0.5) * 5),
                )
              : 0,
        })),
      );
    }, 3000);

    return () => clearInterval(interval);
  }, [autoRefresh]);

  const getStatusIcon = (type: string) => {
    switch (type) {
      case "truss":
        return <Bot className="h-5 w-5" />;
      case "amr":
        return <Truck className="h-5 w-5" />;
      case "test":
        return <TestTube2 className="h-5 w-5" />;
      default:
        return <Settings className="h-5 w-5" />;
    }
  };

  const getStatusBadge = (status: string) => {
    const statusConfig = {
      running: {
        label: "运行中",
        className: "bg-green-500/20 text-green-700 border-green-500/50",
      },
      idle: {
        label: "待机",
        className: "bg-blue-500/20 text-blue-700 border-blue-500/50",
      },
      maintenance: {
        label: "维护中",
        className: "bg-yellow-500/20 text-yellow-700 border-yellow-500/50",
      },
      error: {
        label: "故障",
        className: "bg-red-500/20 text-red-700 border-red-500/50",
      },
    };

    const config = statusConfig[status as keyof typeof statusConfig];
    return (
      <Badge className={`${config.className} border`}>{config.label}</Badge>
    );
  };

  const getErrorSeverityBadge = (severity: string) => {
    const severityConfig = {
      low: { label: "低", className: "bg-blue-500/20 text-blue-700" },
      medium: { label: "中", className: "bg-yellow-500/20 text-yellow-700" },
      high: { label: "高", className: "bg-orange-500/20 text-orange-700" },
      critical: { label: "严重", className: "bg-red-500/20 text-red-700" },
    };

    const config = severityConfig[severity as keyof typeof severityConfig];
    return <Badge className={config.className}>{config.label}</Badge>;
  };

  const getTrendIcon = (current: number, previous: number) => {
    if (current > previous)
      return <TrendingUp className="h-4 w-4 text-green-500" />;
    if (current < previous)
      return <TrendingDown className="h-4 w-4 text-red-500" />;
    return <Minus className="h-4 w-4 text-gray-500" />;
  };

  const filteredEquipment =
    selectedEquipment === "all"
      ? equipmentData
      : equipmentData.filter((eq) => eq.type === selectedEquipment);

  const currentErrorsCount = errorLogs.filter(
    (log) => log.status === "active",
  ).length;
  const averageEfficiency = Math.round(
    equipmentData.reduce((sum, eq) => sum + eq.efficiency, 0) /
      equipmentData.length,
  );
  const totalPowerConsumption =
    Math.round(
      equipmentData.reduce((sum, eq) => sum + eq.powerConsumption, 0) * 10,
    ) / 10;

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-3xl font-bold tracking-tight">设备运行数据</h2>
          <p className="text-muted-foreground">
            实时监控桁架、AMR、测试系统的运行状态和性能数据
          </p>
        </div>
        <div className="flex items-center space-x-2">
          <Select
            value={selectedTimeRange}
            onValueChange={setSelectedTimeRange}
          >
            <SelectTrigger className="w-32">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="1h">1小时</SelectItem>
              <SelectItem value="24h">24小时</SelectItem>
              <SelectItem value="7d">7天</SelectItem>
              <SelectItem value="30d">30天</SelectItem>
            </SelectContent>
          </Select>

          <Select
            value={selectedEquipment}
            onValueChange={setSelectedEquipment}
          >
            <SelectTrigger className="w-32">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">全部设备</SelectItem>
              <SelectItem value="truss">桁架系统</SelectItem>
                              <SelectItem value="amr">AMR车队</SelectItem>
              <SelectItem value="test">测试系统</SelectItem>
            </SelectContent>
          </Select>

          <Button
            variant="outline"
            size="sm"
            onClick={() => setAutoRefresh(!autoRefresh)}
            className={autoRefresh ? "bg-green-50 border-green-200" : ""}
          >
            <RefreshCw
              className={`h-4 w-4 mr-2 ${autoRefresh ? "animate-spin" : ""}`}
            />
            {autoRefresh ? "自动刷新" : "手动刷新"}
          </Button>

          <Button variant="outline" size="sm">
            <Download className="h-4 w-4 mr-2" />
            导出数据
          </Button>
        </div>
      </div>

      {/* 概览统计 */}
      <div className="grid gap-4 md:grid-cols-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">运行设备</CardTitle>
            <Activity className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-green-600">
              {equipmentData.filter((eq) => eq.status === "running").length}
            </div>
            <p className="text-xs text-muted-foreground">
              / {equipmentData.length} 台设备
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">平均效率</CardTitle>
            <Gauge className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{averageEfficiency}%</div>
            <p className="text-xs text-muted-foreground">
              {getTrendIcon(averageEfficiency, 85)} 比昨日{" "}
              {averageEfficiency > 85 ? "+" : ""}
              {averageEfficiency - 85}%
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">总功耗</CardTitle>
            <Zap className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{totalPowerConsumption}</div>
            <p className="text-xs text-muted-foreground">kW·h (当前小时)</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">活跃故障</CardTitle>
            <AlertTriangle className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div
              className={`text-2xl font-bold ${currentErrorsCount > 0 ? "text-red-600" : "text-green-600"}`}
            >
              {currentErrorsCount}
            </div>
            <p className="text-xs text-muted-foreground">需要处理的故障</p>
          </CardContent>
        </Card>
      </div>

      <Tabs defaultValue="realtime" className="space-y-4">
        <TabsList>
          <TabsTrigger value="realtime">实时监控</TabsTrigger>
          <TabsTrigger value="performance">性能分析</TabsTrigger>
          <TabsTrigger value="errors">故障日志</TabsTrigger>
          <TabsTrigger value="maintenance">维护记录</TabsTrigger>
        </TabsList>

        {/* 实时监控 */}
        <TabsContent value="realtime" className="space-y-4">
          <div className="grid gap-4">
            {filteredEquipment.map((equipment) => (
              <Card key={equipment.equipmentId}>
                <CardHeader>
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-3">
                      <div className="p-2 bg-blue-100 rounded-lg">
                        {getStatusIcon(equipment.type)}
                      </div>
                      <div>
                        <CardTitle className="text-lg">
                          {equipment.equipmentName}
                        </CardTitle>
                        <CardDescription>
                          ID: {equipment.equipmentId} | 上次维护:{" "}
                          {equipment.lastMaintenance}
                        </CardDescription>
                      </div>
                    </div>
                    <div className="flex items-center space-x-2">
                      {getStatusBadge(equipment.status)}
                      {equipment.errorCount > 0 && (
                        <Badge variant="destructive" className="text-xs">
                          {equipment.errorCount} 错误
                        </Badge>
                      )}
                    </div>
                  </div>
                </CardHeader>
                <CardContent>
                  <div className="grid gap-6 md:grid-cols-3">
                    {/* 运行参数 */}
                    <div className="space-y-4">
                      <h4 className="font-semibold text-sm text-gray-700">
                        运行参数
                      </h4>
                      <div className="space-y-3">
                        <div>
                          <div className="flex justify-between text-sm mb-1">
                            <span>运行速度</span>
                            <span>
                              {Math.round(equipment.currentSpeed)}/
                              {equipment.maxSpeed}
                            </span>
                          </div>
                          <Progress
                            value={
                              (equipment.currentSpeed / equipment.maxSpeed) *
                              100
                            }
                            className="h-2"
                          />
                        </div>
                        <div>
                          <div className="flex justify-between text-sm mb-1">
                            <span>效率</span>
                            <span>{Math.round(equipment.efficiency)}%</span>
                          </div>
                          <Progress
                            value={equipment.efficiency}
                            className="h-2"
                          />
                        </div>
                        {equipment.currentLoad !== undefined && (
                          <div>
                            <div className="flex justify-between text-sm mb-1">
                              <span>负载</span>
                              <span>{equipment.currentLoad}%</span>
                            </div>
                            <Progress
                              value={equipment.currentLoad}
                              className="h-2"
                            />
                          </div>
                        )}
                      </div>
                    </div>

                    {/* 系统状态 */}
                    <div className="space-y-4">
                      <h4 className="font-semibold text-sm text-gray-700">
                        系统状态
                      </h4>
                      <div className="space-y-3">
                        <div className="flex justify-between">
                          <span className="text-sm">功耗</span>
                          <span className="text-sm font-medium">
                            {equipment.powerConsumption.toFixed(1)} kW
                          </span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-sm">温度</span>
                          <span
                            className={`text-sm font-medium ${equipment.temperature > 70 ? "text-red-600" : equipment.temperature > 50 ? "text-yellow-600" : "text-green-600"}`}
                          >
                            {Math.round(equipment.temperature)}°C
                          </span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-sm">振动</span>
                          <span
                            className={`text-sm font-medium ${equipment.vibration > 1.5 ? "text-red-600" : equipment.vibration > 1.0 ? "text-yellow-600" : "text-green-600"}`}
                          >
                            {equipment.vibration.toFixed(1)} mm/s
                          </span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-sm">运行时间</span>
                          <span className="text-sm font-medium">
                            {equipment.uptime.toFixed(1)}h
                          </span>
                        </div>
                      </div>
                    </div>

                    {/* 专用数据 */}
                    <div className="space-y-4">
                      <h4 className="font-semibold text-sm text-gray-700">
                        专用数据
                      </h4>
                      <div className="space-y-3">
                        {equipment.type === "amr" &&
                          equipment.totalDistance && (
                            <div className="flex justify-between">
                              <span className="text-sm">累计里程</span>
                              <span className="text-sm font-medium">
                                {equipment.totalDistance} km
                              </span>
                            </div>
                          )}
                        {equipment.type === "test" && equipment.testCount && (
                          <div className="flex justify-between">
                            <span className="text-sm">测试次数</span>
                            <span className="text-sm font-medium">
                              {equipment.testCount} 次
                            </span>
                          </div>
                        )}
                        <div className="flex justify-between">
                          <span className="text-sm">故障次数</span>
                          <span
                            className={`text-sm font-medium ${equipment.errorCount > 0 ? "text-red-600" : "text-green-600"}`}
                          >
                            {equipment.errorCount} 次
                          </span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-sm">状态持续</span>
                          <span className="text-sm font-medium">
                            {equipment.status === "running"
                              ? "2.5h"
                              : equipment.status === "idle"
                                ? "0.5h"
                                : equipment.status === "maintenance"
                                  ? "1.2h"
                                  : "0.8h"}
                          </span>
                        </div>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </TabsContent>

        {/* 性能分析 */}
        <TabsContent value="performance" className="space-y-4">
          <div className="grid gap-6 md:grid-cols-2">
            <Card>
              <CardHeader>
                <CardTitle>设备效率趋势</CardTitle>
                <CardDescription>过去24小时设备运行效率变化</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="h-80">
                  <StatusChart
                    type="line"
                    data={performanceHistory}
                    dataKey="efficiency"
                    nameKey="timestamp"
                    colors={["#3b82f6"]}
                  />
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>功耗分析</CardTitle>
                <CardDescription>各设备类型功耗分布</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="h-80">
                  <StatusChart
                    type="pie"
                    data={[
                      {
                        name: "桁架系统",
                        value: equipmentData
                          .filter((eq) => eq.type === "truss")
                          .reduce((sum, eq) => sum + eq.powerConsumption, 0),
                      },
                      {
                        name: "AMR车队",
                        value: equipmentData
                          .filter((eq) => eq.type === "amr")
                          .reduce((sum, eq) => sum + eq.powerConsumption, 0),
                      },
                      {
                        name: "测试系统",
                        value: equipmentData
                          .filter((eq) => eq.type === "test")
                          .reduce((sum, eq) => sum + eq.powerConsumption, 0),
                      },
                    ]}
                    dataKey="value"
                    nameKey="name"
                    colors={["#3b82f6", "#10b981", "#f59e0b"]}
                  />
                </div>
              </CardContent>
            </Card>

            <Card className="md:col-span-2">
              <CardHeader>
                <CardTitle>设备性能对比</CardTitle>
                <CardDescription>各设备关键性能指标对比</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="h-80">
                  <StatusChart
                    type="bar"
                    data={equipmentData.map((eq) => ({
                      name: eq.equipmentName,
                      efficiency: eq.efficiency,
                      uptime: eq.uptime,
                      powerConsumption: eq.powerConsumption,
                    }))}
                    dataKey="efficiency"
                    nameKey="name"
                    colors={["#10b981", "#3b82f6", "#f59e0b"]}
                  />
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        {/* 故障日志 */}
        <TabsContent value="errors" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>设备故障日志</CardTitle>
              <CardDescription>
                设备运行过程中的故障记录和处理情况
              </CardDescription>
            </CardHeader>
            <CardContent>
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>时间</TableHead>
                    <TableHead>设备</TableHead>
                    <TableHead>故障代码</TableHead>
                    <TableHead>故障描述</TableHead>
                    <TableHead>严重程度</TableHead>
                    <TableHead>状态</TableHead>
                    <TableHead>解决方案</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {errorLogs.map((log) => (
                    <TableRow key={log.id}>
                      <TableCell className="text-sm">{log.timestamp}</TableCell>
                      <TableCell>
                        <div className="flex items-center space-x-2">
                          {getStatusIcon(
                            equipmentData.find(
                              (eq) => eq.equipmentId === log.equipmentId,
                            )?.type || "test",
                          )}
                          <span className="text-sm">{log.equipmentId}</span>
                        </div>
                      </TableCell>
                      <TableCell>
                        <code className="text-xs bg-gray-100 px-2 py-1 rounded">
                          {log.errorCode}
                        </code>
                      </TableCell>
                      <TableCell className="text-sm">
                        {log.errorMessage}
                      </TableCell>
                      <TableCell>
                        {getErrorSeverityBadge(log.severity)}
                      </TableCell>
                      <TableCell>
                        <Badge
                          variant={
                            log.status === "active" ? "destructive" : "default"
                          }
                        >
                          {log.status === "active" ? "活跃" : "已解决"}
                        </Badge>
                      </TableCell>
                      <TableCell className="text-sm">
                        {log.resolution || "待处理"}
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </CardContent>
          </Card>
        </TabsContent>

        {/* 维护记录 */}
        <TabsContent value="maintenance" className="space-y-4">
          <div className="grid gap-4 md:grid-cols-2">
            <Card>
              <CardHeader>
                <CardTitle>维护计划</CardTitle>
                <CardDescription>设备预定维护计划</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {equipmentData.map((equipment) => (
                    <div
                      key={equipment.equipmentId}
                      className="flex items-center justify-between p-4 border rounded-lg"
                    >
                      <div className="flex items-center space-x-3">
                        {getStatusIcon(equipment.type)}
                        <div>
                          <div className="font-medium">
                            {equipment.equipmentName}
                          </div>
                          <div className="text-sm text-gray-500">
                            上次维护: {equipment.lastMaintenance}
                          </div>
                        </div>
                      </div>
                      <div className="text-right">
                        <div className="text-sm font-medium">
                          下次维护:{" "}
                          {new Date(
                            new Date(equipment.lastMaintenance).getTime() +
                              30 * 24 * 60 * 60 * 1000,
                          ).toLocaleDateString()}
                        </div>
                        <div className="text-xs text-gray-500">
                          {Math.ceil(
                            (new Date(
                              new Date(equipment.lastMaintenance).getTime() +
                                30 * 24 * 60 * 60 * 1000,
                            ).getTime() -
                              new Date().getTime()) /
                              (24 * 60 * 60 * 1000),
                          )}{" "}
                          天后
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>维护统计</CardTitle>
                <CardDescription>设备维护频率和成本统计</CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-2 gap-4">
                  <div className="text-center p-4 bg-blue-50 rounded-lg">
                    <div className="text-2xl font-bold text-blue-600">15</div>
                    <div className="text-sm text-blue-800">本月维护次数</div>
                  </div>
                  <div className="text-center p-4 bg-green-50 rounded-lg">
                    <div className="text-2xl font-bold text-green-600">
                      98.5%
                    </div>
                    <div className="text-sm text-green-800">设备可用率</div>
                  </div>
                  <div className="text-center p-4 bg-yellow-50 rounded-lg">
                    <div className="text-2xl font-bold text-yellow-600">
                      ¥45,000
                    </div>
                    <div className="text-sm text-yellow-800">维护成本</div>
                  </div>
                  <div className="text-center p-4 bg-purple-50 rounded-lg">
                    <div className="text-2xl font-bold text-purple-600">
                      2.5
                    </div>
                    <div className="text-sm text-purple-800">
                      平均修复时间(h)
                    </div>
                  </div>
                </div>

                <div className="mt-6">
                  <h4 className="font-medium mb-3">维护类型分布</h4>
                  <div className="space-y-2">
                    <div className="flex justify-between">
                      <span className="text-sm">预防性维护</span>
                      <span className="text-sm font-medium">70%</span>
                    </div>
                    <Progress value={70} className="h-2" />
                    <div className="flex justify-between">
                      <span className="text-sm">纠正性维护</span>
                      <span className="text-sm font-medium">25%</span>
                    </div>
                    <Progress value={25} className="h-2" />
                    <div className="flex justify-between">
                      <span className="text-sm">紧急维护</span>
                      <span className="text-sm font-medium">5%</span>
                    </div>
                    <Progress value={5} className="h-2" />
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>
      </Tabs>
    </div>
  );
}
