# 惯组产品智能化存储及月稳测试系统

<div align="center">
  <img src="public/placeholder.svg" alt="系统Logo" width="120" />
  <h3>智能化仓储与测试管理平台</h3>
</div>

## 📋 系统概述

惯组产品智能化存储及月稳测试系统是一套专为惯性导航组件生产企业打造的智能仓储与测试管理平台。系统集成了仓储管理、自动化测试、设备监控、数据分析等功能，实现了惯组产品从入库、存储、出库测试到数据分析的全流程自动化管理。

### 核心价值

- **提升测试效率**：自动化测试流程，减少人工干预，提高测试效率
- **优化仓储管理**：智能化存储与调度，提高空间利用率
- **实时监控**：设备状态、测试进度实时可视化监控
- **数据驱动决策**：全流程数据收集与分析，支持决策优化

## 🚀 主要功能模块

### 1. 工业大屏可视化

- 3D仓储可视化展示
- 设备实时状态监控
- 关键指标实时展示
- 报警信息实时推送

### 2. 测试管理系统

- **测试控制中心**：测试任务管理与控制
- **测试计划管理**：排程与资源分配
- **测试监控**：实时监控测试进度与状态
- **一键还原**：故障快速恢复
- **数据分析**：测试数据统计与分析

### 3. 设备管理系统

- **设备基准信息**：设备档案管理
- **AGV充电管理**：自动调度与充电管理
- **三色灯控制**：货位状态指示系统
- **设备维护计划**：预防性维护管理
- **设备数据分析**：设备性能与健康状态分析

### 4. 出入库管理

- 产品出入库操作
- 库存状态实时监控
- 出入库记录查询
- 库存报表与分析

### 5. 系统管理

- 用户权限管理
- 角色配置
- 系统参数设置
- 数字孪生管理

## 💻 技术架构

- **前端**：React + TypeScript + Vite
- **UI组件**：Radix UI + Tailwind CSS
- **3D可视化**：Three.js + React Three Fiber
- **状态管理**：React Query
- **图表**：Recharts
- **接口通信**：RESTful API

## 🔧 安装与部署

### 系统要求

- Node.js 16.0+
- npm 8.0+
- 现代浏览器（Chrome, Firefox, Edge等）

### 安装步骤

1. 克隆代码仓库

```bash
git clone [仓库地址]
cd guanzu-storage-system
```

2. 安装依赖

```bash
npm install
```

3. 开发环境运行

```bash
npm run dev
```

4. 生产环境构建

```bash
npm run build
```

5. 部署构建产物

将`dist`目录下的文件部署到Web服务器即可。

## 📱 使用指南

### 快速入门

1. **登录系统**：使用分配的账号密码登录
2. **主控面板**：查看系统整体运行状态
3. **测试管理**：进入测试控制中心，管理测试任务
4. **设备监控**：查看设备实时状态
5. **数据分析**：查询历史数据与分析报表

### 常用操作流程

#### 产品出库测试流程

1. 在测试计划管理中创建测试计划
2. 系统自动调度桁架从库位取出产品
3. AGV将产品运送至测试台
4. 测试台自动连接并开始测试
5. 测试完成后，系统自动安排入库

#### 设备故障一键还原

1. 在测试监控页面找到出现故障的任务
2. 点击"一键还原"按钮
3. 系统自动执行故障恢复流程
4. 恢复完成后，任务继续执行

## 🔒 安全与权限

系统采用基于角色的访问控制（RBAC）模型，主要角色包括：

- **系统管理员**：拥有所有权限
- **测试工程师**：测试相关操作权限
- **仓库管理员**：仓储管理相关权限
- **设备维护人员**：设备维护相关权限
- **数据分析师**：数据查询与分析权限

## 🌐 系统集成

本系统可与以下系统进行集成：

- **MES系统**：生产计划与执行
- **WMS系统**：仓储管理
- **ERP系统**：企业资源计划
- **设备控制系统**：桁架、AGV等设备直接控制

## 📞 支持与帮助

如有任何问题或需要技术支持，请联系：

- **技术支持邮箱**：<EMAIL>
- **技术支持电话**：400-123-4567

## 📄 版本信息

- **当前版本**：v1.0.0
- **发布日期**：2024-06-01
- **版权所有**：XXX科技有限公司 