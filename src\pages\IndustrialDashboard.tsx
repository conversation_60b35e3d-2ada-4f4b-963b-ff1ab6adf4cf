import React from "react";
import bgImg from "@/asserts/工业大屏三维图.png";

const menuItems = [
  { label: "管理首页", href: "/" },
  { label: "测试管理", href: "/test/control" },
  { label: "仓储管理", href: "/warehouse/operations" },
  { label: "排产管理", href: "/test/planning" },
  { label: "设备管理", href: "/equipment/base-info" },
  { label: "系统管理", href: "/system/users" },
];

const testingTasks = [
  { id: "A-2024-001", station: "测试台-01", duration: "02:15:23" },
  { id: "A-2024-002", station: "测试台-03", duration: "01:05:47" },
];

const warehouseTasks = [
  { id: "B-2024-011", type: "入库", status: "进行中" },
  { id: "B-2024-012", type: "出库", status: "进行中" },
];

const chargingTasks = [
  { id: "AMR-01", status: "充电中", time: "00:45:12" },
  { id: "AMR-03", status: "充电中", time: "00:22:08" },
];

export default function IndustrialDashboard() {
  return (
    <div
      style={{
        minHeight: "100vh",
        width: "100vw",
        position: "relative",
        overflow: "hidden",
        fontFamily: 'Segoe UI, Arial, sans-serif',
        background: `url(${bgImg}) center center / cover no-repeat`,
      }}
    >
      {/* 顶部菜单栏和标题 */}
      <div
        style={{
          position: "absolute",
          top: 0,
          left: 0,
          width: "100%",
          height: 60,
          display: "flex",
          alignItems: "center",
          justifyContent: "center",
          zIndex: 10,
          background: "linear-gradient(135deg, rgba(10, 15, 28, 0.95) 0%, rgba(26, 35, 50, 0.95) 50%, rgba(10, 15, 28, 0.95) 100%)",
          borderBottom: "2px solid rgba(59, 130, 246, 0.6)",
          backdropFilter: "blur(20px)",
          boxShadow: "0 4px 20px rgba(0, 0, 0, 0.3)",
        }}
      >
        <div style={{ display: "flex", flex: 1, justifyContent: "flex-end", gap: 18, maxWidth: 320 }}>
          {menuItems.slice(0, 3).map((item) => (
            <a
              key={item.label}
              href={item.href}
              style={{ 
                color: "#60a5fa", 
                fontSize: 16, 
                fontWeight: 500, 
                textDecoration: "none", 
                letterSpacing: 1, 
                padding: "8px 12px",
                borderRadius: "6px",
                transition: "all 0.3s ease",
                border: "1px solid transparent",
                backgroundColor: "rgba(59, 130, 246, 0.1)",
                backdropFilter: "blur(10px)",
              }}
              onMouseEnter={(e) => {
                e.currentTarget.style.color = "#93c5fd";
                e.currentTarget.style.backgroundColor = "rgba(59, 130, 246, 0.2)";
                e.currentTarget.style.borderColor = "rgba(59, 130, 246, 0.4)";
                e.currentTarget.style.boxShadow = "0 0 15px rgba(59, 130, 246, 0.3)";
                e.currentTarget.style.transform = "translateY(-2px)";
              }}
              onMouseLeave={(e) => {
                e.currentTarget.style.color = "#60a5fa";
                e.currentTarget.style.backgroundColor = "rgba(59, 130, 246, 0.1)";
                e.currentTarget.style.borderColor = "transparent";
                e.currentTarget.style.boxShadow = "none";
                e.currentTarget.style.transform = "translateY(0)";
              }}
            >
              {item.label}
            </a>
          ))}
        </div>
        <div style={{ flex: 2, textAlign: "center" }}>
          <span
            style={{
              color: "#fff",
              fontSize: 26,
              fontWeight: 700,
              letterSpacing: 3,
              textShadow: "0 0 20px rgba(59, 130, 246, 0.8), 0 0 40px rgba(59, 130, 246, 0.4)",
              padding: "0 16px",
              whiteSpace: 'nowrap',
              animation: "pulse-glow 2s infinite",
            }}
          >
            惯组产品智能化存储及测试系统
          </span>
        </div>
        <div style={{ display: "flex", flex: 1, justifyContent: "flex-start", gap: 18, maxWidth: 320 }}>
          {menuItems.slice(3, 6).map((item) => (
            <a
              key={item.label}
              href={item.href}
              style={{ 
                color: "#60a5fa", 
                fontSize: 16, 
                fontWeight: 500, 
                textDecoration: "none", 
                letterSpacing: 1, 
                padding: "8px 12px",
                borderRadius: "6px",
                transition: "all 0.3s ease",
                border: "1px solid transparent",
                backgroundColor: "rgba(59, 130, 246, 0.1)",
                backdropFilter: "blur(10px)",
              }}
              onMouseEnter={(e) => {
                e.currentTarget.style.color = "#93c5fd";
                e.currentTarget.style.backgroundColor = "rgba(59, 130, 246, 0.2)";
                e.currentTarget.style.borderColor = "rgba(59, 130, 246, 0.4)";
                e.currentTarget.style.boxShadow = "0 0 15px rgba(59, 130, 246, 0.3)";
                e.currentTarget.style.transform = "translateY(-2px)";
              }}
              onMouseLeave={(e) => {
                e.currentTarget.style.color = "#60a5fa";
                e.currentTarget.style.backgroundColor = "rgba(59, 130, 246, 0.1)";
                e.currentTarget.style.borderColor = "transparent";
                e.currentTarget.style.boxShadow = "none";
                e.currentTarget.style.transform = "translateY(0)";
              }}
            >
              {item.label}
            </a>
          ))}
        </div>
      </div>

      {/* 综合显示链接按钮 */}
      <div
        style={{
          position: "absolute",
          top: 80,
          left: 20,
          zIndex: 10,
        }}
      >
        <a
          href="/comprehensive-display"
          style={{
            display: "flex",
            alignItems: "center",
            justifyContent: "center",
            width: "50px",
            height: "50px",
            borderRadius: "50%",
            background: "linear-gradient(145deg, rgba(59, 130, 246, 0.2) 0%, rgba(37, 99, 235, 0.3) 100%)",
            border: "1px solid rgba(59, 130, 246, 0.4)",
            color: "#60a5fa",
            textDecoration: "none",
            transition: "all 0.3s ease",
            backdropFilter: "blur(10px)",
            boxShadow: "0 4px 15px rgba(0, 0, 0, 0.2)",
          }}
          onMouseEnter={(e) => {
            e.currentTarget.style.background = "linear-gradient(145deg, rgba(59, 130, 246, 0.3) 0%, rgba(37, 99, 235, 0.4) 100%)";
            e.currentTarget.style.boxShadow = "0 0 20px rgba(59, 130, 246, 0.4), 0 0 40px rgba(59, 130, 246, 0.2)";
            e.currentTarget.style.transform = "scale(1.1)";
          }}
          onMouseLeave={(e) => {
            e.currentTarget.style.background = "linear-gradient(145deg, rgba(59, 130, 246, 0.2) 0%, rgba(37, 99, 235, 0.3) 100%)";
            e.currentTarget.style.boxShadow = "0 4px 15px rgba(0, 0, 0, 0.2)";
            e.currentTarget.style.transform = "scale(1)";
          }}
        >
          <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
            <path d="M3 3h18v18H3z"/>
            <path d="M9 9h6v6H9z"/>
          </svg>
        </a>
      </div>

      {/* 左上角：正在测试的任务 */}
      <div
        style={{
          position: "absolute",
          top: 140,
          left: 24,
          minWidth: 260,
          background: "rgba(12,28,56,0.88)",
          borderRadius: 12,
          padding: "14px 18px 10px 18px",
          color: "#fff",
          boxShadow: "0 4px 24px #1da1ff33",
          zIndex: 20,
        }}
      >
        <div style={{ fontSize: 17, fontWeight: 600, marginBottom: 8, letterSpacing: 1, color: "#1da1ff" }}>正在测试的任务</div>
        <table style={{ width: "100%", color: "#fff", fontSize: 15, borderSpacing: 0 }}>
          <thead>
            <tr style={{ color: "#1da1ff", fontWeight: 500 }}>
              <th style={{ textAlign: "left", paddingRight: 10 }}>产品编号</th>
              <th style={{ textAlign: "left", paddingRight: 10 }}>测试台</th>
              <th style={{ textAlign: "left" }}>运行时间</th>
            </tr>
          </thead>
          <tbody>
            {testingTasks.map((task) => (
              <tr key={task.id}>
                <td style={{ padding: "2px 10px 2px 0" }}>{task.id}</td>
                <td style={{ padding: "2px 10px 2px 0" }}>{task.station}</td>
                <td style={{ padding: "2px 0" }}>{task.duration}</td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>

      {/* 右上角：正在出入库和充电的任务 */}
      <div
        style={{
          position: "absolute",
          top: 140,
          right: 24,
          minWidth: 260,
          background: "rgba(12,28,56,0.88)",
          borderRadius: 12,
          padding: "14px 18px 10px 18px",
          color: "#fff",
          boxShadow: "0 4px 24px #1da1ff33",
          zIndex: 20,
        }}
      >
        <div style={{ fontSize: 17, fontWeight: 600, marginBottom: 8, letterSpacing: 1, color: "#1da1ff" }}>正在出入库的任务</div>
        <table style={{ width: "100%", color: "#fff", fontSize: 15, borderSpacing: 0, marginBottom: 10 }}>
          <thead>
            <tr style={{ color: "#1da1ff", fontWeight: 500 }}>
              <th style={{ textAlign: "left", paddingRight: 10 }}>任务编号</th>
              <th style={{ textAlign: "left", paddingRight: 10 }}>类型</th>
              <th style={{ textAlign: "left" }}>状态</th>
            </tr>
          </thead>
          <tbody>
            {warehouseTasks.map((task) => (
              <tr key={task.id}>
                <td style={{ padding: "2px 10px 2px 0" }}>{task.id}</td>
                <td style={{ padding: "2px 10px 2px 0" }}>{task.type}</td>
                <td style={{ padding: "2px 0" }}>{task.status}</td>
              </tr>
            ))}
          </tbody>
        </table>
        <div style={{ fontSize: 17, fontWeight: 600, marginBottom: 8, letterSpacing: 1, color: "#1da1ff" }}>正在充电的任务</div>
        <table style={{ width: "100%", color: "#fff", fontSize: 15, borderSpacing: 0 }}>
          <thead>
            <tr style={{ color: "#1da1ff", fontWeight: 500 }}>
                              <th style={{ textAlign: "left", paddingRight: 10 }}>AMR编号</th>
              <th style={{ textAlign: "left", paddingRight: 10 }}>状态</th>
              <th style={{ textAlign: "left" }}>已充时间</th>
            </tr>
          </thead>
          <tbody>
            {chargingTasks.map((task) => (
              <tr key={task.id}>
                <td style={{ padding: "2px 10px 2px 0" }}>{task.id}</td>
                <td style={{ padding: "2px 10px 2px 0" }}>{task.status}</td>
                <td style={{ padding: "2px 0" }}>{task.time}</td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>
    </div>
  );
}
