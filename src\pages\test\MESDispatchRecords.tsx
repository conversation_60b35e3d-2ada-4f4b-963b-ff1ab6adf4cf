import { Card, CardHeader, CardTitle, CardDescription, CardContent } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Network } from "lucide-react";

// 静态MES调度记录数据
const mesRecords = [
  { id: "MES-20240101-001", productId: "A-2024-001", orderNo: "WO-1001", time: "2024-01-15 08:30:00", content: "月稳测试排产", status: "已下发" },
  { id: "MES-20240101-002", productId: "A-2024-002", orderNo: "WO-1002", time: "2024-01-15 09:00:00", content: "性能测试排产", status: "已下发" },
  { id: "MES-20240101-003", productId: "A-2024-003", orderNo: "WO-1003", time: "2024-01-15 09:30:00", content: "老化测试排产", status: "已下发" },
];

export default function MESDispatchRecords() {
  return (
    <div className="min-h-screen bg-white text-gray-900 p-6">
      <div className="max-w-5xl mx-auto space-y-6">
        <h2 className="text-3xl font-bold tracking-tight flex items-center gap-2">
          <Network className="h-7 w-7 text-blue-500" />
          MES调度记录
        </h2>
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">MES调度记录</CardTitle>
            <CardDescription>记录MES下发的排产调度信息</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="overflow-x-auto">
              <table className="min-w-full text-sm border">
                <thead>
                  <tr className="bg-gray-50">
                    <th className="px-3 py-2 border">调度编号</th>
                    <th className="px-3 py-2 border">产品编号</th>
                    <th className="px-3 py-2 border">工单号</th>
                    <th className="px-3 py-2 border">下发时间</th>
                    <th className="px-3 py-2 border">调度内容</th>
                    <th className="px-3 py-2 border">状态</th>
                  </tr>
                </thead>
                <tbody>
                  {mesRecords.map((rec) => (
                    <tr key={rec.id}>
                      <td className="px-3 py-2 border font-mono">{rec.id}</td>
                      <td className="px-3 py-2 border">{rec.productId}</td>
                      <td className="px-3 py-2 border">{rec.orderNo}</td>
                      <td className="px-3 py-2 border">{rec.time}</td>
                      <td className="px-3 py-2 border">{rec.content}</td>
                      <td className="px-3 py-2 border">
                        <Badge variant="secondary">{rec.status}</Badge>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
} 