import { useState, useEffect } from "react";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import {
  Clock,
  Bell,
  Monitor,
  Box,
  AlertCircle,
  CheckCircle,
  Eye,
  Maximize2,
  ArrowLeft,
  TrendingUp,
  PieChart,
  Activity,
  BarChart3,
  Zap,
} from "lucide-react";
import {
  BarChart,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  ResponsiveContainer,
  <PERSON><PERSON><PERSON> as RechartsPieChart,
  Pie,
  Cell,
  LineChart,
  Line,
  AreaChart,
  Area,
  Legend,
} from "recharts";
import monitorImg from "@/asserts/监控.png";
import industrialImg from "@/asserts/工业大屏三维图.png";

interface Notification {
  id: number;
  type: "info" | "warning" | "error" | "success";
  title: string;
  message: string;
  time: string;
  read: boolean;
}

// 柱状图数据 - 测试状态
const testStatusData = [
  { 
    name: "测试台1", 
    设备编号: "GZ2024001", 
    运行时间: 125,  // 分钟
    状态: "测试中"
  },
  { 
    name: "测试台2", 
    设备编号: "GZ2024015", 
    运行时间: 45,
    状态: "测试中"
  },
  { 
    name: "测试台3", 
    设备编号: "GZ2024022", 
    运行时间: 180,
    状态: "测试中"
  },
  { 
    name: "测试台4", 
    设备编号: "GZ2024008", 
    运行时间: 90,
    状态: "测试中"
  },
  { 
    name: "测试台5", 
    设备编号: "GZ2024019", 
    运行时间: 30,
    状态: "准备中"
  },
];

// 饼图数据 - 设备状态分布
const pieChartData = [
  { name: "正常运行", value: 75, color: "#10b981" },
  { name: "维护中", value: 15, color: "#f59e0b" },
  { name: "故障", value: 8, color: "#ef4444" },
  { name: "离线", value: 2, color: "#6b7280" },
];

// 折线图数据 - 每小时测试数量
const lineChartData = [
  { time: "08:00", 完成数量: 12, 计划数量: 15 },
  { time: "09:00", 完成数量: 19, 计划数量: 15 },
  { time: "10:00", 完成数量: 15, 计划数量: 15 },
  { time: "11:00", 完成数量: 22, 计划数量: 15 },
  { time: "12:00", 完成数量: 8, 计划数量: 15 },
  { time: "13:00", 完成数量: 6, 计划数量: 15 },
  { time: "14:00", 完成数量: 18, 计划数量: 15 },
  { time: "15:00", 完成数量: 25, 计划数量: 15 },
  { time: "16:00", 完成数量: 28, 计划数量: 15 },
  { time: "17:00", 完成数量: 16, 计划数量: 15 },
];

  // 面积图数据 - AMR电量趋势
const areaChartData = [
  { time: "12:00", AMR1: 95, AMR2: 88, AMR3: 76 },
  { time: "12:30", AMR1: 92, AMR2: 85, AMR3: 72 },
  { time: "13:00", AMR1: 88, AMR2: 82, AMR3: 68 },
  { time: "13:30", AMR1: 85, AMR2: 78, AMR3: 64 },
  { time: "14:00", AMR1: 82, AMR2: 75, AMR3: 60 },
  { time: "14:30", AMR1: 78, AMR2: 95, AMR3: 95 }, // AMR2和AMR3充电
  { time: "15:00", AMR1: 75, AMR2: 92, AMR3: 88 },
  { time: "15:30", AMR1: 72, AMR2: 89, AMR3: 85 },
];

export default function ComprehensiveDisplay() {
  const [currentTime, setCurrentTime] = useState(new Date());
  const [notifications, setNotifications] = useState<Notification[]>([
    {
      id: 1,
      type: "info",
      title: "系统信息",
      message: "AMR-01 完成充电",
      time: "14:30:15",
      read: false,
    },
    {
      id: 2,
      type: "warning",
      title: "设备告警",
      message: "桁架TRUSS-02 速度异常",
      time: "14:25:30",
      read: false,
    },
    {
      id: 3,
      type: "success",
      title: "任务完成",
      message: "产品P12345测试完成",
      time: "14:20:45",
      read: true,
    },
    {
      id: 4,
      type: "error",
      title: "系统故障",
      message: "测试台-03 连接异常",
      time: "14:15:20",
      read: false,
    },
    {
      id: 5,
      type: "info",
      title: "充电提醒",
      message: "AMR-03 电量低于20%",
      time: "14:10:30",
      read: true,
    },
  ]);

  // 更新时间
  useEffect(() => {
    const timer = setInterval(() => {
      setCurrentTime(new Date());
    }, 1000);

    return () => clearInterval(timer);
  }, []);

  const formatTime = (date: Date) => {
    return date.toLocaleString("zh-CN", {
      year: "numeric",
      month: "2-digit",
      day: "2-digit",
      hour: "2-digit",
      minute: "2-digit",
      second: "2-digit",
      hour12: false,
    });
  };

  const getNotificationIcon = (type: string) => {
    switch (type) {
      case "success":
        return <CheckCircle className="h-3 w-3 text-green-400" />;
      case "warning":
        return <AlertCircle className="h-3 w-3 text-yellow-400" />;
      case "error":
        return <AlertCircle className="h-3 w-3 text-red-400" />;
      default:
        return <Bell className="h-3 w-3 text-blue-400" />;
    }
  };

  const markAsRead = (id: number) => {
    setNotifications(prev =>
      prev.map(notification =>
        notification.id === id
          ? { ...notification, read: true }
          : notification
      )
    );
  };

  const unreadCount = notifications.filter(n => !n.read).length;

  return (
    <div className="h-screen w-screen overflow-hidden bg-slate-900 text-white">
      {/* 返回按钮 - 图标样式 */}
      <Button
        className="absolute top-4 left-4 z-50 bg-black/60 hover:bg-black/80 text-white border-white/20 w-10 h-10 p-0"
        onClick={() => window.location.href = '/industrial'}
      >
        <ArrowLeft className="h-5 w-5" />
      </Button>

      {/* 上半部分 - 高度60% */}
      <div className="h-[60%] flex">
        {/* 左侧 - 三维图像区域 (70%宽度) */}
        <div className="w-[70%] relative">
          <img
            src={industrialImg}
            alt="三维可视化"
            className="w-full h-full object-cover"
          />
          
          {/* 时间显示悬浮在左上角 */}
          <div className="absolute top-4 left-4 z-40">
            <Card className="bg-black/70 border-white/20 backdrop-blur-md">
              <CardContent className="p-4">
                <div className="text-center">
                  <div className="flex items-center gap-2 mb-2">
                    <Clock className="h-5 w-5 text-blue-400" />
                    <span className="text-sm text-blue-200">系统时间</span>
                  </div>
                  <div className="text-2xl font-mono font-bold text-white">
                    {formatTime(currentTime).split(" ")[1]}
                  </div>
                  <div className="text-sm text-blue-200">
                    {formatTime(currentTime).split(" ")[0]}
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* 系统状态指示器悬浮在底部 */}
          <div className="absolute bottom-4 left-1/2 transform -translate-x-1/2 z-40">
            <div className="flex gap-4">
              <Badge className="bg-green-600/80 text-white border-green-400/50 backdrop-blur-sm">
                AMR: 6/8 在线
              </Badge>
              <Badge className="bg-blue-600/80 text-white border-blue-400/50 backdrop-blur-sm">
                桁架: 正常运行
              </Badge>
              <Badge className="bg-purple-600/80 text-white border-purple-400/50 backdrop-blur-sm">
                测试台: 5/7 运行
              </Badge>
              <Badge className="bg-yellow-600/80 text-white border-yellow-400/50 backdrop-blur-sm">
                充电桩: 2/4 使用中
              </Badge>
            </div>
          </div>
        </div>

        {/* 右侧 - 通知和图表区域 (30%宽度) */}
        <div className="w-[30%] flex flex-col">
          {/* 通知区域 - 上半部分 */}
          <div className="h-1/2 p-2">
            <Card className="h-full bg-slate-800/90 border-slate-600/50">
              <CardHeader className="pb-1 pt-2">
                <CardTitle className="text-white flex items-center gap-2 text-sm">
                  <Bell className="h-4 w-4 text-yellow-400" />
                  系统通知
                  {unreadCount > 0 && (
                    <Badge className="bg-red-500 text-white text-xs">
                      {unreadCount}
                    </Badge>
                  )}
                </CardTitle>
              </CardHeader>
              <CardContent className="flex-1 overflow-y-auto p-2">
                <div className="space-y-2">
                  {notifications.map((notification) => (
                    <div
                      key={notification.id}
                      className={`p-2 rounded border transition-all duration-200 cursor-pointer hover:bg-slate-700/50 text-xs ${
                        notification.read
                          ? "bg-slate-700/30 border-slate-600/30"
                          : "bg-slate-600/50 border-slate-500/50"
                      }`}
                      onClick={() => markAsRead(notification.id)}
                    >
                      <div className="flex items-start gap-2">
                        {getNotificationIcon(notification.type)}
                        <div className="flex-1 min-w-0">
                          <div className="flex items-center justify-between mb-1">
                            <h4 className="font-medium text-white text-xs truncate">
                              {notification.title}
                            </h4>
                            <span className="text-xs text-white/60 ml-2">
                              {notification.time}
                            </span>
                          </div>
                          <p className="text-xs text-white/80 line-clamp-2">
                            {notification.message}
                          </p>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </div>

          {/* 测试状态表格 */}
          <div className="h-1/2 p-2">
            <Card className="h-full bg-slate-800/90 border-slate-600/50">
              <CardHeader className="pb-1 pt-2">
                <CardTitle className="text-white flex items-center gap-1 text-base">
                  <TrendingUp className="h-5 w-5 text-blue-400" />
                  测试状态
                </CardTitle>
              </CardHeader>
              <CardContent className="flex-1 p-2">
                <div className="h-full overflow-auto">
                  <table className="w-full">
                    <thead>
                      <tr className="text-sm text-slate-300">
                        <th className="text-left py-2 px-2">测试台</th>
                        <th className="text-left py-2 px-2">设备编号</th>
                        <th className="text-left py-2 px-2">运行时间</th>
                        <th className="text-left py-2 px-2">状态</th>
                      </tr>
                    </thead>
                    <tbody>
                      {testStatusData.map((item, index) => (
                        <tr 
                          key={item.name}
                          className={`text-sm ${index % 2 === 0 ? 'bg-slate-700/30' : 'bg-slate-800/30'}`}
                        >
                          <td className="py-2 px-2 text-white">{item.name}</td>
                          <td className="py-2 px-2 text-white">{item.设备编号}</td>
                          <td className="py-2 px-2 text-white">
                            {Math.floor(item.运行时间 / 60)}小时{item.运行时间 % 60}分钟
                          </td>
                          <td className="py-2 px-2">
                            <Badge className={item.状态 === "测试中" ? "bg-green-500/80" : "bg-yellow-500/80"}>
                              {item.状态}
                            </Badge>
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>

      {/* 下半部分 - 高度40% */}
      <div className="h-[40%] flex">
        {/* 视频监控 - 25%宽度 */}
        <div className="w-1/4 p-2">
          <Card className="h-full bg-slate-800/90 border-slate-600/50">
            <CardHeader className="pb-1 pt-2">
              <CardTitle className="text-white flex items-center justify-between text-base">
                <div className="flex items-center gap-1">
                  <Monitor className="h-5 w-5 text-green-400" />
                  视频监控
                </div>
                <Button variant="ghost" size="sm" className="text-white/60 hover:text-white h-6 px-2">
                  <Maximize2 className="h-4 w-4" />
                </Button>
              </CardTitle>
            </CardHeader>
            <CardContent className="flex-1 p-2">
              <div className="relative bg-black rounded overflow-hidden h-full">
                <img
                  src={monitorImg}
                  alt="监控画面"
                  className="w-full h-full object-cover"
                />
                <div className="absolute top-1 left-1 bg-red-600 text-white px-1 py-0.5 rounded text-sm font-semibold">
                  ● 实时
                </div>
                <div className="absolute bottom-1 right-1 bg-black/70 text-white px-1 py-0.5 rounded text-sm">
                  {formatTime(currentTime).split(" ")[1]}
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* 每小时测试数量折线图 - 25%宽度 */}
        <div className="w-1/4 p-2">
          <Card className="h-full bg-slate-800/90 border-slate-600/50">
            <CardHeader className="pb-1 pt-2">
              <CardTitle className="text-white flex items-center gap-1 text-base">
                <Activity className="h-5 w-5 text-green-400" />
                每小时测试数量
              </CardTitle>
            </CardHeader>
            <CardContent className="flex-1 p-2">
              <div className="w-full h-full">
                <ResponsiveContainer width="100%" height={200}>
                  <LineChart data={lineChartData}>
                    <CartesianGrid strokeDasharray="3 3" stroke="#374151" />
                    <XAxis 
                      dataKey="time" 
                      tick={{ fill: '#ffffff', fontSize: 12 }}
                      axisLine={{ stroke: '#6b7280' }}
                      tickLine={{ stroke: '#6b7280' }}
                    />
                    <YAxis 
                      tick={{ fill: '#ffffff', fontSize: 12 }}
                      axisLine={{ stroke: '#6b7280' }}
                      tickLine={{ stroke: '#6b7280' }}
                    />
                    <Tooltip 
                      contentStyle={{
                        backgroundColor: '#1f2937',
                        border: '1px solid #374151',
                        borderRadius: '6px',
                        color: '#ffffff',
                        fontSize: '12px'
                      }}
                    />
                    <Legend 
                      wrapperStyle={{ color: '#ffffff', fontSize: '12px' }}
                    />
                    <Line 
                      name="完成数量"
                      type="monotone" 
                      dataKey="完成数量" 
                      stroke="#10b981" 
                      strokeWidth={2} 
                      dot={{ fill: '#10b981', r: 3 }}
                      activeDot={{ r: 5, fill: '#10b981' }}
                    />
                    <Line 
                      name="计划数量"
                      type="monotone" 
                      dataKey="计划数量" 
                      stroke="#6b7280" 
                      strokeWidth={2}
                      strokeDasharray="5 5"
                      dot={false}
                    />
                  </LineChart>
                </ResponsiveContainer>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* 设备状态分布饼图 - 25%宽度 */}
        <div className="w-1/4 p-2">
          <Card className="h-full bg-slate-800/90 border-slate-600/50">
            <CardHeader className="pb-1 pt-2">
              <CardTitle className="text-white flex items-center gap-1 text-base">
                <PieChart className="h-5 w-5 text-purple-400" />
                设备状态分布
              </CardTitle>
            </CardHeader>
            <CardContent className="flex-1 p-2">
              <div className="w-full h-full">
                <ResponsiveContainer width="100%" height={200}>
                  <RechartsPieChart>
                    <Pie
                      data={pieChartData}
                      cx="50%"
                      cy="50%"
                      outerRadius={60}
                      dataKey="value"
                      label={({ name, value }) => `${value}%`}
                      labelLine={false}
                    >
                      {pieChartData.map((entry, index) => (
                        <Cell key={`cell-${index}`} fill={entry.color} />
                      ))}
                    </Pie>
                    <Tooltip 
                      contentStyle={{
                        backgroundColor: '#1f2937',
                        border: '1px solid #374151',
                        borderRadius: '6px',
                        color: '#ffffff',
                        fontSize: '12px'
                      }}
                    />
                    <Legend 
                      wrapperStyle={{ color: '#ffffff', fontSize: '12px' }}
                    />
                  </RechartsPieChart>
                </ResponsiveContainer>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* AMR电量趋势面积图 - 25%宽度 */}
        <div className="w-1/4 p-2">
          <Card className="h-full bg-slate-800/90 border-slate-600/50">
            <CardHeader className="pb-1 pt-2">
              <CardTitle className="text-white flex items-center gap-1 text-base">
                <Zap className="h-5 w-5 text-yellow-400" />
                AMR电量趋势
              </CardTitle>
            </CardHeader>
            <CardContent className="flex-1 p-2">
              <div className="w-full h-full">
                <ResponsiveContainer width="100%" height={200}>
                  <AreaChart data={areaChartData}>
                    <CartesianGrid strokeDasharray="3 3" stroke="#374151" />
                    <XAxis 
                      dataKey="time" 
                      tick={{ fill: '#ffffff', fontSize: 12 }}
                      axisLine={{ stroke: '#6b7280' }}
                      tickLine={{ stroke: '#6b7280' }}
                    />
                    <YAxis 
                      tick={{ fill: '#ffffff', fontSize: 12 }}
                      axisLine={{ stroke: '#6b7280' }}
                      tickLine={{ stroke: '#6b7280' }}
                      domain={[0, 100]}
                    />
                    <Tooltip 
                      contentStyle={{
                        backgroundColor: '#1f2937',
                        border: '1px solid #374151',
                        borderRadius: '6px',
                        color: '#ffffff',
                        fontSize: '12px'
                      }}
                    />
                    <Legend 
                      wrapperStyle={{ color: '#ffffff', fontSize: '12px' }}
                    />
                    <Area 
                      type="monotone" 
                      dataKey="AMR1" 
                      stroke="#3b82f6" 
                      fill="#3b82f6" 
                      fillOpacity={0.6} 
                    />
                    <Area 
                      type="monotone" 
                      dataKey="AMR2" 
                      stroke="#10b981" 
                      fill="#10b981" 
                      fillOpacity={0.6} 
                    />
                    <Area 
                      type="monotone" 
                      dataKey="AMR3" 
                      stroke="#f59e0b" 
                      fill="#f59e0b" 
                      fillOpacity={0.6} 
                    />
                  </AreaChart>
                </ResponsiveContainer>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
} 