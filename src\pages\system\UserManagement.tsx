import { useState } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Badge } from "@/components/ui/badge";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import { Switch } from "@/components/ui/switch";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import {
  Plus,
  Search,
  Edit,
  Trash2,
  <PERSON>,
  <PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON>,
} from "lucide-react";

interface User {
  id: string;
  username: string;
  realName: string;
  email: string;
  phone: string;
  department: string;
  role: string;
  status: "active" | "inactive" | "locked";
  lastLogin: string;
  createdAt: string;
  permissions: string[];
}

interface Role {
  id: string;
  name: string;
  description: string;
  permissions: string[];
  userCount: number;
  createdAt: string;
}

const mockUsers: User[] = [
  {
    id: "U001",
    username: "admin",
    realName: "管理员",
    email: "<EMAIL>",
    phone: "138****8888",
    department: "系统管理部",
    role: "系统管理员",
    status: "active",
    lastLogin: "2024-01-15 15:30",
    createdAt: "2023-06-01",
    permissions: ["system_admin", "user_management", "equipment_management"],
  },
  {
    id: "U002",
    username: "zhangsan",
    realName: "张三",
    email: "<EMAIL>",
    phone: "139****9999",
    department: "测试部",
    role: "测试操作员",
    status: "active",
    lastLogin: "2024-01-15 14:20",
    createdAt: "2023-08-15",
    permissions: ["test_operation", "test_monitoring"],
  },
  {
    id: "U003",
    username: "lisi",
    realName: "李四",
    email: "<EMAIL>",
    phone: "136****6666",
    department: "仓储部",
    role: "仓储操作员",
    status: "inactive",
    lastLogin: "2024-01-12 09:15",
    createdAt: "2023-09-01",
    permissions: ["warehouse_operation", "inventory_management"],
  },
];

const mockRoles: Role[] = [
  {
    id: "R001",
    name: "系统管理员",
    description: "拥有系统最高权限，可以管理所有功能",
    permissions: [
      "system_admin",
      "user_management",
      "equipment_management",
      "test_management",
      "warehouse_management",
    ],
    userCount: 1,
    createdAt: "2023-06-01",
  },
  {
    id: "R002",
    name: "测试操作员",
    description: "负责测试设备操作和测试过程监控",
    permissions: ["test_operation", "test_monitoring", "product_management"],
    userCount: 8,
    createdAt: "2023-06-01",
  },
  {
    id: "R003",
    name: "仓储操作员",
    description: "负责产品出入库操作和库存管理",
    permissions: [
      "warehouse_operation",
      "inventory_management",
      "product_query",
    ],
    userCount: 5,
    createdAt: "2023-06-01",
  },
];

const availablePermissions = [
  { id: "system_admin", name: "系统管理", category: "系统" },
  { id: "user_management", name: "用户管理", category: "系统" },
  { id: "equipment_management", name: "设备管理", category: "设备" },
  { id: "test_management", name: "测试管理", category: "测试" },
  { id: "test_operation", name: "测试操作", category: "测试" },
  { id: "test_monitoring", name: "测试监控", category: "测试" },
  { id: "warehouse_management", name: "仓储管理", category: "仓储" },
  { id: "warehouse_operation", name: "出入库操作", category: "仓储" },
  { id: "inventory_management", name: "库存管理", category: "仓储" },
  { id: "product_management", name: "产品管理", category: "产品" },
  { id: "product_query", name: "产品查询", category: "产品" },
];

export default function UserManagement() {
  const [users, setUsers] = useState(mockUsers);
  const [roles, setRoles] = useState(mockRoles);
  const [selectedUser, setSelectedUser] = useState<User | null>(null);

  const getStatusBadge = (status: string) => {
    const statusConfig = {
      active: { label: "正常", variant: "default" as const, icon: UserCheck },
      inactive: { label: "停用", variant: "secondary" as const, icon: UserX },
      locked: { label: "锁定", variant: "destructive" as const, icon: UserX },
    };

    const config = statusConfig[status as keyof typeof statusConfig];
    const Icon = config.icon;
    return (
      <Badge variant={config.variant} className="flex items-center gap-1">
        <Icon className="h-3 w-3" />
        {config.label}
      </Badge>
    );
  };

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h2 className="text-3xl font-bold tracking-tight">系统管理</h2>
      </div>

      <Tabs defaultValue="users" className="space-y-4">
        <TabsList>
          <TabsTrigger value="users">人员管理</TabsTrigger>
          <TabsTrigger value="roles">角色管理</TabsTrigger>
          <TabsTrigger value="permissions">权限管理</TabsTrigger>
          <TabsTrigger value="settings">系统设置</TabsTrigger>
        </TabsList>

        <TabsContent value="users">
          <div className="space-y-6">
            {/* User Management Header */}
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-2">
                <Users className="h-5 w-5" />
                <h3 className="text-lg font-semibold">人员管理</h3>
              </div>
              <Dialog>
                <DialogTrigger asChild>
                  <Button>
                    <Plus className="mr-2 h-4 w-4" />
                    新增用户
                  </Button>
                </DialogTrigger>
                <DialogContent className="max-w-2xl">
                  <DialogHeader>
                    <DialogTitle>新增用户</DialogTitle>
                    <DialogDescription>创建新的系统用户账号</DialogDescription>
                  </DialogHeader>
                  <div className="grid gap-4 py-4">
                    <div className="grid grid-cols-2 gap-4">
                      <div className="space-y-2">
                        <Label htmlFor="newUsername">用户名</Label>
                        <Input id="newUsername" placeholder="用户名" />
                      </div>
                      <div className="space-y-2">
                        <Label htmlFor="newRealName">真实姓名</Label>
                        <Input id="newRealName" placeholder="真实姓名" />
                      </div>
                    </div>
                    <div className="grid grid-cols-2 gap-4">
                      <div className="space-y-2">
                        <Label htmlFor="newEmail">邮箱</Label>
                        <Input
                          id="newEmail"
                          type="email"
                          placeholder="<EMAIL>"
                        />
                      </div>
                      <div className="space-y-2">
                        <Label htmlFor="newPhone">手机号</Label>
                        <Input id="newPhone" placeholder="手机号" />
                      </div>
                    </div>
                    <div className="grid grid-cols-2 gap-4">
                      <div className="space-y-2">
                        <Label htmlFor="newDepartment">所属部门</Label>
                        <Select>
                          <SelectTrigger>
                            <SelectValue placeholder="选择部门" />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="test">测试部</SelectItem>
                            <SelectItem value="warehouse">仓储部</SelectItem>
                            <SelectItem value="equipment">设备部</SelectItem>
                            <SelectItem value="system">系统管理部</SelectItem>
                          </SelectContent>
                        </Select>
                      </div>
                      <div className="space-y-2">
                        <Label htmlFor="newRole">角色</Label>
                        <Select>
                          <SelectTrigger>
                            <SelectValue placeholder="选择角色" />
                          </SelectTrigger>
                          <SelectContent>
                            {roles.map((role) => (
                              <SelectItem key={role.id} value={role.id}>
                                {role.name}
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                      </div>
                    </div>
                    <div className="grid grid-cols-2 gap-4">
                      <div className="space-y-2">
                        <Label htmlFor="newPassword">初始密码</Label>
                        <Input
                          id="newPassword"
                          type="password"
                          placeholder="初始密码"
                        />
                      </div>
                      <div className="space-y-2">
                        <Label htmlFor="confirmPassword">确认密码</Label>
                        <Input
                          id="confirmPassword"
                          type="password"
                          placeholder="确认密码"
                        />
                      </div>
                    </div>
                  </div>
                  <div className="flex justify-end space-x-2">
                    <Button variant="outline">取消</Button>
                    <Button>创建用户</Button>
                  </div>
                </DialogContent>
              </Dialog>
            </div>

            {/* Search and Filter */}
            <Card>
              <CardHeader>
                <CardTitle>用户查询</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid gap-4 md:grid-cols-4">
                  <div className="space-y-2">
                    <Label htmlFor="searchUser">用户名/姓名</Label>
                    <Input id="searchUser" placeholder="搜索用户" />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="filterDepartment">部门</Label>
                    <Select>
                      <SelectTrigger>
                        <SelectValue placeholder="选择部门" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="all">全部</SelectItem>
                        <SelectItem value="test">测试部</SelectItem>
                        <SelectItem value="warehouse">仓储部</SelectItem>
                        <SelectItem value="equipment">设备部</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="filterStatus">状态</Label>
                    <Select>
                      <SelectTrigger>
                        <SelectValue placeholder="选择状态" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="all">全部</SelectItem>
                        <SelectItem value="active">正常</SelectItem>
                        <SelectItem value="inactive">停用</SelectItem>
                        <SelectItem value="locked">锁定</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                  <div className="flex items-end">
                    <Button className="w-full">
                      <Search className="mr-2 h-4 w-4" />
                      查询
                    </Button>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Users Table */}
            <Card>
              <CardHeader>
                <CardTitle>用户列表</CardTitle>
              </CardHeader>
              <CardContent>
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>用户</TableHead>
                      <TableHead>真实姓名</TableHead>
                      <TableHead>联系方式</TableHead>
                      <TableHead>部门</TableHead>
                      <TableHead>角色</TableHead>
                      <TableHead>状态</TableHead>
                      <TableHead>最后登录</TableHead>
                      <TableHead>操作</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {users.map((user) => (
                      <TableRow key={user.id}>
                        <TableCell>
                          <div className="flex items-center space-x-2">
                            <Avatar className="h-8 w-8">
                              <AvatarImage src="" />
                              <AvatarFallback>
                                {user.realName.slice(0, 1)}
                              </AvatarFallback>
                            </Avatar>
                            <div>
                              <div className="font-medium">{user.username}</div>
                              <div className="text-sm text-gray-500">
                                {user.id}
                              </div>
                            </div>
                          </div>
                        </TableCell>
                        <TableCell>{user.realName}</TableCell>
                        <TableCell>
                          <div>
                            <div className="text-sm">{user.email}</div>
                            <div className="text-sm text-gray-500">
                              {user.phone}
                            </div>
                          </div>
                        </TableCell>
                        <TableCell>{user.department}</TableCell>
                        <TableCell>
                          <Badge variant="outline">{user.role}</Badge>
                        </TableCell>
                        <TableCell>{getStatusBadge(user.status)}</TableCell>
                        <TableCell>{user.lastLogin}</TableCell>
                        <TableCell>
                          <div className="flex space-x-2">
                            <Button size="sm" variant="outline">
                              <Edit className="h-3 w-3" />
                            </Button>
                            <Button size="sm" variant="outline">
                              <Key className="h-3 w-3" />
                            </Button>
                            <Button size="sm" variant="outline">
                              <Trash2 className="h-3 w-3" />
                            </Button>
                          </div>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="roles">
          <div className="space-y-6">
            {/* Role Management Header */}
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-2">
                <Shield className="h-5 w-5" />
                <h3 className="text-lg font-semibold">角色管理</h3>
              </div>
              <Dialog>
                <DialogTrigger asChild>
                  <Button>
                    <Plus className="mr-2 h-4 w-4" />
                    新增角色
                  </Button>
                </DialogTrigger>
                <DialogContent className="max-w-2xl">
                  <DialogHeader>
                    <DialogTitle>新增角色</DialogTitle>
                    <DialogDescription>
                      创建新的系统角色并分配权限
                    </DialogDescription>
                  </DialogHeader>
                  <div className="grid gap-4 py-4">
                    <div className="space-y-2">
                      <Label htmlFor="newRoleName">角色名称</Label>
                      <Input id="newRoleName" placeholder="角色名称" />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="newRoleDesc">角色描述</Label>
                      <Input id="newRoleDesc" placeholder="角色描述" />
                    </div>
                    <div className="space-y-2">
                      <Label>权限配置</Label>
                      <div className="border rounded-lg p-4 space-y-4 max-h-60 overflow-y-auto">
                        {Object.entries(
                          availablePermissions.reduce(
                            (acc, perm) => {
                              if (!acc[perm.category]) acc[perm.category] = [];
                              acc[perm.category].push(perm);
                              return acc;
                            },
                            {} as Record<string, typeof availablePermissions>,
                          ),
                        ).map(([category, permissions]) => (
                          <div key={category}>
                            <h4 className="font-medium text-sm mb-2">
                              {category}
                            </h4>
                            <div className="grid grid-cols-2 gap-2">
                              {permissions.map((permission) => (
                                <div
                                  key={permission.id}
                                  className="flex items-center space-x-2"
                                >
                                  <Switch id={permission.id} />
                                  <Label
                                    htmlFor={permission.id}
                                    className="text-sm"
                                  >
                                    {permission.name}
                                  </Label>
                                </div>
                              ))}
                            </div>
                          </div>
                        ))}
                      </div>
                    </div>
                  </div>
                  <div className="flex justify-end space-x-2">
                    <Button variant="outline">取消</Button>
                    <Button>创建角色</Button>
                  </div>
                </DialogContent>
              </Dialog>
            </div>

            {/* Roles Grid */}
            <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
              {roles.map((role) => (
                <Card key={role.id}>
                  <CardHeader>
                    <CardTitle className="flex items-center justify-between">
                      <span>{role.name}</span>
                      <Badge variant="outline">{role.userCount} 人</Badge>
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-4">
                      <p className="text-sm text-gray-600">
                        {role.description}
                      </p>

                      <div>
                        <h4 className="text-sm font-medium mb-2">权限列表</h4>
                        <div className="flex flex-wrap gap-1">
                          {role.permissions.map((permId) => {
                            const permission = availablePermissions.find(
                              (p) => p.id === permId,
                            );
                            return permission ? (
                              <Badge
                                key={permId}
                                variant="secondary"
                                className="text-xs"
                              >
                                {permission.name}
                              </Badge>
                            ) : null;
                          })}
                        </div>
                      </div>

                      <div className="flex space-x-2">
                        <Button size="sm" variant="outline" className="flex-1">
                          <Edit className="mr-1 h-3 w-3" />
                          编辑
                        </Button>
                        <Button size="sm" variant="outline" className="flex-1">
                          <Trash2 className="mr-1 h-3 w-3" />
                          删除
                        </Button>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          </div>
        </TabsContent>

        <TabsContent value="permissions">
          <Card>
            <CardHeader>
              <CardTitle>权限管理</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-6">
                {Object.entries(
                  availablePermissions.reduce(
                    (acc, perm) => {
                      if (!acc[perm.category]) acc[perm.category] = [];
                      acc[perm.category].push(perm);
                      return acc;
                    },
                    {} as Record<string, typeof availablePermissions>,
                  ),
                ).map(([category, permissions]) => (
                  <div key={category}>
                    <h3 className="font-semibold mb-4">{category}权限</h3>
                    <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
                      {permissions.map((permission) => (
                        <Card key={permission.id} className="p-4">
                          <div className="flex items-center justify-between">
                            <div>
                              <h4 className="font-medium">{permission.name}</h4>
                              <p className="text-sm text-gray-500">
                                {permission.id}
                              </p>
                            </div>
                            <Switch />
                          </div>
                        </Card>
                      ))}
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="settings">
          <div className="grid gap-6 md:grid-cols-2">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <Settings className="mr-2 h-4 w-4" />
                  系统基本设置
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="systemName">系统名称</Label>
                  <Input
                    id="systemName"
                      defaultValue="智能存储测试"
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="companyName">公司名称</Label>
                  <Input id="companyName" placeholder="公司名称" />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="adminEmail">管理员邮箱</Label>
                  <Input
                    id="adminEmail"
                    type="email"
                    placeholder="<EMAIL>"
                  />
                </div>
                <div className="flex items-center space-x-2">
                  <Switch id="maintenanceMode" />
                  <Label htmlFor="maintenanceMode">维护模式</Label>
                </div>
                <div className="flex items-center space-x-2">
                  <Switch id="autoBackup" defaultChecked />
                  <Label htmlFor="autoBackup">自动备份</Label>
                </div>
                <Button className="w-full">保存设置</Button>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>安全设置</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="sessionTimeout">会话超时时间(分钟)</Label>
                  <Input id="sessionTimeout" type="number" defaultValue="30" />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="passwordPolicy">密码复杂度</Label>
                  <Select defaultValue="medium">
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="low">低 - 6位以上</SelectItem>
                      <SelectItem value="medium">中 - 8位+字母数字</SelectItem>
                      <SelectItem value="high">
                        高 - 8位+字母数字特殊字符
                      </SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <div className="space-y-2">
                  <Label htmlFor="loginAttempts">最大登录尝试次数</Label>
                  <Input id="loginAttempts" type="number" defaultValue="5" />
                </div>
                <div className="flex items-center space-x-2">
                  <Switch id="twoFactor" />
                  <Label htmlFor="twoFactor">启用双因素认证</Label>
                </div>
                <div className="flex items-center space-x-2">
                  <Switch id="loginLog" defaultChecked />
                  <Label htmlFor="loginLog">记录登录日志</Label>
                </div>
                <Button className="w-full">保存设置</Button>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>数据备份</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-2">
                  <Label>备份策略</Label>
                  <Select defaultValue="daily">
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="hourly">每小时</SelectItem>
                      <SelectItem value="daily">每日</SelectItem>
                      <SelectItem value="weekly">每周</SelectItem>
                      <SelectItem value="monthly">每月</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <div className="space-y-2">
                  <Label htmlFor="backupRetention">备份保留天数</Label>
                  <Input id="backupRetention" type="number" defaultValue="30" />
                </div>
                <div className="space-y-2">
                  <Label>最近备份</Label>
                  <div className="text-sm text-gray-600">
                    2024-01-15 02:00:00
                  </div>
                </div>
                <div className="space-y-2">
                  <Button className="w-full" variant="outline">
                    立即备份
                  </Button>
                  <Button className="w-full" variant="outline">
                    恢复数据
                  </Button>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>系统信息</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-2">
                  <div className="flex justify-between text-sm">
                    <span>系统版本:</span>
                    <span>v1.0.0</span>
                  </div>
                  <div className="flex justify-between text-sm">
                    <span>数据库版本:</span>
                    <span>MySQL 8.0</span>
                  </div>
                  <div className="flex justify-between text-sm">
                    <span>服务器时间:</span>
                    <span>2024-01-15 15:30:25</span>
                  </div>
                  <div className="flex justify-between text-sm">
                    <span>运行时长:</span>
                    <span>15天 3小时</span>
                  </div>
                  <div className="flex justify-between text-sm">
                    <span>活跃用户:</span>
                    <span>8</span>
                  </div>
                </div>
                <Button className="w-full" variant="outline">
                  查看系统日志
                </Button>
              </CardContent>
            </Card>
          </div>
        </TabsContent>
      </Tabs>
    </div>
  );
}
