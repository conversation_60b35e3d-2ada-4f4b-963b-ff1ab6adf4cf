import { useState } from "react";
import { Link, useLocation, useNavigate } from "react-router-dom";
import { cn } from "@/lib/utils";
import { But<PERSON> } from "@/components/ui/button";
import { Separator } from "@/components/ui/separator";
import { Sheet, SheetContent, SheetTrigger } from "@/components/ui/sheet";
import { Badge } from "@/components/ui/badge";
import {
  Home,
  TestTube,
  Settings,
  Package,
  Cog,
  Menu,
  BarChart3,
  Users,
  ClipboardList,
  Database,
  Wrench,
  Archive,
  User,
  Shield,
  Calendar,
  Bell,
  Battery,
  Zap,
  Factory,
  Network,
  Monitor,
  Box,
  Layers,
  FileText,
  Activity,
  HardDrive,
  Server,
  Cpu,
  MemoryStick,
  Info,
  LayoutDashboard,
  Eye,
  X,
  Plus,
  ChevronDown,
  Edit,
  Search,
  List
} from "lucide-react";
import React from "react";

interface LayoutProps {
  children: React.ReactNode;
}

interface TabItem {
  id: string;
  title: string;
  path: string;
  icon?: React.ComponentType<any>;
}

const quickMenuItems = [
  {
    title: "管理首页",
    href: "/",
    icon: Home,
  },
  // {
  //   title: "工业大屏",
  //   href: "/industrial",
  //   icon: LayoutDashboard,
  // },
  {
    title: "测试管理",
    href: "/test/control",
    icon: TestTube,
  },
  {
    title: "出入库管理",
    href: "/warehouse/operations",
    icon: Archive,
  },
  {
    title: "货架管理",
    href: "/equipment/shelves",
    icon: Layers,
  },
  {
    title: "排产管理",
    href: "/test/planning",
    icon: ClipboardList,
  },
  {
    title: "设备管理",
    href: "/equipment/base-info",
    icon: Cog,
  },
  {
    title: "系统管理",
    href: "/system/users",
    icon: Settings,
  },
];

const navigationItems = [
  {
    title: "管理首页",
    href: "/",
    icon: Home,
  },
  // {
  //   title: "大屏监控",
  //   icon: LayoutDashboard,
  //   children: [
  //     { title: "工业大屏监控", href: "/industrial", icon: LayoutDashboard },
  //     { title: "综合测试看板", href: "/test/dashboard", icon: Eye },
  //   ],
  // },
  {
    title: "测试管理",
    icon: TestTube,
    children: [
      { title: "测试控制中心", href: "/test/control", icon: Settings },
      { title: "测试计划管理", href: "/test/planning", icon: ClipboardList },
      { title: "测试监控", href: "/test/monitoring", icon: Monitor },
      { title: "数据分析查询", href: "/test/analysis", icon: BarChart3 },
    ],
  },
  {
    title: "产品管理",
    icon: Package,
    children: [
      { title: "产品台账", href: "/products/ledger", icon: List },
      { title: "产品盘点", href: "/products/inventory", icon: Search }
    ],
  },
  {
    title: "设备管理",
    icon: Cog,
    children: [
             { title: "设备基准信息", href: "/equipment/base-info", icon: Database },
       { title: "AMR充电管理", href: "/equipment/charging", icon: Battery },
       { title: "三色灯控制", href: "/equipment/lights", icon: Bell },
       // { title: "年度保养计划", href: "/equipment/maintenance", icon: Calendar },
       // { title: "数据分析", href: "/equipment/analysis", icon: BarChart3 },
       { title: "设备数据", href: "/equipment/data", icon: Wrench },
       { title: "货架管理", href: "/equipment/shelves", icon: Layers },
    ],
  },
  {
    title: "出入库管理",
    icon: Archive,
    children: [
      { title: "出入库操作", href: "/warehouse/operations", icon: Package },
      { title: "出入库记录", href: "/warehouse/records", icon: ClipboardList },
    ],
  },
  {
    title: "特殊产品管理",
    icon: Package,
    children: [
      { title: "特殊产品台账", href: "/special-products/inventory", icon: ClipboardList },
      { title: "特殊产品入库登记", href: "/special-products/inbound", icon: Package },
      { title: "特殊产品出库", href: "/special-products/outbound", icon: Archive },
    ],
  },
  {
    title: "系统管理",
    icon: Settings,
    children: [
      { title: "人员管理", href: "/system/users", icon: Users },
      { title: "角色管理", href: "/system/roles", icon: Shield },
      { title: "系统设置", href: "/system/settings", icon: Settings },
      { title: "系统日志", href: "/system/logs", icon: FileText },
      { title: "数字孪生", href: "/system/digital-twin", icon: Network },
    ],
  },
  {
    title: "系统运维",
    icon: Activity,
    children: [
      { title: "性能监控", href: "/operation/performance", icon: Monitor },
      { title: "Redis监控", href: "/operation/redis", icon: Database },
      { title: "Tomcat监控", href: "/operation/tomcat", icon: Server },
      { title: "JVM监控", href: "/operation/jvm", icon: Cpu },
      { title: "请求追踪", href: "/operation/tracing", icon: Activity },
      { title: "磁盘监控", href: "/operation/disk", icon: HardDrive },
    ],
  },
];

// 页面标题映射
const pageTitles: Record<string, string> = {
  "/": "管理首页",
  "/industrial": "工业大屏监控",
  "/test": "测试管理",
  "/test/control": "测试控制中心",
  "/test/planning": "测试计划管理",
  "/test/monitoring": "测试监控",
  "/test/analysis": "数据分析查询",
  "/test/dashboard": "综合测试看板",
  "/test/products": "产品管理",
  "/products/inventory": "产品盘点",
  "/products/ledger": "产品台账",
  "/equipment/base-info": "设备基准信息",
  "/equipment/charging": "AMR充电管理",
     "/equipment/lights": "三色灯控制",
   // "/equipment/maintenance": "年度保养计划",
   // "/equipment/analysis": "设备数据分析",
   "/equipment/data": "设备数据",
  "/warehouse/operations": "出入库操作",
  "/warehouse/records": "出入库记录",
  "/special-products/inventory": "特殊产品台账",
  "/special-products/inbound": "特殊产品入库登记",
  "/special-products/outbound": "特殊产品出库",
  "/system/users": "人员管理",
  "/system/roles": "角色管理",
  "/equipment/shelves": "货架管理",
  "/system/settings": "系统设置",
  "/system/logs": "系统日志",
  "/system/digital-twin": "数字孪生",
  "/operation/performance": "性能监控",
  "/operation/redis": "Redis监控",
  "/operation/tomcat": "Tomcat监控",
  "/operation/jvm": "JVM监控",
  "/operation/tracing": "请求追踪",
  "/operation/disk": "磁盘监控",
};

export default function Layout({ children }: LayoutProps) {
  const [sidebarOpen, setSidebarOpen] = useState(false);
  const [expandedSections, setExpandedSections] = useState<string[]>([]);
  const [openTabs, setOpenTabs] = useState<TabItem[]>([
    { id: "home", title: "管理首页", path: "/" }
  ]);
  const [activeTab, setActiveTab] = useState("home");

  const location = useLocation();
  const navigate = useNavigate();

  const toggleSection = (title: string) => {
    setExpandedSections(prev =>
      prev.includes(title)
        ? prev.filter(section => section !== title)
        : [...prev, title]
    );
  };

  // 添加新标签页
  const addTab = (path: string) => {
    const title = pageTitles[path] || "未知页面";
    const tabId = `tab-${Date.now()}`;
    
    // 检查是否已经存在相同的路径
    const existingTab = openTabs.find(tab => tab.path === path);
    if (existingTab) {
      setActiveTab(existingTab.id);
      return;
    }

    const newTab: TabItem = {
      id: tabId,
      title,
      path
    };

    setOpenTabs(prev => [...prev, newTab]);
    setActiveTab(tabId);
  };

  // 关闭标签页
  const closeTab = (tabId: string) => {
    const tabIndex = openTabs.findIndex(tab => tab.id === tabId);
    if (tabIndex === -1) return;

    const newTabs = openTabs.filter(tab => tab.id !== tabId);
    
    // 如果关闭的是当前活动标签页，需要切换到其他标签页
    if (activeTab === tabId) {
      if (newTabs.length > 0) {
        const nextTab = newTabs[Math.min(tabIndex, newTabs.length - 1)];
        setActiveTab(nextTab.id);
        navigate(nextTab.path);
      } else {
        // 如果没有其他标签页了，回到首页
        setActiveTab("home");
        navigate("/");
      }
    }

    setOpenTabs(newTabs);
  };

  // 关闭所有标签页
  const closeAllTabs = () => {
    setOpenTabs([{ id: "home", title: "管理首页", path: "/" }]);
    setActiveTab("home");
    navigate("/");
  };

  // 切换标签页
  const switchTab = (tabId: string) => {
    const tab = openTabs.find(t => t.id === tabId);
    if (tab) {
      setActiveTab(tabId);
      navigate(tab.path);
    }
  };

  // 监听路由变化，自动添加标签页
  React.useEffect(() => {
    if (location.pathname !== "/") {
      addTab(location.pathname);
    }
  }, [location.pathname]);

  const QuickNavigation = () => (
    <div className="blue-tech-card border-b border-[#4e73df]/20 shadow-sm" style={{background: 'linear-gradient(135deg, #4e73df 0%, #3a5fcd 100%)'}}>
      <div className="mx-auto px-4" style={{ padding: '0.5rem 0 0.5rem 1.5rem',maxWidth: '98rem' }}>
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-3">
            {quickMenuItems.map((item) => (
              <Button
                key={item.title}
                variant="ghost"
                onClick={() => navigate(item.href)}
                className={cn(
                  "flex flex-col items-center space-y-2 px-6 py-9 rounded-lg transition-all duration-300",
                  "text-white min-w-[140px] justify-center",
                  "backdrop-blur-sm",
                  "bg-white/10 hover:bg-white/20 border border-white/20 hover:border-white/40",
                  location.pathname === item.href
                    ? "bg-white/25 border-white/50 shadow-lg"
                    : "",
                )}
              >
                <item.icon style={{ width: '25px', height: '25px' }} />
                <span className="text-md font-medium" style={{ lineHeight: '0.1rem',fontWeight: 'bold' }}>{item.title}</span>
              </Button>
            ))}
          </div>
          <div className="flex items-center space-x-2">
            <Button variant="ghost" size="icon" className="text-white bg-white/10 hover:bg-white/20 border border-white/20 hover:border-white/40">
              <Bell className="h-5 w-5" />
            </Button>
            <Button variant="ghost" size="icon" className="text-white bg-white/10 hover:bg-white/20 border border-white/20 hover:border-white/40">
              <User className="h-5 w-5" />
            </Button>
          </div>
        </div>
      </div>
    </div>
  );

  const Sidebar = () => (
    <div className="flex h-full flex-col blue-tech-sidebar">
      <div style={{ padding: '1rem 1.4rem' }} className="relative overflow-hidden bg-gradient-to-r from-[#4e73df] to-[#3a5fcd]">
        <div className="absolute inset-0 bg-gradient-to-r from-[#4e73df]/80 to-[#3a5fcd]/80"></div>
        <div className="absolute top-0 right-0 w-20 h-20 bg-gradient-to-br from-white/10 to-white/5 rounded-full -translate-y-10 translate-x-10"></div>
        <div className="absolute bottom-0 left-0 w-16 h-16 bg-gradient-to-tr from-white/5 to-white/10 rounded-full translate-y-8 -translate-x-8"></div>
        <div className="relative z-10 flex items-center space-x-3">
          <Box className="h-8 w-8 text-white" />
          <h2 className="text-[1.4rem] font-bold text-white">惯组智能化存<br/>储及测试系统</h2>
        </div>
      </div>
      <Separator className="bg-gradient-to-r from-white/30 to-white/20 h-0.5" />
      <nav className="flex-1 space-y-2 p-4" style={{color: 'white'}}>
        {navigationItems.map((item) => (
          <div key={item.title}>
            {item.children ? (
              <div>
                <button
                  onClick={() => toggleSection(item.title)}
                  className={cn(
                    "blue-tech-nav-item w-full flex items-center justify-between px-3 py-2 text-sm font-medium transition-colors",
                    expandedSections.includes(item.title) ? "active" : ""
                  )}
                >
                  <div className="flex items-center space-x-2">
                    <item.icon className="h-4 w-4" />
                    <span>{item.title}</span>
                  </div>
                  <ChevronDown
                    className={cn(
                      "h-4 w-4 transition-transform",
                      expandedSections.includes(item.title) ? "rotate-180" : ""
                    )}
                  />
                </button>
                {expandedSections.includes(item.title) && (
                  <div className="ml-4 mt-2 space-y-1">
                    {item.children.map((child) => (
                      <Link
                        key={child.href}
                        to={child.href}
                        className={cn(
                          "blue-tech-nav-item flex items-center space-x-2 px-3 py-2 text-sm transition-colors",
                          location.pathname === child.href ? "active" : ""
                        )}
                      >
                        <child.icon className="h-4 w-4" />
                        <span>{child.title}</span>
                      </Link>
                    ))}
                  </div>
                )}
              </div>
            ) : (
              <Link
                to={item.href}
                className={cn(
                  "blue-tech-nav-item flex items-center space-x-2 px-3 py-2 text-sm font-medium transition-colors",
                  location.pathname === item.href ? "active" : ""
                )}
              >
                <item.icon className="h-4 w-4" />
                <span>{item.title}</span>
              </Link>
            )}
          </div>
        ))}
      </nav>
    </div>
  );

  // Tab菜单组件
  const TabMenu = () => (
    <div className="bg-white border-b border-slate-200">
      <div className="flex items-center justify-between px-4 py-2">
        <div className="flex items-center space-x-1 overflow-x-auto">
          {openTabs.map((tab) => (
            <div
              key={tab.id}
              className={cn(
                "flex items-center space-x-2 px-3 py-2 rounded-t-lg border-b-2 transition-all cursor-pointer min-w-fit",
                activeTab === tab.id
                  ? "bg-blue-50 border-blue-500 text-blue-700"
                  : "bg-slate-50 border-transparent text-slate-600 hover:bg-slate-100"
              )}
              onClick={() => switchTab(tab.id)}
            >
              <span className="text-sm font-medium truncate max-w-32">{tab.title}</span>
              {tab.id !== "home" && (
                <button
                  onClick={(e) => {
                    e.stopPropagation();
                    closeTab(tab.id);
                  }}
                  className="ml-1 p-0.5 rounded hover:bg-slate-200 transition-colors"
                >
                  <X className="h-3 w-3" />
                </button>
              )}
            </div>
          ))}
        </div>
        {openTabs.length > 1 && (
          <Button
            variant="ghost"
            size="sm"
            onClick={closeAllTabs}
            className="text-slate-500 hover:text-slate-700"
          >
            全部关闭
          </Button>
        )}
      </div>
    </div>
  );

  return (
    <div className="flex h-screen bg-slate-50">
      {/* Sidebar */}
      <div className="hidden md:flex md:w-64 md:flex-col">
        <Sidebar />
      </div>

      {/* Mobile sidebar */}
      <Sheet open={sidebarOpen} onOpenChange={setSidebarOpen}>
        <SheetContent side="left" className="p-0">
          <Sidebar />
        </SheetContent>
      </Sheet>

      {/* Main content */}
      <div className="flex flex-1 flex-col overflow-hidden">
            {/* Quick Navigation */}
        <QuickNavigation />
        {/* Header */}
        {/* <header className="blue-tech-card border-b border-[#4e73df]/20 p-4" style={{background: 'linear-gradient(135deg, #4e73df 0%, #3a5fcd 100%)'}}>
         
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <Button
                variant="ghost"
                size="icon"
                className="md:hidden text-white bg-white/10 hover:bg-white/20 border border-white/20 hover:border-white/40"
                onClick={() => setSidebarOpen(true)}
              >
                <Menu className="h-5 w-5" />
              </Button>
            </div>
          </div>
       
        </header> */}

       

        {/* Tab Menu */}
        <TabMenu />

        {/* Page content */}
        <main className="flex-1 overflow-auto p-6">
          {children}
        </main>
      </div>
    </div>
  );
}
