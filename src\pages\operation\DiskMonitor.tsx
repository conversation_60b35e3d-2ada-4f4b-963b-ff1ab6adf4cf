import { useState, useEffect } from "react";
import {
  <PERSON>,
  CardContent,
  CardDescription,
  Card<PERSON>eader,
  Card<PERSON><PERSON>le,
} from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs";
import { StatusChart } from "@/components/StatusChart";
import { Progress } from "@/components/ui/progress";
import {
  HardDrive,
  Activity,
  Clock,
  RefreshCw,
  Download,
  AlertTriangle,
  CheckCircle,
  TrendingUp,
  TrendingDown,
  Zap,
  Database,
  FileText,
  Settings,
  Gauge,
  Layers,
  Package,
  Trash2,
  Folder,
  File,
  Archive,
} from "lucide-react";

interface DiskMetrics {
  timestamp: string;
  disks: {
    device: string;
    mountPoint: string;
    fileSystem: string;
    totalSize: number;
    usedSize: number;
    availableSize: number;
    usagePercent: number;
    inodes: {
      total: number;
      used: number;
      available: number;
      usagePercent: number;
    };
  }[];
  ioStats: {
    device: string;
    readBytes: number;
    writeBytes: number;
    readCount: number;
    writeCount: number;
    readTime: number;
    writeTime: number;
    ioTime: number;
    weightedIoTime: number;
  }[];
  performance: {
    readSpeed: number;
    writeSpeed: number;
    iops: number;
    latency: number;
    queueDepth: number;
  };
  fileSystem: {
    totalFiles: number;
    totalDirectories: number;
    largestDirectories: {
      path: string;
      size: number;
      fileCount: number;
    }[];
    fileTypes: {
      type: string;
      count: number;
      size: number;
    }[];
  };
}

interface DiskAlert {
  id: string;
  type: "warning" | "error" | "info";
  message: string;
  timestamp: string;
  severity: "low" | "medium" | "high";
  resolved: boolean;
}

export default function DiskMonitor() {
  const [metrics, setMetrics] = useState<DiskMetrics[]>([]);
  const [currentMetrics, setCurrentMetrics] = useState<DiskMetrics | null>(null);
  const [alerts, setAlerts] = useState<DiskAlert[]>([]);
  const [selectedDisk, setSelectedDisk] = useState<string>("all");
  const [isAutoRefresh, setIsAutoRefresh] = useState(true);

  // 模拟磁盘数据
  useEffect(() => {
    const generateMetrics = (): DiskMetrics => ({
      timestamp: new Date().toLocaleString(),
      disks: [
        {
          device: "/dev/sda",
          mountPoint: "/",
          fileSystem: "ext4",
          totalSize: 1000000000000, // 1TB
          usedSize: 600000000000 + Math.random() * 200000000000, // 600-800GB
          availableSize: 400000000000 - Math.random() * 200000000000, // 200-400GB
          usagePercent: 60 + Math.random() * 20,
          inodes: {
            total: 100000000,
            used: 50000000 + Math.random() * 20000000,
            available: 50000000 - Math.random() * 20000000,
            usagePercent: 50 + Math.random() * 20,
          },
        },
        {
          device: "/dev/sdb",
          mountPoint: "/data",
          fileSystem: "xfs",
          totalSize: 2000000000000, // 2TB
          usedSize: 1200000000000 + Math.random() * 400000000000, // 1.2-1.6TB
          availableSize: 800000000000 - Math.random() * 400000000000, // 400-800GB
          usagePercent: 60 + Math.random() * 20,
          inodes: {
            total: 200000000,
            used: 100000000 + Math.random() * 40000000,
            available: 100000000 - Math.random() * 40000000,
            usagePercent: 50 + Math.random() * 20,
          },
        },
        {
          device: "/dev/sdc",
          mountPoint: "/backup",
          fileSystem: "ext4",
          totalSize: 500000000000, // 500GB
          usedSize: 300000000000 + Math.random() * 100000000000, // 300-400GB
          availableSize: 200000000000 - Math.random() * 100000000000, // 100-200GB
          usagePercent: 60 + Math.random() * 20,
          inodes: {
            total: 50000000,
            used: 25000000 + Math.random() * 10000000,
            available: 25000000 - Math.random() * 10000000,
            usagePercent: 50 + Math.random() * 20,
          },
        },
      ],
      ioStats: [
        {
          device: "/dev/sda",
          readBytes: 1000000000000 + Math.random() * 100000000000,
          writeBytes: 500000000000 + Math.random() * 50000000000,
          readCount: 10000000 + Math.random() * 1000000,
          writeCount: 5000000 + Math.random() * 500000,
          readTime: 5000000 + Math.random() * 500000,
          writeTime: 3000000 + Math.random() * 300000,
          ioTime: 8000000 + Math.random() * 800000,
          weightedIoTime: 10000000 + Math.random() * 1000000,
        },
        {
          device: "/dev/sdb",
          readBytes: 2000000000000 + Math.random() * 200000000000,
          writeBytes: 1000000000000 + Math.random() * 100000000000,
          readCount: 20000000 + Math.random() * 2000000,
          writeCount: 10000000 + Math.random() * 1000000,
          readTime: 10000000 + Math.random() * 1000000,
          writeTime: 6000000 + Math.random() * 600000,
          ioTime: 16000000 + Math.random() * 1600000,
          weightedIoTime: 20000000 + Math.random() * 2000000,
        },
        {
          device: "/dev/sdc",
          readBytes: 500000000000 + Math.random() * 50000000000,
          writeBytes: 250000000000 + Math.random() * 25000000000,
          readCount: 5000000 + Math.random() * 500000,
          writeCount: 2500000 + Math.random() * 250000,
          readTime: 2500000 + Math.random() * 250000,
          writeTime: 1500000 + Math.random() * 150000,
          ioTime: 4000000 + Math.random() * 400000,
          weightedIoTime: 5000000 + Math.random() * 500000,
        },
      ],
      performance: {
        readSpeed: 100 + Math.random() * 50,
        writeSpeed: 50 + Math.random() * 25,
        iops: 1000 + Math.random() * 500,
        latency: 5 + Math.random() * 10,
        queueDepth: 10 + Math.random() * 20,
      },
      fileSystem: {
        totalFiles: 10000000 + Math.random() * 1000000,
        totalDirectories: 500000 + Math.random() * 50000,
        largestDirectories: [
          {
            path: "/data/logs",
            size: 50000000000 + Math.random() * 10000000000,
            fileCount: 100000 + Math.random() * 10000,
          },
          {
            path: "/data/database",
            size: 300000000000 + Math.random() * 50000000000,
            fileCount: 50000 + Math.random() * 5000,
          },
          {
            path: "/data/cache",
            size: 100000000000 + Math.random() * 20000000000,
            fileCount: 200000 + Math.random() * 20000,
          },
        ],
        fileTypes: [
          { type: "log", count: 500000, size: 50000000000 },
          { type: "database", count: 1000, size: 300000000000 },
          { type: "image", count: 100000, size: 20000000000 },
          { type: "document", count: 50000, size: 10000000000 },
          { type: "archive", count: 10000, size: 5000000000 },
        ],
      },
    });

    const updateMetrics = () => {
      const newMetrics = generateMetrics();
      setCurrentMetrics(newMetrics);
      setMetrics(prev => [...prev.slice(-59), newMetrics]);
    };

    updateMetrics();
    const interval = setInterval(updateMetrics, 5000);

    return () => clearInterval(interval);
  }, []);

  // 模拟告警数据
  useEffect(() => {
    const mockAlerts: DiskAlert[] = [
      {
        id: "1",
        type: "warning",
        message: "磁盘 /dev/sda 使用率超过80%",
        timestamp: "2024-01-15 15:30:00",
        severity: "medium",
        resolved: false,
      },
      {
        id: "2",
        type: "error",
        message: "磁盘 /dev/sdb 空间不足",
        timestamp: "2024-01-15 15:25:00",
        severity: "high",
        resolved: false,
      },
      {
        id: "3",
        type: "info",
        message: "磁盘 I/O 性能下降",
        timestamp: "2024-01-15 15:20:00",
        severity: "low",
        resolved: true,
      },
    ];
    setAlerts(mockAlerts);
  }, []);

  const getStatusBadge = (usage: number) => {
    if (usage >= 90) {
      return <Badge className="bg-red-100 text-red-800 border-red-200">严重</Badge>;
    }
    if (usage >= 80) {
      return <Badge className="bg-yellow-100 text-yellow-800 border-yellow-200">警告</Badge>;
    }
    return <Badge className="bg-green-100 text-green-800 border-green-200">正常</Badge>;
  };

  const getAlertIcon = (type: string) => {
    switch (type) {
      case "error":
        return <AlertTriangle className="h-4 w-4 text-red-600" />;
      case "warning":
        return <AlertTriangle className="h-4 w-4 text-yellow-600" />;
      case "info":
        return <CheckCircle className="h-4 w-4 text-blue-600" />;
      default:
        return <CheckCircle className="h-4 w-4 text-gray-600" />;
    }
  };

  const formatBytes = (bytes: number) => {
    const sizes = ['B', 'KB', 'MB', 'GB', 'TB'];
    if (bytes === 0) return '0 B';
    const i = Math.floor(Math.log(bytes) / Math.log(1024));
    return `${(bytes / Math.pow(1024, i)).toFixed(2)} ${sizes[i]}`;
  };

  if (!currentMetrics) return <div>加载中...</div>;

  const totalUsage = currentMetrics.disks.reduce((sum, disk) => sum + disk.usagePercent, 0) / currentMetrics.disks.length;
  const totalIOPS = currentMetrics.performance.iops;

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <div>
          <h2 className="text-3xl font-bold tracking-tight">磁盘监控</h2>
          <p className="text-muted-foreground">
            监控磁盘使用情况、I/O性能、文件系统状态和存储分析
          </p>
        </div>
        <div className="flex items-center space-x-3">
          <Button
            variant="outline"
            size="sm"
            onClick={() => setIsAutoRefresh(!isAutoRefresh)}
          >
            <RefreshCw className={`mr-2 h-4 w-4 ${isAutoRefresh ? 'animate-spin' : ''}`} />
            {isAutoRefresh ? '自动刷新' : '手动刷新'}
          </Button>
          <Button variant="outline" size="sm">
            <Download className="mr-2 h-4 w-4" />
            导出报告
          </Button>
        </div>
      </div>

      {/* 关键指标卡片 */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">平均磁盘使用率</CardTitle>
            <HardDrive className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className={`text-2xl font-bold ${totalUsage > 80 ? 'text-red-600' : totalUsage > 60 ? 'text-yellow-600' : 'text-green-600'}`}>
              {totalUsage.toFixed(1)}%
            </div>
            <div className="flex items-center justify-between mt-2">
              <Progress value={totalUsage} className="flex-1 mr-2" />
              {getStatusBadge(totalUsage)}
            </div>
            <p className="text-xs text-muted-foreground mt-2">
              磁盘数量: {currentMetrics.disks.length}
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">总IOPS</CardTitle>
            <Activity className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-blue-600">
              {totalIOPS.toFixed(0)}
            </div>
            <div className="flex items-center justify-between mt-2">
              <Progress value={Math.min(totalIOPS / 2000 * 100, 100)} className="flex-1 mr-2" />
              <Badge className="bg-blue-100 text-blue-800 border-blue-200">正常</Badge>
            </div>
            <p className="text-xs text-muted-foreground mt-2">
              读取: {currentMetrics.performance.readSpeed.toFixed(1)} MB/s
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">写入速度</CardTitle>
            <TrendingUp className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-green-600">
              {currentMetrics.performance.writeSpeed.toFixed(1)} MB/s
            </div>
            <div className="flex items-center justify-between mt-2">
              <Progress value={Math.min(currentMetrics.performance.writeSpeed / 100 * 100, 100)} className="flex-1 mr-2" />
              <Badge className="bg-green-100 text-green-800 border-green-200">正常</Badge>
            </div>
            <p className="text-xs text-muted-foreground mt-2">
              延迟: {currentMetrics.performance.latency.toFixed(1)}ms
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">文件总数</CardTitle>
            <FileText className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-purple-600">
              {currentMetrics.fileSystem.totalFiles.toLocaleString()}
            </div>
            <div className="flex items-center justify-between mt-2">
              <Progress value={Math.min(currentMetrics.fileSystem.totalFiles / 20000000 * 100, 100)} className="flex-1 mr-2" />
              <Badge className="bg-purple-100 text-purple-800 border-purple-200">活跃</Badge>
            </div>
            <p className="text-xs text-muted-foreground mt-2">
              目录: {currentMetrics.fileSystem.totalDirectories.toLocaleString()}
            </p>
          </CardContent>
        </Card>
      </div>

      {/* 主要内容区域 */}
      <Tabs defaultValue="overview" className="space-y-4">
        <TabsList>
          <TabsTrigger value="overview">概览</TabsTrigger>
          <TabsTrigger value="disks">磁盘状态</TabsTrigger>
          <TabsTrigger value="performance">性能监控</TabsTrigger>
          <TabsTrigger value="filesystem">文件系统</TabsTrigger>
          <TabsTrigger value="alerts">告警信息</TabsTrigger>
        </TabsList>

        {/* 概览 */}
        <TabsContent value="overview" className="space-y-4">
          <div className="grid gap-4 md:grid-cols-2">
            <Card>
              <CardHeader>
                <CardTitle>磁盘使用趋势</CardTitle>
                <CardDescription>各磁盘使用率变化</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="h-80">
                  <StatusChart
                    type="line"
                    data={metrics.map(m => ({
                      time: m.timestamp,
                      sda: m.disks[0]?.usagePercent || 0,
                      sdb: m.disks[1]?.usagePercent || 0,
                      sdc: m.disks[2]?.usagePercent || 0,
                    }))}
                    dataKey="sda"
                    nameKey="time"
                    colors={["#ef4444", "#3b82f6", "#10b981"]}
                  />
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>I/O性能趋势</CardTitle>
                <CardDescription>读写速度和IOPS变化</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="h-80">
                  <StatusChart
                    type="line"
                    data={metrics.map(m => ({
                      time: m.timestamp,
                      readSpeed: m.performance.readSpeed,
                      writeSpeed: m.performance.writeSpeed,
                      iops: m.performance.iops / 100,
                    }))}
                    dataKey="readSpeed"
                    nameKey="time"
                    colors={["#3b82f6", "#ef4444", "#f59e0b"]}
                  />
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        {/* 磁盘状态 */}
        <TabsContent value="disks" className="space-y-4">
          <div className="grid gap-4 md:grid-cols-2">
            <Card>
              <CardHeader>
                <CardTitle>磁盘使用情况</CardTitle>
                <CardDescription>各磁盘的详细使用信息</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {currentMetrics.disks.map((disk) => (
                    <div key={disk.device} className="space-y-3">
                      <div className="flex items-center justify-between">
                        <div>
                          <p className="font-medium">{disk.device}</p>
                          <p className="text-sm text-muted-foreground">
                            {disk.mountPoint} ({disk.fileSystem})
                          </p>
                        </div>
                        {getStatusBadge(disk.usagePercent)}
                      </div>
                      <div className="space-y-2">
                        <div className="flex justify-between text-sm">
                          <span>使用率</span>
                          <span>{disk.usagePercent.toFixed(1)}%</span>
                        </div>
                        <Progress value={disk.usagePercent} />
                        <div className="grid grid-cols-3 gap-2 text-xs text-muted-foreground">
                          <div>
                            <span>总容量:</span>
                            <p className="font-medium">{formatBytes(disk.totalSize)}</p>
                          </div>
                          <div>
                            <span>已使用:</span>
                            <p className="font-medium">{formatBytes(disk.usedSize)}</p>
                          </div>
                          <div>
                            <span>可用:</span>
                            <p className="font-medium">{formatBytes(disk.availableSize)}</p>
                          </div>
                        </div>
                      </div>
                      <div className="pt-2 border-t">
                        <div className="flex justify-between text-sm">
                          <span>Inode使用率</span>
                          <span>{disk.inodes.usagePercent.toFixed(1)}%</span>
                        </div>
                        <Progress value={disk.inodes.usagePercent} />
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>I/O统计</CardTitle>
                <CardDescription>各磁盘的I/O操作统计</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {currentMetrics.ioStats.map((io) => (
                    <div key={io.device} className="space-y-3">
                      <div className="flex items-center justify-between">
                        <p className="font-medium">{io.device}</p>
                        <Badge className="bg-blue-100 text-blue-800 border-blue-200">
                          {Math.round((io.readCount + io.writeCount) / 1000)}K ops
                        </Badge>
                      </div>
                      <div className="grid grid-cols-2 gap-4 text-sm">
                        <div>
                          <label className="text-muted-foreground">读取</label>
                          <p className="font-semibold">{formatBytes(io.readBytes)}</p>
                          <p className="text-xs text-muted-foreground">
                            {io.readCount.toLocaleString()} 次
                          </p>
                        </div>
                        <div>
                          <label className="text-muted-foreground">写入</label>
                          <p className="font-semibold">{formatBytes(io.writeBytes)}</p>
                          <p className="text-xs text-muted-foreground">
                            {io.writeCount.toLocaleString()} 次
                          </p>
                        </div>
                      </div>
                      <div className="grid grid-cols-2 gap-4 text-sm">
                        <div>
                          <label className="text-muted-foreground">读取时间</label>
                          <p className="font-semibold">{(io.readTime / 1000).toFixed(1)}s</p>
                        </div>
                        <div>
                          <label className="text-muted-foreground">写入时间</label>
                          <p className="font-semibold">{(io.writeTime / 1000).toFixed(1)}s</p>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        {/* 性能监控 */}
        <TabsContent value="performance" className="space-y-4">
          <div className="grid gap-4 md:grid-cols-2">
            <Card>
              <CardHeader>
                <CardTitle>I/O性能指标</CardTitle>
                <CardDescription>磁盘I/O性能详细分析</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <label className="text-sm font-medium">读取速度</label>
                      <p className="text-2xl font-bold">{currentMetrics.performance.readSpeed.toFixed(1)} MB/s</p>
                    </div>
                    <div>
                      <label className="text-sm font-medium">写入速度</label>
                      <p className="text-2xl font-bold">{currentMetrics.performance.writeSpeed.toFixed(1)} MB/s</p>
                    </div>
                    <div>
                      <label className="text-sm font-medium">IOPS</label>
                      <p className="text-2xl font-bold">{currentMetrics.performance.iops.toFixed(0)}</p>
                    </div>
                    <div>
                      <label className="text-sm font-medium">平均延迟</label>
                      <p className="text-2xl font-bold">{currentMetrics.performance.latency.toFixed(1)}ms</p>
                    </div>
                  </div>
                  <div>
                    <label className="text-sm font-medium">队列深度</label>
                    <p className="text-2xl font-bold">{currentMetrics.performance.queueDepth.toFixed(1)}</p>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>性能趋势</CardTitle>
                <CardDescription>I/O性能指标变化趋势</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="h-80">
                  <StatusChart
                    type="line"
                    data={metrics.map(m => ({
                      time: m.timestamp,
                      readSpeed: m.performance.readSpeed,
                      writeSpeed: m.performance.writeSpeed,
                      iops: m.performance.iops / 10,
                    }))}
                    dataKey="readSpeed"
                    nameKey="time"
                    colors={["#3b82f6", "#ef4444", "#f59e0b"]}
                  />
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        {/* 文件系统 */}
        <TabsContent value="filesystem" className="space-y-4">
          <div className="grid gap-4 md:grid-cols-2">
            <Card>
              <CardHeader>
                <CardTitle>文件系统统计</CardTitle>
                <CardDescription>文件和目录统计信息</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <label className="text-sm font-medium">总文件数</label>
                      <p className="text-2xl font-bold">{currentMetrics.fileSystem.totalFiles.toLocaleString()}</p>
                    </div>
                    <div>
                      <label className="text-sm font-medium">总目录数</label>
                      <p className="text-2xl font-bold">{currentMetrics.fileSystem.totalDirectories.toLocaleString()}</p>
                    </div>
                  </div>
                  <div className="space-y-3">
                    <h4 className="font-medium">最大目录</h4>
                    {currentMetrics.fileSystem.largestDirectories.map((dir, index) => (
                      <div key={index} className="flex items-center justify-between p-3 border rounded">
                        <div>
                          <p className="font-medium">{dir.path}</p>
                          <p className="text-sm text-muted-foreground">
                            {dir.fileCount.toLocaleString()} 个文件
                          </p>
                        </div>
                        <div className="text-right">
                          <p className="font-semibold">{formatBytes(dir.size)}</p>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>文件类型分布</CardTitle>
                <CardDescription>按文件类型统计的存储使用情况</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="h-80">
                  <StatusChart
                    type="pie"
                    data={currentMetrics.fileSystem.fileTypes.map(type => ({
                      type: type.type,
                      size: type.size / 1024 / 1024 / 1024, // Convert to GB
                      count: type.count,
                    }))}
                    dataKey="size"
                    nameKey="type"
                    colors={["#3b82f6", "#ef4444", "#10b981", "#f59e0b", "#8b5cf6"]}
                  />
                </div>
                <div className="mt-4 space-y-2">
                  {currentMetrics.fileSystem.fileTypes.map((type) => (
                    <div key={type.type} className="flex items-center justify-between">
                      <div className="flex items-center space-x-2">
                        {type.type === "log" && <FileText className="h-4 w-4 text-blue-600" />}
                        {type.type === "database" && <Database className="h-4 w-4 text-green-600" />}
                        {type.type === "image" && <File className="h-4 w-4 text-purple-600" />}
                        {type.type === "document" && <FileText className="h-4 w-4 text-orange-600" />}
                        {type.type === "archive" && <Archive className="h-4 w-4 text-red-600" />}
                        <span className="font-medium">{type.type}</span>
                      </div>
                      <div className="text-right">
                        <p className="font-semibold">{formatBytes(type.size)}</p>
                        <p className="text-xs text-muted-foreground">
                          {type.count.toLocaleString()} 文件
                        </p>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        {/* 告警信息 */}
        <TabsContent value="alerts" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>磁盘告警</CardTitle>
              <CardDescription>当前活跃的磁盘相关告警信息</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {alerts.map((alert) => (
                  <div
                    key={alert.id}
                    className={`flex items-center justify-between p-4 rounded-lg border ${
                      alert.resolved ? 'bg-gray-50' : 'bg-red-50 border-red-200'
                    }`}
                  >
                    <div className="flex items-center space-x-3">
                      {getAlertIcon(alert.type)}
                      <div>
                        <p className="font-medium">{alert.message}</p>
                        <p className="text-sm text-muted-foreground">
                          {alert.timestamp} - 严重程度: {alert.severity}
                        </p>
                      </div>
                    </div>
                    <div className="flex items-center space-x-2">
                      {alert.resolved ? (
                        <Badge className="bg-green-100 text-green-800 border-green-200">已解决</Badge>
                      ) : (
                        <Badge className="bg-red-100 text-red-800 border-red-200">未解决</Badge>
                      )}
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
} 