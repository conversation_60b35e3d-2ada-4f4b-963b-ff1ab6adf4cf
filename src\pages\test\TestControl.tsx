import { useState, useEffect } from "react";
import { DashboardCard } from "@/components/DashboardCard";
import { StatusChart } from "@/components/StatusChart";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Progress } from "@/components/ui/progress";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Ta<PERSON>, <PERSON><PERSON>Content, Ta<PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  Play,
  Pause,
  Settings,
  AlertTriangle,
  CheckCircle,
  Clock,
  Activity,
  Power,
  PowerOff,
  Zap,
  <PERSON>ch,
  Eye,
  RotateCcw,
  <PERSON>,
  <PERSON><PERSON>,
  TestTube2,
  ShieldCheck,
  ShieldAlert,
  RefreshCw,
  AlertCircle,
  XCircle,
  CheckCircle2,
} from "lucide-react";
import { Input } from "@/components/ui/input";

interface SystemStatus {
  mode: "automatic" | "manual";
  isRunning: boolean;
  lastSwitchTime: string;
  nextScheduledTest?: string;
  activeTests: number;
  queuedTests: number;
}

interface SubSystemStatus {
  name: string;
  type: "truss" | "amr" | "test";
  status: "running" | "idle" | "maintenance" | "error";
  currentTask?: string;
  progress?: number;
}

interface TestOperation {
  id: string;
  productId: string;
  operation: string;
  status: "pending" | "in_progress" | "completed" | "failed";
  startTime?: string;
  estimatedCompletion?: string;
  assignedSystem: string;
}

const mockSystemStatus: SystemStatus = {
  mode: "automatic",
  isRunning: true,
  lastSwitchTime: "2024-01-15 08:30:00",
  nextScheduledTest: "2024-01-15 16:45:00",
  activeTests: 12,
  queuedTests: 12,
};

const mockSubSystems: SubSystemStatus[] = [
  {
    name: "桁架系统-01",
    type: "truss",
    status: "running",
    currentTask: "产品A-2024-001入库",
    progress: 75,
  },
  {
    name: "AMR-01",
    type: "amr",
    status: "running",
    currentTask: "运输A-2024-002到测试台",
    progress: 45,
  },
  { name: "AMR-02", type: "amr", status: "maintenance", currentTask: "充电中" },
  { name: "AMR-03", type: "amr", status: "idle" },
  { name: "AMR-04", type: "amr", status: "maintenance", currentTask: "充电中" },
  { name: "AMR-05", type: "amr", status: "maintenance", currentTask: "充电中" },
  { name: "AMR-06", type: "amr", status: "maintenance", currentTask: "充电中" },
  {
    name: "测试台-01",
    type: "test",
    status: "running",
    currentTask: "月稳测试A-2024-004",
    progress: 30,
  },
  {
    name: "测试台-02",
    type: "test",
    status: "running",
    currentTask: "月稳测试A-2024-001",
    progress: 30,
  },
  { name: "测试台-03", type: "test", status: "error", currentTask: "设备故障" },
  {
    name: "测试台-04",
    type: "test",
    status: "running",
    currentTask: "月稳测试A-2024-004",
    progress: 30,
  },
  {
    name: "测试台-05",
    type: "test",
    status: "running",
    currentTask: "月稳测试A-2024-001",
    progress: 30,
  },
  {
    name: "测试台-06",
    type: "test",
    status: "running",
    currentTask: "月稳测试A-2024-001",
    progress: 30,
  },
  {
    name: "测试台-07",
    type: "test",
    status: "running",
    currentTask: "月稳测试A-2024-001",
    progress: 30,
  },
  {
    name: "测试台-08",
    type: "test",
    status: "running",
    currentTask: "月稳测试A-2024-001",
    progress: 30,
  },
  {
    name: "测试台-09",
    type: "test",
    status: "running",
    currentTask: "月稳测试A-2024-001",
    progress: 30,
  },
  {
    name: "测试台-10",
    type: "test",
    status: "running",
    currentTask: "月稳测试A-2024-001",
    progress: 30,
  },
  {
    name: "测试台-11",
    type: "test",
    status: "running",
    currentTask: "月稳测试A-2024-001",
    progress: 30,
  },
  {
    name: "测试台-12",
    type: "test",
    status: "running",
    currentTask: "月稳测试A-2024-001",
    progress: 30,
  }
];

const mockOperations: TestOperation[] = [
  {
    id: "OP001",
    productId: "A-2024-001",
    operation: "桁架入库操作",
    status: "in_progress",
    startTime: "15:30",
    estimatedCompletion: "15:45",
    assignedSystem: "桁架系统-01",
  },
  {
    id: "OP002",
    productId: "A-2024-002",
    operation: "AMR运输到测试台",
    status: "in_progress",
    startTime: "15:25",
    estimatedCompletion: "15:35",
    assignedSystem: "AMR-01",
  },
  {
    id: "OP003",
    productId: "A-2024-005",
    operation: "开始月稳测试",
    status: "pending",
    assignedSystem: "测试台-04",
  },
];

// 静态模拟产品数据
const manualProducts = [
        { id: "A-2024-001", status: "在库", location: "A区-01-01" },
      { id: "A-2024-002", status: "测试中", location: "测试台-01" },
      { id: "A-2024-003", status: "在库", location: "B区-01-05" },
      { id: "A-2024-004", status: "暂停", location: "C区-01-02" },
];

export default function TestControl() {
  const [systemStatus, setSystemStatus] = useState(mockSystemStatus);
  const [confirmDialog, setConfirmDialog] = useState<
    "auto" | "manual" | "restore" | null
  >(null);
  const [subSystems, setSubSystems] = useState(mockSubSystems);
  const [operations, setOperations] = useState(mockOperations);
  const [isRestoring, setIsRestoring] = useState(false);
  const [restoreProgress, setRestoreProgress] = useState(0);
  const [manualSearch, setManualSearch] = useState("");
  const [manualSelected, setManualSelected] = useState<string[]>([]);

  const handleModeSwitch = (newMode: "automatic" | "manual") => {
    setSystemStatus({
      ...systemStatus,
      mode: newMode,
      lastSwitchTime: new Date().toLocaleString("zh-CN"),
      isRunning: newMode === "automatic",
    });
    setConfirmDialog(null);
  };

  const getSystemIcon = (type: string) => {
    switch (type) {
      case "truss":
        return <Bot className="h-4 w-4" />;
      case "amr":
        return <Truck className="h-4 w-4" />;
      case "test":
        return <TestTube2 className="h-4 w-4" />;
      default:
        return <Settings className="h-4 w-4" />;
    }
  };

  const getStatusBadge = (status: string) => {
    const statusConfig = {
      running: { label: "运行中", variant: "default" as const, icon: Play },
      idle: { label: "待机", variant: "secondary" as const, icon: Pause },
      maintenance: {
        label: "维护中",
        variant: "destructive" as const,
        icon: Wrench,
      },
      error: {
        label: "故障",
        variant: "destructive" as const,
        icon: AlertTriangle,
      },
    };

    const config = statusConfig[status as keyof typeof statusConfig];
    const Icon = config.icon;
    return (
      <Badge variant={config.variant} className="flex items-center gap-1">
        <Icon className="h-3 w-3" />
        {config.label}
      </Badge>
    );
  };

  const getOperationStatusBadge = (status: string) => {
    const statusConfig = {
      pending: { label: "待执行", variant: "secondary" as const },
      in_progress: { label: "执行中", variant: "default" as const },
      completed: { label: "已完成", variant: "default" as const },
      failed: { label: "失败", variant: "destructive" as const },
    };

    const config = statusConfig[status as keyof typeof statusConfig];
    return <Badge variant={config.variant}>{config.label}</Badge>;
  };

  // One-click restore functionality
  const handleEmergencyRestore = async () => {
    setIsRestoring(true);
    setRestoreProgress(0);

    // Simulate restore process
    const steps = [
      { progress: 20, message: "停止故障任务..." },
      { progress: 40, message: "重置桁架系统..." },
      { progress: 60, message: "重置AMR调度..." },
      { progress: 80, message: "重置测试台状态..." },
      { progress: 100, message: "恢复完成" },
    ];

    for (const step of steps) {
      await new Promise((resolve) => setTimeout(resolve, 800));
      setRestoreProgress(step.progress);
    }

    // Reset failed systems to idle
    setSubSystems((prev) =>
      prev.map((system) => ({
        ...system,
        status: system.status === "error" ? "idle" : system.status,
        currentTask: system.status === "error" ? undefined : system.currentTask,
        progress: system.status === "error" ? undefined : system.progress,
      })),
    );

    // Remove failed operations
    setOperations((prev) => prev.filter((op) => op.status !== "failed"));

    // Reset system to automatic mode if it was in manual
    setSystemStatus((prev) => ({
      ...prev,
      mode: "automatic",
      isRunning: true,
      lastSwitchTime: new Date().toLocaleString("zh-CN"),
    }));

    setIsRestoring(false);
    setConfirmDialog(null);
    setRestoreProgress(0);
  };

  // Get failed systems count
  const failedSystemsCount = subSystems.filter(
    (s) => s.status === "error",
  ).length;
  const failedOperationsCount = operations.filter(
    (op) => op.status === "failed",
  ).length;

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h2 className="text-3xl font-bold tracking-tight">测试控制中心</h2>
        <div className="flex items-center space-x-2">
          {(failedSystemsCount > 0 || failedOperationsCount > 0) && (
            <Dialog
              open={confirmDialog === "restore"}
              onOpenChange={() => setConfirmDialog(null)}
            >
              <DialogTrigger asChild>
                <Button
                  variant="destructive"
                  className="bg-red-600 hover:bg-red-700"
                  onClick={() => setConfirmDialog("restore")}
                  disabled={isRestoring}
                >
                  <RefreshCw
                    className={`mr-2 h-4 w-4 ${isRestoring ? "animate-spin" : ""}`}
                  />
                  一键还原
                </Button>
              </DialogTrigger>
              <DialogContent className="sm:max-w-[600px]">
                <DialogHeader>
                  <DialogTitle className="flex items-center text-red-600">
                    <AlertCircle className="mr-2 h-5 w-5" />
                    紧急一键还原确认
                  </DialogTitle>
                  <DialogDescription className="space-y-3">
                    <div className="text-red-600 font-medium">
                      ⚠️ 警告：此操作将强制重置所有故障设备和任务
                    </div>
                    <div className="bg-red-50 p-4 rounded-lg space-y-2">
                      <div className="font-medium">即将执行的操作：</div>
                      <div className="space-y-1 text-sm">
                        <div className="flex items-center">
                          <XCircle className="mr-2 h-4 w-4 text-red-500" />
                          关闭 {failedOperationsCount} 个故障任务
                        </div>
                        <div className="flex items-center">
                          <RefreshCw className="mr-2 h-4 w-4 text-blue-500" />
                          重置 {failedSystemsCount} 个故障设备状态
                        </div>
                        <div className="flex items-center">
                          <CheckCircle2 className="mr-2 h-4 w-4 text-green-500" />
                          恢复设备到待机状态，准备执行新任务
                        </div>
                        <div className="flex items-center">
                          <Zap className="mr-2 h-4 w-4 text-orange-500" />
                          自动切换到自动控制模式
                        </div>
                      </div>
                    </div>
                    <div className="text-gray-600">
                      此操作不会影响正常运行的设备和任务，仅重置故障状态。
                    </div>
                    {isRestoring && (
                      <div className="space-y-2">
                        <div className="flex justify-between text-sm">
                          <span>还原进度</span>
                          <span>{restoreProgress}%</span>
                        </div>
                        <Progress value={restoreProgress} className="h-2" />
                      </div>
                    )}
                  </DialogDescription>
                </DialogHeader>
                <div className="flex justify-end space-x-2">
                  <Button
                    variant="outline"
                    onClick={() => setConfirmDialog(null)}
                    disabled={isRestoring}
                  >
                    取消
                  </Button>
                  <Button
                    variant="destructive"
                    onClick={handleEmergencyRestore}
                    disabled={isRestoring}
                  >
                    {isRestoring ? (
                      <>
                        <RefreshCw className="mr-2 h-4 w-4 animate-spin" />
                        还原中...
                      </>
                    ) : (
                      <>
                        <RefreshCw className="mr-2 h-4 w-4" />
                        确认还原
                      </>
                    )}
                  </Button>
                </div>
              </DialogContent>
            </Dialog>
          )}

          {systemStatus.mode === "automatic" ? (
            <Badge className="bg-green-100 text-green-800 flex items-center gap-2">
              <ShieldCheck className="h-4 w-4" />
              自动控制模式
            </Badge>
          ) : (
            <Badge variant="destructive" className="flex items-center gap-2">
              <ShieldAlert className="h-4 w-4" />
              手动控制模式
            </Badge>
          )}
        </div>
      </div>

      {/* Control Mode Section */}
      <div className="grid gap-6 md:grid-cols-2">
        {/* Automatic Control */}
        <Card
          className={`transition-all duration-300 ${
            systemStatus.mode === "automatic"
              ? "ring-2 ring-green-500 bg-green-50 dark:bg-green-950"
              : "hover:shadow-lg"
          }`}
        >
          <CardHeader>
            <CardTitle className="flex items-center justify-between">
              <div className="flex items-center space-x-2">
                <Zap className="h-6 w-6 text-green-600" />
                <span>自动控制模式</span>
              </div>
              {systemStatus.mode === "automatic" && (
                <div className="flex items-center space-x-1">
                  <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
                  <span className="text-sm text-green-600">运行中</span>
                </div>
              )}
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <p className="text-gray-600 dark:text-gray-300">
              系统将根据预设的测试计划自动调度桁架、AMR和测试系统，无需人工干预地执行惯组的测试流程。
            </p>

            <div className="space-y-2">
              <div className="flex justify-between text-sm">
                <span>正在执行测试:</span>
                <span className="font-semibold">
                  {systemStatus.activeTests} 个
                </span>
              </div>
              <div className="flex justify-between text-sm">
                <span>排队等待测试:</span>
                <span className="font-semibold">
                  {systemStatus.queuedTests} 个
                </span>
              </div>
              {systemStatus.nextScheduledTest && (
                <div className="flex justify-between text-sm">
                  <span>下次计划测试:</span>
                  <span className="font-semibold">
                    {systemStatus.nextScheduledTest}
                  </span>
                </div>
              )}
            </div>

            <Dialog
              open={confirmDialog === "auto"}
              onOpenChange={() => setConfirmDialog(null)}
            >
              <DialogTrigger asChild>
                <Button
                  className="w-full"
                  disabled={systemStatus.mode === "automatic"}
                  onClick={() => setConfirmDialog("auto")}
                >
                  <Power className="mr-2 h-4 w-4" />
                  启用自动控制
                </Button>
              </DialogTrigger>
              <DialogContent>
                <DialogHeader>
                  <DialogTitle>确认启用自动控制模式</DialogTitle>
                  <DialogDescription>
                    启用自动控制后，系统将按照预设计划自动执行测试任务。请确保：
                    <br />• 所有设备状态正常
                    <br />• 测试计划已正确配置
                    <br />• 相关人员已就位
                  </DialogDescription>
                </DialogHeader>
                <div className="flex justify-end space-x-2">
                  <Button
                    variant="outline"
                    onClick={() => setConfirmDialog(null)}
                  >
                    取消
                  </Button>
                  <Button onClick={() => handleModeSwitch("automatic")}>
                    确认启用
                  </Button>
                </div>
              </DialogContent>
            </Dialog>
          </CardContent>
        </Card>

        {/* Manual Control */}
        <Card
          className={`transition-all duration-300 ${
            systemStatus.mode === "manual"
              ? "ring-2 ring-orange-500 bg-orange-50 dark:bg-orange-950"
              : "hover:shadow-lg"
          }`}
        >
          <CardHeader>
            <CardTitle className="flex items-center justify-between">
              <div className="flex items-center space-x-2">
                <Settings className="h-6 w-6 text-orange-600" />
                <span>手动控制模式</span>
              </div>
              {systemStatus.mode === "manual" && (
                <div className="flex items-center space-x-1">
                  <div className="w-2 h-2 bg-orange-500 rounded-full animate-pulse"></div>
                  <span className="text-sm text-orange-600">维护模式</span>
                </div>
              )}
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <p className="text-gray-600 dark:text-gray-300">
              系统暂停自动调度，进入维护状态。适用于设备维护、故障排除、系统调试等场景。
            </p>

            <Alert>
              <AlertTriangle className="h-4 w-4" />
              <AlertDescription>
                手动模式下，所有自动测试计划将被暂停，需要人工控制设备操作。
              </AlertDescription>
            </Alert>

            <div className="space-y-2">
              <div className="flex justify-between text-sm">
                <span>暂停的测试计划:</span>
                <span className="font-semibold text-orange-600">
                  {systemStatus.queuedTests} 个
                </span>
              </div>
              <div className="flex justify-between text-sm">
                <span>需要人工处理:</span>
                <span className="font-semibold text-red-600">3 项</span>
              </div>
            </div>

            <Dialog
              open={confirmDialog === "manual"}
              onOpenChange={() => setConfirmDialog(null)}
            >
              <DialogTrigger asChild>
                <Button
                  variant="destructive"
                  className="w-full"
                  disabled={systemStatus.mode === "manual"}
                  onClick={() => setConfirmDialog("manual")}
                >
                  <PowerOff className="mr-2 h-4 w-4" />
                  切换到手动控制
                </Button>
              </DialogTrigger>
              <DialogContent>
                <DialogHeader>
                  <DialogTitle>确认切换到手动控制模式</DialogTitle>
                  <DialogDescription className="space-y-2">
                    <div className="text-red-600 font-medium">
                      ⚠️ 警告：此操作将暂停所有自动测试计划
                    </div>
                    <div>切换到手动控制后：</div>
                    <div>• 所有自动测试将被暂停</div>
                    <div>• 设备需要人工操作</div>
                    <div>• 测试进度可能受到影响</div>
                    <div className="mt-2">请确认当前没有关键测试正在进行。</div>
                  </DialogDescription>
                </DialogHeader>
                <div className="flex justify-end space-x-2">
                  <Button
                    variant="outline"
                    onClick={() => setConfirmDialog(null)}
                  >
                    取消
                  </Button>
                  <Button
                    variant="destructive"
                    onClick={() => handleModeSwitch("manual")}
                  >
                    确认切换
                  </Button>
                </div>
              </DialogContent>
            </Dialog>
          </CardContent>
        </Card>
      </div>

      {/* System Status Overview */}
      {(failedSystemsCount > 0 || failedOperationsCount > 0) && (
        <Alert className="border-red-200 bg-red-50">
          <AlertTriangle className="h-4 w-4 text-red-600" />
          <AlertDescription className="text-red-800">
            <div className="font-medium">
              系统检测到故障，建议使用一键还原功能：
            </div>
            <div className="mt-1 text-sm">
              • {failedSystemsCount} 个设备处于故障状态 •{" "}
              {failedOperationsCount} 个任务执行失败 •
              点击右上角"一键还原"按钮可快速重置故障状态
            </div>
          </AlertDescription>
        </Alert>
      )}

      <div className="grid gap-4 md:grid-cols-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">系统状态</CardTitle>
            <Activity className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {systemStatus.mode === "automatic" ? "自动运行" : "手动控制"}
            </div>
            <p className="text-xs text-muted-foreground">
              上次切换: {systemStatus.lastSwitchTime}
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">活跃测试</CardTitle>
            <TestTube2 className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{systemStatus.activeTests}</div>
            <p className="text-xs text-muted-foreground">进行中的测试任务</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">排队任务</CardTitle>
            <Clock className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{systemStatus.queuedTests}</div>
            <p className="text-xs text-muted-foreground">等待执行的任务</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">设备效率</CardTitle>
            <CheckCircle className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">87%</div>
            <p className="text-xs text-muted-foreground">整体运行效率</p>
          </CardContent>
        </Card>
      </div>

      <Tabs defaultValue="subsystems" className="space-y-4">
        <TabsList>
          <TabsTrigger value="subsystems">子系统状态</TabsTrigger>
          <TabsTrigger value="operations">实时操作</TabsTrigger>
          <TabsTrigger value="schedule">测试计划</TabsTrigger>
          <TabsTrigger value="manual">手动控制</TabsTrigger>
        </TabsList>

        <TabsContent value="subsystems">
          <Card>
            <CardHeader>
              <CardTitle>子系统运行状态</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {["truss", "amr", "test"].map((type) => (
                  <div key={type}>
                    <h4 className="font-semibold mb-3 flex items-center">
                      {getSystemIcon(type)}
                      <span className="ml-2">
                        {type === "truss"
                          ? "桁架系统"
                          : type === "amr"
? "AMR调度"
                            : "测试系统"}
                      </span>
                    </h4>
                    <div className="grid gap-3 md:grid-cols-2 lg:grid-cols-3">
                      {subSystems
                        .filter((s) => s.type === type)
                        .map((system) => (
                          <div
                            key={system.name}
                            className="border rounded-lg p-3"
                          >
                            <div className="flex items-center justify-between mb-2">
                              <span className="font-medium">{system.name}</span>
                              {getStatusBadge(system.status)}
                            </div>
                            {system.currentTask && (
                              <div className="text-sm text-gray-600 mb-2">
                                {system.currentTask}
                              </div>
                            )}
                            {system.progress !== undefined && (
                              <div className="space-y-1">
                                <div className="flex justify-between text-xs">
                                  <span>进度</span>
                                  <span>{system.progress}%</span>
                                </div>
                                <Progress
                                  value={system.progress}
                                  className="h-1"
                                />
                              </div>
                            )}
                          </div>
                        ))}
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="operations">
          <Card>
            <CardHeader>
              <CardTitle>实时操作监控</CardTitle>
            </CardHeader>
            <CardContent>
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>操作编号</TableHead>
                    <TableHead>产品编号</TableHead>
                    <TableHead>操作内容</TableHead>
                    <TableHead>分配系统</TableHead>
                    <TableHead>开始时间</TableHead>
                    <TableHead>预计完成</TableHead>
                    <TableHead>状态</TableHead>
                    <TableHead>操作</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {operations.map((operation) => (
                    <TableRow key={operation.id}>
                      <TableCell className="font-medium">
                        {operation.id}
                      </TableCell>
                      <TableCell>{operation.productId}</TableCell>
                      <TableCell>{operation.operation}</TableCell>
                      <TableCell>{operation.assignedSystem}</TableCell>
                      <TableCell>{operation.startTime || "-"}</TableCell>
                      <TableCell>
                        {operation.estimatedCompletion || "-"}
                      </TableCell>
                      <TableCell>
                        {getOperationStatusBadge(operation.status)}
                      </TableCell>
                      <TableCell>
                        <div className="flex space-x-2">
                          <Button size="sm" variant="outline">
                            <Eye className="h-3 w-3" />
                          </Button>
                          {systemStatus.mode === "manual" && (
                            <Button size="sm" variant="outline">
                              <Settings className="h-3 w-3" />
                            </Button>
                          )}
                        </div>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="schedule">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center justify-between">
                <span>测试计划状态</span>
                <div className="flex space-x-2">
                  <Button size="sm" variant="outline">
                    <RotateCcw className="h-4 w-4" />
                  </Button>
                  {systemStatus.mode === "manual" && (
                    <Button size="sm">手动执行</Button>
                  )}
                </div>
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <Alert>
                  <CheckCircle className="h-4 w-4" />
                  <AlertDescription>
                    当前有 {systemStatus.queuedTests} 个测试计划等待执行
                    {systemStatus.mode === "manual" && "（已暂停自动执行）"}
                  </AlertDescription>
                </Alert>

                <div className="grid gap-4 md:grid-cols-2">
                  <div>
                    <h4 className="font-semibold mb-2">今日计划</h4>
                    <div className="space-y-2">
                      <div className="flex justify-between p-2 bg-gray-50 rounded">
                        <span>A-2024-005 月稳测试</span>
                        <Badge variant="secondary">16:30</Badge>
                      </div>
                      <div className="flex justify-between p-2 bg-gray-50 rounded">
                        <span>A-2024-006 月稳测试</span>
                        <Badge variant="secondary">18:00</Badge>
                      </div>
                      <div className="flex justify-between p-2 bg-gray-50 rounded">
                        <span>A-2024-007 月稳测试</span>
                        <Badge variant="secondary">19:30</Badge>
                      </div>
                    </div>
                  </div>

                  <div>
                    <h4 className="font-semibold mb-2">明日预览</h4>
                    <div className="space-y-2">
                      <div className="flex justify-between p-2 bg-gray-50 rounded">
                        <span>A-2024-008 月稳测试</span>
                        <Badge variant="outline">09:00</Badge>
                      </div>
                      <div className="flex justify-between p-2 bg-gray-50 rounded">
                        <span>A-2024-009 月稳测试</span>
                        <Badge variant="outline">10:30</Badge>
                      </div>
                      <div className="flex justify-between p-2 bg-gray-50 rounded">
                        <span>A-2024-010 月稳测试</span>
                        <Badge variant="outline">14:00</Badge>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="manual">
          <Card>
            <CardHeader>
              <CardTitle>手动控制说明</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4 text-base text-gray-700">
                <p>手动模式只能进行手动测试，在手动控制台产品列表中选择要测试的产品，勾选产品记录最前边的选择框，然后点击"出库测试"按钮开始测试；测试完毕后点击"测试完毕入库"按钮将产品搬运回仓库指定库位。</p>
              </div>
              <div className="mt-6 space-y-4">
                {/* 产品搜索 */}
                <div className="flex items-center space-x-2 mb-2">
                  <Input
                    placeholder="输入产品编号快速搜索"
                    value={manualSearch}
                    onChange={e => setManualSearch(e.target.value)}
                    className="w-64"
                  />
                </div>
                {/* 产品列表表格 */}
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead className="w-[50px]">
                        <input
                          type="checkbox"
                          checked={manualSelected.length === manualProducts.filter(p => p.id.includes(manualSearch)).length && manualProducts.filter(p => p.id.includes(manualSearch)).length > 0}
                          onChange={e => {
                            if (e.target.checked) {
                              setManualSelected(manualProducts.filter(p => p.id.includes(manualSearch)).map(p => p.id));
                            } else {
                              setManualSelected([]);
                            }
                          }}
                        />
                      </TableHead>
                      <TableHead>产品编号</TableHead>
                      <TableHead>状态</TableHead>
                      <TableHead>位置</TableHead>
                      <TableHead>操作</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {manualProducts.filter(p => p.id.includes(manualSearch)).map(product => (
                      <TableRow key={product.id}>
                        <TableCell>
                          <input
                            type="checkbox"
                            checked={manualSelected.includes(product.id)}
                            onChange={e => {
                              if (e.target.checked) {
                                setManualSelected([...manualSelected, product.id]);
                              } else {
                                setManualSelected(manualSelected.filter(id => id !== product.id));
                              }
                            }}
                          />
                        </TableCell>
                        <TableCell>{product.id}</TableCell>
                        <TableCell>{product.status}</TableCell>
                        <TableCell>{product.location}</TableCell>
                        <TableCell>
                          <div className="flex space-x-2">
                            <Button size="sm" variant="outline">出库测试</Button>
                            <Button size="sm" variant="outline">入库</Button>
                            <Button size="sm" variant="outline">暂停操作</Button>
                          </div>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}
