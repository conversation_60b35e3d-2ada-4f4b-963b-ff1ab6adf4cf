import { useState, useEffect } from "react";
import { DashboardCard } from "@/components/DashboardCard";
import { StatusChart } from "@/components/StatusChart";
import { But<PERSON> } from "@/components/ui/button";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Badge } from "@/components/ui/badge";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import { Progress } from "@/components/ui/progress";
import { <PERSON><PERSON>, AlertDescription } from "@/components/ui/alert";
import {
  Battery,
  BatteryCharging,
  BatteryLow,
  Power,
  PowerOff,
  Zap,
  AlertTriangle,
  CheckCircle,
  Clock,
  Play,
  Square,
  RefreshCw,
  MapPin,
  Timer,
} from "lucide-react";

interface AMR {
  id: string;
  name: string;
  batteryLevel: number;
  chargingStatus: "charging" | "idle" | "low_battery" | "full" | "error";
  location: string;
  chargingStationId?: string;
  chargingStartTime?: string;
  estimatedChargingTime?: number; // in minutes
  lastChargeTime?: string;
  cycleCount: number;
  batteryHealth: number; // percentage
  isOnline: boolean;
}

interface ChargingStation {
  id: string;
  name: string;
  status: "available" | "occupied" | "maintenance" | "error";
  currentAMR?: string;
  powerOutput: number; // in watts
  location: string;
  efficiency: number; // percentage
}

const mockAMRs: AMR[] = [
  {
    id: "AMR-01",
    name: "AMR小车-01",
    batteryLevel: 85,
    chargingStatus: "idle",
    location: "仓储区A-通道1",
    lastChargeTime: "2024-01-15 08:30",
    cycleCount: 1245,
    batteryHealth: 92,
    isOnline: true,
  },
  {
    id: "AMR-02",
    name: "AMR小车-02",
    batteryLevel: 45,
    chargingStatus: "charging",
    location: "充电站-01",
    chargingStationId: "CS-01",
    chargingStartTime: "2024-01-15 14:20",
    estimatedChargingTime: 35,
    cycleCount: 1156,
    batteryHealth: 88,
    isOnline: true,
  },
  {
    id: "AMR-03",
    name: "AMR小车-03",
    batteryLevel: 15,
    chargingStatus: "low_battery",
    location: "测试区域",
    lastChargeTime: "2024-01-14 22:15",
    cycleCount: 1389,
    batteryHealth: 85,
    isOnline: true,
  },
  {
    id: "AMR-04",
    name: "AMR小车-04",
    batteryLevel: 100,
    chargingStatus: "full",
    location: "充电站-02",
    chargingStationId: "CS-02",
    chargingStartTime: "2024-01-15 12:00",
    cycleCount: 987,
    batteryHealth: 95,
    isOnline: true,
  },
  {
    id: "AMR-05",
    name: "AMR小车-05",
    batteryLevel: 0,
    chargingStatus: "error",
    location: "仓储区B-通道2",
    lastChargeTime: "2024-01-15 06:45",
    cycleCount: 1567,
    batteryHealth: 78,
    isOnline: false,
  },
  {
    id: "AMR-06",
    name: "AMR小车-06",
    batteryLevel: 72,
    chargingStatus: "idle",
    location: "出入库区域",
    lastChargeTime: "2024-01-15 10:20",
    cycleCount: 1098,
    batteryHealth: 90,
    isOnline: true,
  },
];

const mockChargingStations: ChargingStation[] = [
  {
    id: "CS-01",
    name: "充电站-01",
    status: "occupied",
    currentAMR: "AMR-02",
    powerOutput: 3000,
    location: "充电区域A",
    efficiency: 92,
  },
  {
    id: "CS-02",
    name: "充电站-02",
    status: "occupied",
    currentAMR: "AMR-04",
    powerOutput: 3000,
    location: "充电区域A",
    efficiency: 89,
  },
  {
    id: "CS-03",
    name: "充电站-03",
    status: "available",
    powerOutput: 3000,
    location: "充电区域B",
    efficiency: 94,
  },
  {
    id: "CS-04",
    name: "充电站-04",
    status: "maintenance",
    powerOutput: 0,
    location: "充电区域B",
    efficiency: 0,
  },
];

const batteryLevelData = [
  { name: "80-100%", value: 2 },
  { name: "60-80%", value: 1 },
  { name: "40-60%", value: 1 },
  { name: "20-40%", value: 1 },
  { name: "0-20%", value: 1 },
];

const chargingTrendData = [
  { name: "00:00", charging: 2 },
  { name: "04:00", charging: 1 },
  { name: "08:00", charging: 3 },
  { name: "12:00", charging: 2 },
  { name: "16:00", charging: 1 },
  { name: "20:00", charging: 2 },
];

export default function ChargingManagement() {
  const [amrs, setAMRs] = useState(mockAMRs);
  const [chargingStations, setChargingStations] =
    useState(mockChargingStations);
  const [selectedAMR, setSelectedAMR] = useState<AMR | null>(null);
  const [autoRefresh, setAutoRefresh] = useState(true);

  // Simulate real-time updates
  useEffect(() => {
    if (!autoRefresh) return;

    const interval = setInterval(() => {
          setAMRs((prevAMRs) =>
      prevAMRs.map((amr) => {
        if (amr.chargingStatus === "charging" && amr.batteryLevel < 100) {
          return {
            ...amr,
            batteryLevel: Math.min(100, amr.batteryLevel + Math.random() * 2),
          };
        }
        return amr;
      }),
    );
    }, 5000);

    return () => clearInterval(interval);
  }, [autoRefresh]);

  const getChargingStatusBadge = (status: string) => {
    const statusConfig = {
      charging: {
        label: "充电中",
        variant: "default" as const,
        icon: BatteryCharging,
      },
      idle: { label: "待机", variant: "secondary" as const, icon: Battery },
      low_battery: {
        label: "低电量",
        variant: "destructive" as const,
        icon: BatteryLow,
      },
      full: { label: "已充满", variant: "default" as const, icon: Battery },
      error: {
        label: "异常",
        variant: "destructive" as const,
        icon: AlertTriangle,
      },
    };

    const config = statusConfig[status as keyof typeof statusConfig];
    const Icon = config.icon;
    return (
      <Badge variant={config.variant} className="flex items-center gap-1">
        <Icon className="h-3 w-3" />
        {config.label}
      </Badge>
    );
  };

  const getStationStatusBadge = (status: string) => {
    const statusConfig = {
      available: {
        label: "可用",
        variant: "default" as const,
        icon: CheckCircle,
      },
      occupied: {
        label: "使用中",
        variant: "secondary" as const,
        icon: BatteryCharging,
      },
      maintenance: {
        label: "维护中",
        variant: "destructive" as const,
        icon: AlertTriangle,
      },
      error: {
        label: "故障",
        variant: "destructive" as const,
        icon: AlertTriangle,
      },
    };

    const config = statusConfig[status as keyof typeof statusConfig];
    const Icon = config.icon;
    return (
      <Badge variant={config.variant} className="flex items-center gap-1">
        <Icon className="h-3 w-3" />
        {config.label}
      </Badge>
    );
  };

  const getBatteryIcon = (level: number, isCharging: boolean) => {
    if (isCharging)
      return <BatteryCharging className="h-4 w-4 text-green-600" />;
    if (level > 50) return <Battery className="h-4 w-4 text-green-600" />;
    if (level > 20) return <Battery className="h-4 w-4 text-yellow-600" />;
    return <BatteryLow className="h-4 w-4 text-red-600" />;
  };

  const handleStartCharging = (amrId: string, stationId: string) => {
    setAMRs((prevAMRs) =>
      prevAMRs.map((amr) =>
        amr.id === amrId
          ? {
              ...amr,
              chargingStatus: "charging" as const,
              chargingStationId: stationId,
              chargingStartTime: new Date().toISOString(),
              location:
                chargingStations.find((s) => s.id === stationId)?.name ||
                amr.location,
            }
          : amr,
      ),
    );

    setChargingStations((prevStations) =>
      prevStations.map((station) =>
        station.id === stationId
          ? { ...station, status: "occupied" as const, currentAMR: amrId }
          : station,
      ),
    );
  };

  const handleStopCharging = (amrId: string) => {
    const amr = amrs.find((a) => a.id === amrId);
    if (amr?.chargingStationId) {
      setChargingStations((prevStations) =>
        prevStations.map((station) =>
          station.id === amr.chargingStationId
            ? {
                ...station,
                status: "available" as const,
                currentAMR: undefined,
              }
            : station,
        ),
      );
    }

    setAMRs((prevAMRs) =>
      prevAMRs.map((a) =>
        a.id === amrId
          ? {
              ...a,
              chargingStatus:
                a.batteryLevel >= 100 ? ("full" as const) : ("idle" as const),
              chargingStationId: undefined,
              chargingStartTime: undefined,
              lastChargeTime: new Date().toLocaleString("zh-CN"),
            }
          : a,
      ),
    );
  };

  const lowBatteryAMRs = amrs.filter(
    (amr) => amr.batteryLevel < 30 && amr.chargingStatus !== "charging",
  );
  const chargingAMRs = amrs.filter((amr) => amr.chargingStatus === "charging");
  const availableStations = chargingStations.filter(
    (station) => station.status === "available",
  );

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h2 className="text-3xl font-bold tracking-tight">AMR充电管理</h2>
        <div className="flex items-center space-x-2">
          <Button
            variant="outline"
            size="sm"
            onClick={() => setAutoRefresh(!autoRefresh)}
          >
            <RefreshCw
              className={`mr-2 h-4 w-4 ${autoRefresh ? "animate-spin" : ""}`}
            />
            自动刷新 {autoRefresh ? "开" : "关"}
          </Button>
          <Button variant="outline" size="sm">
            <Timer className="mr-2 h-4 w-4" />
            充电统计
          </Button>
        </div>
      </div>

      {/* Alert for low battery AMRs */}
      {lowBatteryAMRs.length > 0 && (
        <Alert className="border-orange-200 bg-orange-50">
          <AlertTriangle className="h-4 w-4 text-orange-600" />
          <AlertDescription className="text-orange-800">
            <strong>低电量警告:</strong> {lowBatteryAMRs.length}{" "}
            台AMR电量不足30%，建议安排充电
            <div className="mt-1">
              {lowBatteryAMRs.map((amr) => amr.name).join(", ")}
            </div>
          </AlertDescription>
        </Alert>
      )}

      <Tabs defaultValue="monitor" className="space-y-4">
        <TabsList>
          <TabsTrigger value="monitor">充电监控</TabsTrigger>
          <TabsTrigger value="statistics">统计分析</TabsTrigger>
        </TabsList>

        <TabsContent value="monitor">
          <div className="space-y-6">
            {/* Overview Cards */}
            <div className="grid gap-4 md:grid-cols-4">
              <Card>
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium">
                    正在充电
                  </CardTitle>
                  <BatteryCharging className="h-4 w-4 text-muted-foreground" />
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">
                    {chargingAMRs.length}
                  </div>
                  <p className="text-xs text-muted-foreground">
                    共{amrs.length}台AMR
                  </p>
                </CardContent>
              </Card>

              <Card>
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium">
                    低电量数量
                  </CardTitle>
                  <BatteryLow className="h-4 w-4 text-muted-foreground" />
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold text-red-600">
                    {lowBatteryAMRs.length}
                  </div>
                  <p className="text-xs text-muted-foreground">电量 &lt; 30%</p>
                </CardContent>
              </Card>

              <Card>
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium">
                    可用充电站
                  </CardTitle>
                  <Zap className="h-4 w-4 text-muted-foreground" />
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">
                    {availableStations.length}
                  </div>
                  <p className="text-xs text-muted-foreground">
                    共{chargingStations.length}个充电站
                  </p>
                </CardContent>
              </Card>

              <Card>
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium">
                    平均电量
                  </CardTitle>
                  <Battery className="h-4 w-4 text-muted-foreground" />
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">
                    {Math.round(
                      amrs.reduce((sum, amr) => sum + amr.batteryLevel, 0) /
                        amrs.length,
                    )}
                    %
                  </div>
                  <p className="text-xs text-muted-foreground">系统平均值</p>
                </CardContent>
              </Card>
            </div>

            {/* Real-time AMR Status */}
            <Card>
              <CardHeader>
                <CardTitle>AMR实时状态监控</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
                  {amrs.map((amr) => (
                    <Card key={amr.id} className="p-4">
                      <div className="space-y-3">
                        <div className="flex items-center justify-between">
                          <div className="flex items-center space-x-2">
                            {getBatteryIcon(
                              amr.batteryLevel,
                              amr.chargingStatus === "charging",
                            )}
                            <span className="font-medium">{amr.name}</span>
                          </div>
                          <div className="flex items-center space-x-1">
                            <div
                              className={`w-2 h-2 rounded-full ${amr.isOnline ? "bg-green-500" : "bg-red-500"}`}
                            />
                            <span className="text-xs text-gray-500">
                              {amr.isOnline ? "在线" : "离线"}
                            </span>
                          </div>
                        </div>

                        <div className="space-y-2">
                          <div className="flex items-center justify-between text-sm">
                            <span>电量:</span>
                            <span
                              className={`font-semibold ${
                                amr.batteryLevel > 50
                                  ? "text-green-600"
                                  : amr.batteryLevel > 20
                                    ? "text-yellow-600"
                                    : "text-red-600"
                              }`}
                            >
                              {amr.batteryLevel}%
                            </span>
                          </div>
                          <Progress value={amr.batteryLevel} className="h-2" />
                        </div>

                        <div className="space-y-1 text-xs text-gray-600">
                          <div className="flex justify-between">
                            <span>状态:</span>
                            <div>
                              {getChargingStatusBadge(amr.chargingStatus)}
                            </div>
                          </div>
                          <div className="flex justify-between">
                            <span>位置:</span>
                            <span>{amr.location}</span>
                          </div>
                          {amr.chargingStatus === "charging" &&
                            amr.estimatedChargingTime && (
                              <div className="flex justify-between">
                                <span>预计剩余:</span>
                                <span>{amr.estimatedChargingTime}分钟</span>
                              </div>
                            )}
                          <div className="flex justify-between">
                            <span>电池健康:</span>
                            <span
                              className={
                                amr.batteryHealth > 85
                                  ? "text-green-600"
                                  : "text-yellow-600"
                              }
                            >
                              {amr.batteryHealth}%
                            </span>
                          </div>
                        </div>

                        <div className="flex space-x-2">
                          {amr.chargingStatus === "charging" ? (
                            <Button
                              size="sm"
                              variant="outline"
                              className="flex-1"
                              onClick={() => handleStopCharging(amr.id)}
                            >
                              <Square className="mr-1 h-3 w-3" />
                              停止充电
                            </Button>
                          ) : (
                            <Button
                              size="sm"
                              variant="outline"
                              className="flex-1"
                              disabled={
                                availableStations.length === 0 || !amr.isOnline
                              }
                              onClick={() => setSelectedAMR(amr)}
                            >
                              <Play className="mr-1 h-3 w-3" />
                              开始充电
                            </Button>
                          )}
                          <Button size="sm" variant="outline">
                            <MapPin className="h-3 w-3" />
                          </Button>
                        </div>
                      </div>
                    </Card>
                  ))}
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="control">
          <div className="space-y-6">
            <div className="grid gap-6 md:grid-cols-2">
              {/* Quick Charging Control */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center">
                    <Zap className="mr-2 h-4 w-4" />
                    快速充电控制
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="space-y-2">
                    <Label htmlFor="quickAMR">选择AMR</Label>
                    <Select>
                      <SelectTrigger>
                        <SelectValue placeholder="选择需要充电的AMR" />
                      </SelectTrigger>
                      <SelectContent>
                        {amrs
                          .filter(
                            (amr) =>
                              amr.chargingStatus !== "charging" && amr.isOnline,
                          )
                          .map((amr) => (
                            <SelectItem key={amr.id} value={amr.id}>
                              {amr.name} - {amr.batteryLevel}%
                            </SelectItem>
                          ))}
                      </SelectContent>
                    </Select>
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="quickStation">选择充电站</Label>
                    <Select>
                      <SelectTrigger>
                        <SelectValue placeholder="选择充电站" />
                      </SelectTrigger>
                      <SelectContent>
                        {availableStations.map((station) => (
                          <SelectItem key={station.id} value={station.id}>
                            {station.name} - {station.location}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>
                  <Button className="w-full">
                    <Power className="mr-2 h-4 w-4" />
                    开始充电
                  </Button>
                </CardContent>
              </Card>

              {/* Batch Operations */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center">
                    <Battery className="mr-2 h-4 w-4" />
                    批量操作
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="space-y-3">
                    <Button
                      className="w-full"
                      variant="outline"
                      disabled={
                        lowBatteryAMRs.length === 0 ||
                        availableStations.length === 0
                      }
                    >
                      <BatteryCharging className="mr-2 h-4 w-4" />
                      低电量AMR自动充电
                      {lowBatteryAMRs.length > 0 && (
                        <Badge variant="destructive" className="ml-2">
                          {lowBatteryAMRs.length}
                        </Badge>
                      )}
                    </Button>

                    <Button
                      className="w-full"
                      variant="outline"
                      disabled={chargingAMRs.length === 0}
                    >
                      <PowerOff className="mr-2 h-4 w-4" />
                      停止所有充电
                      {chargingAMRs.length > 0 && (
                        <Badge variant="secondary" className="ml-2">
                          {chargingAMRs.length}
                        </Badge>
                      )}
                    </Button>

                    <Button className="w-full" variant="outline">
                      <RefreshCw className="mr-2 h-4 w-4" />
                      刷新所有AMR状态
                    </Button>
                  </div>

                  <div className="pt-4 border-t">
                    <h4 className="text-sm font-medium mb-2">紧急操作</h4>
                    <Button
                      className="w-full"
                      variant="destructive"
                      disabled={chargingAMRs.length === 0}
                    >
                      <AlertTriangle className="mr-2 h-4 w-4" />
                      强制停止所有充电
                    </Button>
                  </div>
                </CardContent>
              </Card>
            </div>

            {/* Active Charging Sessions */}
            <Card>
              <CardHeader>
                <CardTitle>当前充电会话</CardTitle>
              </CardHeader>
              <CardContent>
                {chargingAMRs.length === 0 ? (
                  <div className="text-center py-8 text-gray-500">
                    当前没有AMR在充电
                  </div>
                ) : (
                  <Table>
                    <TableHeader>
                                          <TableRow>
                      <TableHead>AMR编号</TableHead>
                      <TableHead>充电站</TableHead>
                      <TableHead>开始时间</TableHead>
                      <TableHead>当前电量</TableHead>
                      <TableHead>充电进度</TableHead>
                      <TableHead>预计完成</TableHead>
                      <TableHead>操作</TableHead>
                    </TableRow>
                    </TableHeader>
                    <TableBody>
                      {chargingAMRs.map((amr) => {
                        const station = chargingStations.find(
                          (s) => s.id === amr.chargingStationId,
                        );
                        return (
                          <TableRow key={amr.id}>
                            <TableCell className="font-medium">
                              {amr.name}
                            </TableCell>
                            <TableCell>{station?.name || "-"}</TableCell>
                            <TableCell>
                              {amr.chargingStartTime
                                ? new Date(
                                    amr.chargingStartTime,
                                  ).toLocaleString("zh-CN")
                                : "-"}
                            </TableCell>
                            <TableCell>
                              <div className="flex items-center space-x-2">
                                {getBatteryIcon(amr.batteryLevel, true)}
                                <span>{amr.batteryLevel}%</span>
                              </div>
                            </TableCell>
                            <TableCell>
                              <div className="flex items-center space-x-2">
                                <Progress
                                  value={amr.batteryLevel}
                                  className="w-16 h-2"
                                />
                                <span className="text-sm">
                                  {amr.batteryLevel}%
                                </span>
                              </div>
                            </TableCell>
                            <TableCell>
                              {amr.estimatedChargingTime
                                ? `${amr.estimatedChargingTime}分钟`
                                : "-"}
                            </TableCell>
                            <TableCell>
                              <div className="flex space-x-2">
                                <Button
                                  size="sm"
                                  variant="outline"
                                  onClick={() => handleStopCharging(amr.id)}
                                >
                                  <Square className="h-3 w-3" />
                                </Button>
                                <Button size="sm" variant="destructive">
                                  <PowerOff className="h-3 w-3" />
                                </Button>
                              </div>
                            </TableCell>
                          </TableRow>
                        );
                      })}
                    </TableBody>
                  </Table>
                )}
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="stations">
          <Card>
            <CardHeader>
              <CardTitle>充电站管理</CardTitle>
            </CardHeader>
            <CardContent>
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>充电站编号</TableHead>
                    <TableHead>充电站名称</TableHead>
                    <TableHead>位置</TableHead>
                    <TableHead>状态</TableHead>
                    <TableHead>当前AMR</TableHead>
                    <TableHead>功率输出</TableHead>
                    <TableHead>效率</TableHead>
                    <TableHead>操作</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {chargingStations.map((station) => (
                    <TableRow key={station.id}>
                      <TableCell className="font-medium">
                        {station.id}
                      </TableCell>
                      <TableCell>{station.name}</TableCell>
                      <TableCell>{station.location}</TableCell>
                      <TableCell>
                        {getStationStatusBadge(station.status)}
                      </TableCell>
                      <TableCell>{station.currentAMR || "-"}</TableCell>
                      <TableCell>{station.powerOutput}W</TableCell>
                      <TableCell>
                        <div className="flex items-center space-x-2">
                          <Progress
                            value={station.efficiency}
                            className="w-16 h-2"
                          />
                          <span className="text-sm">{station.efficiency}%</span>
                        </div>
                      </TableCell>
                      <TableCell>
                        <div className="flex space-x-2">
                          <Button size="sm" variant="outline">
                            设置
                          </Button>
                          {station.status === "occupied" && (
                            <Button size="sm" variant="destructive">
                              强制释放
                            </Button>
                          )}
                        </div>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="statistics">
          <div className="space-y-6">
            <div className="grid gap-6 md:grid-cols-2">
              <DashboardCard title="电量分布" description="AMR电量等级分布情况">
                <StatusChart type="pie" data={batteryLevelData} />
              </DashboardCard>

              <DashboardCard title="充电趋势" description="24小时充电数量趋势">
                <StatusChart
                  type="line"
                  data={chargingTrendData}
                  dataKey="charging"
                  nameKey="name"
                />
              </DashboardCard>
            </div>

            <div className="grid gap-6 md:grid-cols-3">
              <Card>
                <CardHeader>
                  <CardTitle>电池健康统计</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="space-y-2">
                    <div className="flex justify-between text-sm">
                      <span>平均健康度:</span>
                      <span className="font-semibold">
                        {Math.round(
                          amrs.reduce(
                            (sum, amr) => sum + amr.batteryHealth,
                            0,
                          ) / amrs.length,
                        )}
                        %
                      </span>
                    </div>
                    <div className="flex justify-between text-sm">
                      <span>最佳健康度:</span>
                      <span className="font-semibold text-green-600">
                        {Math.max(...amrs.map((amr) => amr.batteryHealth))}%
                      </span>
                    </div>
                    <div className="flex justify-between text-sm">
                      <span>最差健康度:</span>
                      <span className="font-semibold text-red-600">
                        {Math.min(...amrs.map((amr) => amr.batteryHealth))}%
                      </span>
                    </div>
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle>充电站利用率</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="space-y-2">
                    <div className="flex justify-between text-sm">
                      <span>使用中:</span>
                      <span className="font-semibold">
                        {
                          chargingStations.filter(
                            (s) => s.status === "occupied",
                          ).length
                        }{" "}
                        / {chargingStations.length}
                      </span>
                    </div>
                    <div className="flex justify-between text-sm">
                      <span>利用率:</span>
                      <span className="font-semibold text-blue-600">
                        {Math.round(
                          (chargingStations.filter(
                            (s) => s.status === "occupied",
                          ).length /
                            chargingStations.length) *
                            100,
                        )}
                        %
                      </span>
                    </div>
                    <div className="flex justify-between text-sm">
                      <span>维护中:</span>
                      <span className="font-semibold text-orange-600">
                        {
                          chargingStations.filter(
                            (s) => s.status === "maintenance",
                          ).length
                        }
                      </span>
                    </div>
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle>充电效率</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="space-y-2">
                    <div className="flex justify-between text-sm">
                      <span>平均效率:</span>
                      <span className="font-semibold">
                        {Math.round(
                          chargingStations.reduce(
                            (sum, station) => sum + station.efficiency,
                            0,
                          ) / chargingStations.length,
                        )}
                        %
                      </span>
                    </div>
                    <div className="flex justify-between text-sm">
                      <span>总功率输出:</span>
                      <span className="font-semibold">
                        {chargingStations.reduce(
                          (sum, station) => sum + station.powerOutput,
                          0,
                        )}
                        W
                      </span>
                    </div>
                    <div className="flex justify-between text-sm">
                      <span>今日充电次数:</span>
                      <span className="font-semibold text-green-600">
                        {chargingAMRs.length +
                          Math.floor(Math.random() * 10) +
                          15}
                      </span>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>
          </div>
        </TabsContent>
      </Tabs>

      {/* Start Charging Dialog */}
      {selectedAMR && (
        <Dialog open={!!selectedAMR} onOpenChange={() => setSelectedAMR(null)}>
          <DialogContent>
            <DialogHeader>
              <DialogTitle>启动充电 - {selectedAMR.name}</DialogTitle>
              <DialogDescription>选择充电站并开始充电</DialogDescription>
            </DialogHeader>
            <div className="space-y-4">
              <div className="space-y-2">
                <Label>当前电量</Label>
                <div className="flex items-center space-x-2">
                  {getBatteryIcon(selectedAMR.batteryLevel, false)}
                  <span className="font-semibold">
                    {selectedAMR.batteryLevel}%
                  </span>
                </div>
              </div>
              <div className="space-y-2">
                <Label htmlFor="selectStation">选择充电站</Label>
                <Select>
                  <SelectTrigger>
                    <SelectValue placeholder="选择可用的充电站" />
                  </SelectTrigger>
                  <SelectContent>
                    {availableStations.map((station) => (
                      <SelectItem key={station.id} value={station.id}>
                        {station.name} - {station.location} (效率:{" "}
                        {station.efficiency}%)
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
              <div className="space-y-2">
                <Label>预计充电时间</Label>
                <div className="text-sm text-gray-600">
                  约 {Math.round((100 - selectedAMR.batteryLevel) * 1.2)} 分钟
                </div>
              </div>
            </div>
            <div className="flex justify-end space-x-2">
              <Button variant="outline" onClick={() => setSelectedAMR(null)}>
                取消
              </Button>
              <Button
                onClick={() => {
                  if (availableStations.length > 0) {
                    handleStartCharging(
                      selectedAMR.id,
                      availableStations[0].id,
                    );
                    setSelectedAMR(null);
                  }
                }}
              >
                <Power className="mr-2 h-4 w-4" />
                开始充电
              </Button>
            </div>
          </DialogContent>
        </Dialog>
      )}
    </div>
  );
}
