import { useRef, useMemo, useState, useEffect } from "react";
import { Can<PERSON>, useFrame, useThree } from "@react-three/fiber";
import { OrbitControls, Text, Html, useGLTF, Preload } from "@react-three/drei";
import * as THREE from "three";
import { loadPositionsLocal, savePositionsLocal } from "../api/warehouse-api";

// 自定义CSS样式用于网格布局
const gridStyles = `
  .grid-cols-28 { grid-template-columns: repeat(28, minmax(0, 1fr)); }
  .grid-cols-34 { grid-template-columns: repeat(34, minmax(0, 1fr)); }
  .grid-cols-36 { grid-template-columns: repeat(36, minmax(0, 1fr)); }
`;

// 注入样式到页面
if (typeof document !== 'undefined') {
  const style = document.createElement('style');
  style.textContent = gridStyles;
  document.head.appendChild(style);
}

interface StorageItem {
  id: string;
  row: number; // 1-6排
  column: number; // 列号
  level: number; // 1-3层
  status: "empty" | "stored" | "testing";
  productId?: string;
}

interface WarehouseVisualizationProps {
  storageData?: StorageItem[];
  className?: string;
}

// 货架配置：每排的列数
const ROW_CONFIG = {
  1: 28, // 第一排28列
  2: 34, // 第二排34列
  3: 34, // 第三排34列
  4: 34, // 第四排34列
  5: 34, // 第五排34列
  6: 36, // 第六排36列
};

// 列间距配置
const COLUMN_SPACING = 3.0; // 列间距4米

// 位置保存文件名
const POSITION_SAVE_FILE = 'warehouse_positions.json';

// 过道位置：第22和23列之间
const AISLE_POSITION = -9.15;

// 生成模拟数据
const generateMockStorageData = (): StorageItem[] => {
  const data: StorageItem[] = [];
  
  for (let row = 1; row <= 6; row++) {
    const columns = ROW_CONFIG[row as keyof typeof ROW_CONFIG];
    
    for (let col = 1; col <= columns; col++) {
      for (let level = 1; level <= 3; level++) {
        const status = Math.random() > 0.3 ? "empty" : 
                      Math.random() > 0.5 ? "stored" : "testing";
        
        data.push({
          id: `${row}-${col}-${level}`,
          row,
          column: col,
          level,
          status,
          productId: status !== "empty" ? `PROD${Math.floor(Math.random() * 100000).toString().padStart(5, '0')}` : undefined
        });
      }
    }
  }
  
  return data;
};

// Preload the models to avoid loading delays
function ModelsPreloader() {
  useGLTF.preload('/asserts/shelf1.glb');
  useGLTF.preload('/asserts/shelf2.glb');
  useGLTF.preload('/asserts/shelf6.glb');
  useGLTF.preload('/asserts/惯组.glb');
  return null;
}

// 可拖拽列组件
function DraggableColumn({
  rowNumber,
  columnNumber,
  level,
  storageItem,
  basePosition,
  isSelected,
  onSelect,
  onDragStart,
  onDragEnd,
  onDrag,
  productScene
}: {
  rowNumber: number;
  columnNumber: number;
  level: number;
  storageItem: StorageItem | undefined;
  basePosition: [number, number, number];
  isSelected: boolean;
  onSelect: () => void;
  onDragStart: (row: number, column: number) => void;
  onDragEnd: () => void;
  onDrag: (row: number, column: number, newX: number) => void;
  productScene: THREE.Group;
}) {
  const groupRef = useRef<THREE.Group>(null);

  // 直接使用basePosition，确保位置实时更新
  useFrame(() => {
    if (groupRef.current) {
      groupRef.current.position.set(...basePosition);
    }
  });

  const handlePointerDown = (event: any) => {
    event.stopPropagation();
    // 普通点击选中
    onSelect();
  };

  return (
    <group
      ref={groupRef}
      position={basePosition}
      onPointerDown={handlePointerDown}
    >
             {/* 选中高亮边框 */}
       {isSelected && (
         <mesh position={[0, 0, 0]}>
           <boxGeometry args={[0.9, 0.9, 1.3]} />
           <meshStandardMaterial 
             color="#FCD34D" 
             transparent
             opacity={0.8}
             wireframe={true}
           />
         </mesh>
       )}
      
                    {/* 产品模型 */}
        {storageItem?.status !== "empty" && (
          <group>
            <primitive 
              object={productScene.clone()}
              scale={[2.2, 2.2, 2.2]}
              rotation={[0, -1.57, 0]}
              position={[0, level === 1 ? -0.46 : level === 2 ? -0.34 : -0.26, 0]}
            />
            {/* 为产品模型添加额外的光源 */}
            <pointLight 
              position={[0, 0, 0]} 
              intensity={0.8} 
              distance={3}
              color="#ffffff"
            />
          </group>
        )}

      {/* 如果是空货位，显示透明占位 */}
      {storageItem?.status === "empty" && (
        <mesh>
          <boxGeometry args={[0.8, 0.8, 1.2]} />
          <meshStandardMaterial 
            color="#e5e7eb"
            transparent
            opacity={0.3}
          />
        </mesh>
      )}

      {/* 气泡提示 */}
      {isSelected && (
        <Html position={[0, 1.2, 0]} center>
          <div className="bg-black text-white text-xs px-2 py-1 rounded shadow-lg whitespace-nowrap">
            <div>第{rowNumber}排-{columnNumber}列-{level}层</div>
            {storageItem?.productId && <div>产品: {storageItem.productId}</div>}
            <div>状态: {storageItem?.status === "empty" ? "空货位" : storageItem?.status === "stored" ? "已存储" : "测试中"}</div>
          </div>
        </Html>
      )}
    </group>
  );
}



export function WarehouseVisualization({
  storageData = [],
  className = "",
}: WarehouseVisualizationProps) {
  const [data, setData] = useState<StorageItem[]>(() => 
    storageData.length > 0 ? storageData : generateMockStorageData()
  );
  const [selectedSlot, setSelectedSlot] = useState<{ row: number; column: number; level: number } | null>(null);
              const [cameraPosition, setCameraPosition] = useState<[number, number, number]>([0, 3.7, -50]);
      const [cameraTarget, setCameraTarget] = useState<[number, number, number]>([0, 2.7, -45]);
  const [columnPositions, setColumnPositions] = useState<{ [key: string]: number }>({});
  const [isDraggingColumn, setIsDraggingColumn] = useState(false);
  const [draggedColumn, setDraggedColumn] = useState<{ row: number; column: number } | null>(null);
  const [visibleRows, setVisibleRows] = useState<number[]>([1, 2, 3, 4, 5, 6]); // 默认显示所有排
  const [isManualMode, setIsManualMode] = useState(false); // 手动/自动模式切换


  const shelf1Scene = useGLTF('/asserts/shelf1.glb').scene;
  const shelf2Scene = useGLTF('/asserts/shelf2.glb').scene;
  const shelf6Scene = useGLTF('/asserts/shelf6.glb').scene;
  const productScene = useGLTF('/asserts/惯组.glb').scene;

  // 相机位置控制函数
  const handleCameraToFirstRow = () => {
    setCameraPosition([0, 8, -3]);
    setCameraTarget([0, 4, -45]);
  };

  const handleCameraToMiddleRows = () => {
    setCameraPosition([0, 8, -8]);
    setCameraTarget([0, 4, -47]);
  };

  const handleCameraToLastRow = () => {
    setCameraPosition([0, 8, -3]);
    setCameraTarget([0, 4, -50]);
  };

  const handleCameraToOverview = () => {
    setCameraPosition([0, 12, -30]);
    setCameraTarget([0, 4, -47]);
  };

           // 排显示控制函数
    const showAllRows = () => {
      setVisibleRows([1, 2, 3, 4, 5, 6]);
      setCameraPosition([0, 3.7, -50]);
      setCameraTarget([0, 2.7, -45]);
    };

     const showSingleRow = (rowNumber: number) => {
     setVisibleRows([rowNumber]);
           // 根据排号调整相机位置，拉近与货架的距离
      let zOffset: number;
      if (rowNumber === 1) {
        zOffset = -45;
      } else if (rowNumber === 2) {
        zOffset = -44;
      } else if (rowNumber === 3) {
        zOffset = -43.5;
      } else if (rowNumber === 4) {
        zOffset = -43.5; // 第三排和第四排背靠背
      } else if (rowNumber === 5) {
        zOffset = -42.5;
      } else if (rowNumber === 6) {
        zOffset = -42;
      } else {
        zOffset = -45 + (rowNumber - 1) * 1;
      }
    
         // 根据排号设置相同的相机距离
     const cameraDistance = -35; // 所有排都使用相同的距离
    
    setCameraPosition([0, 8, cameraDistance]);
    setCameraTarget([0, 4, zOffset]);
  };

  // 加载保存的位置数据
  useEffect(() => {
    const loadSavedPositions = () => {
      try {
        const positions = loadPositionsLocal(POSITION_SAVE_FILE);
        if (positions) {
          setColumnPositions(positions);
        }
      } catch (error) {
        console.log('No saved positions found, using defaults');
      }
    };
    loadSavedPositions();
  }, []);

  // 保存位置数据
  const savePositions = (positions: { [key: string]: number }) => {
    try {
      savePositionsLocal({
        file: POSITION_SAVE_FILE,
        positions: positions
      });
    } catch (error) {
      console.error('Failed to save positions:', error);
    }
  };

     // 键盘事件处理
   useEffect(() => {
     const handleKeyDown = (event: KeyboardEvent) => {
       // 列移动控制：Shift + 箭头键
      if (event.shiftKey && selectedSlot) {
        const key = `${selectedSlot.row}-${selectedSlot.column}`;
        const currentX = columnPositions[key] || 0;
        let newX = currentX;

        if (event.key === 'ArrowLeft') {
          // Shift + 左箭头：向x轴负方向移动
          newX = currentX - 0.3; // 调整为原来的三分之一
          setIsDraggingColumn(true);
        } else if (event.key === 'ArrowRight') {
          // Shift + 右箭头：向x轴正方向移动
          newX = currentX + 0.3; // 调整为原来的三分之一
          setIsDraggingColumn(true);
        }

        if (newX !== currentX) {
          console.log(`移动列 ${key}: ${currentX} -> ${newX}`);
          
          // 更新所有相关排的相同列位置
          const updatedPositions = { ...columnPositions };
          
          // 如果是第二、三、四、五排，同步移动所有这四排的相同列
          if ([2, 3, 4, 5].includes(selectedSlot.row)) {
            [2, 3, 4, 5].forEach(row => {
              const syncKey = `${row}-${selectedSlot.column}`;
              updatedPositions[syncKey] = newX;
              console.log(`同步移动第${row}排-${selectedSlot.column}列: ${currentX} -> ${newX}`);
            });
          } else {
            // 其他排只移动当前列
            updatedPositions[key] = newX;
          }
          
          setColumnPositions(updatedPositions);
          
          // 计算并显示最上面产品的坐标
          const coordinates = getTopProductCoordinates(selectedSlot.row, selectedSlot.column);
          console.log(`第${selectedSlot.row}排-${selectedSlot.column}列最上面产品坐标: X=${coordinates.x.toFixed(2)}, Y=${coordinates.y.toFixed(2)}, Z=${coordinates.z.toFixed(2)}`);
          
          // 立即保存位置数据
          savePositions(updatedPositions);
          
          // 延迟重置拖拽状态
          setTimeout(() => {
            setIsDraggingColumn(false);
          }, 200);
        }
      }
    };

    window.addEventListener('keydown', handleKeyDown);
    return () => {
      window.removeEventListener('keydown', handleKeyDown);
    };
  }, [selectedSlot, columnPositions, savePositions, cameraPosition, cameraTarget]);

  // 列拖拽处理函数（保留用于兼容性）
  const handleColumnDragStart = (row: number, column: number) => {
    setIsDraggingColumn(true);
    setDraggedColumn({ row, column });
  };

  const handleColumnDragEnd = () => {
    setIsDraggingColumn(false);
    setDraggedColumn(null);
    // 保存位置数据
    savePositions(columnPositions);
  };

  const handleColumnDrag = (row: number, column: number, newX: number) => {
    const key = `${row}-${column}`;
    setColumnPositions(prev => ({
      ...prev,
      [key]: newX
    }));
  };

        // 计算每列最上面产品的坐标
    const getTopProductCoordinates = (rowNumber: number, columnNumber: number) => {
      const columns = ROW_CONFIG[rowNumber as keyof typeof ROW_CONFIG];
      const key = `${rowNumber}-${columnNumber}`;
      const savedX = columnPositions[key] || 0;
      // 镜像翻转：将第一个和倒数第一个互换，第二个和倒数第二个互换，以此类推
      const mirroredColumnIndex = columns - columnNumber;
      const baseX = -(columns * COLUMN_SPACING) / 2 + mirroredColumnIndex * COLUMN_SPACING + COLUMN_SPACING / 2;
     
     // 使用与货架渲染相同的Z位置计算
     let zPosition: number;
     if (rowNumber === 1) {
       zPosition = -45;
     } else if (rowNumber === 2) {
       zPosition = -44;
     } else if (rowNumber === 3) {
       zPosition = -43.5;
     } else if (rowNumber === 4) {
       zPosition = -43.5; // 第三排和第四排背靠背
     } else if (rowNumber === 5) {
       zPosition = -42.5;
     } else if (rowNumber === 6) {
       zPosition = -42;
     } else {
       zPosition = -45 + (rowNumber - 1) * 1;
     }
    
    // 最上面一层是第3层
    const level = 3;
    const yPosition = (level - 0.7) * 1.3 + 0.5; // 货位高度 + 基础高度
    const xPosition = baseX + savedX;
    const zPositionFinal = zPosition + 10 + rowNumber * 6; // 货位在货架中的Z位置
    
    return {
      x: xPosition,
      y: yPosition,
      z: zPositionFinal
    };
  };

  // 测试移动功能
  const testMoveColumn = () => {
    if (selectedSlot) {
      const key = `${selectedSlot.row}-${selectedSlot.column}`;
      const currentX = columnPositions[key] || 0;
      const newX = currentX + 5.0; // 向右移动5个单位
      
      console.log(`测试移动列 ${key}: ${currentX} -> ${newX}`);
      
      // 更新所有相关排的相同列位置
      const updatedPositions = { ...columnPositions };
      
      // 如果是第二、三、四、五排，同步移动所有这四排的相同列
      if ([2, 3, 4, 5].includes(selectedSlot.row)) {
        [2, 3, 4, 5].forEach(row => {
          const syncKey = `${row}-${selectedSlot.column}`;
          updatedPositions[syncKey] = newX;
          console.log(`同步移动第${row}排-${selectedSlot.column}列: ${currentX} -> ${newX}`);
        });
      } else {
        // 其他排只移动当前列
        updatedPositions[key] = newX;
      }
      
      setColumnPositions(updatedPositions);
      
      // 计算并显示最上面产品的坐标
      const coordinates = getTopProductCoordinates(selectedSlot.row, selectedSlot.column);
      console.log(`第${selectedSlot.row}排-${selectedSlot.column}列最上面产品坐标: X=${coordinates.x.toFixed(2)}, Y=${coordinates.y.toFixed(2)}, Z=${coordinates.z.toFixed(2)}`);
      
      savePositions(updatedPositions);
    }
  };



  const stats = useMemo(() => {
    const total = data.length;
    const empty = data.filter(item => item.status === "empty").length;
    const stored = data.filter(item => item.status === "stored").length;
    const testing = data.filter(item => item.status === "testing").length;
    
    return { total, empty, stored, testing };
  }, [data]);

     return (
     <div className={`w-full h-full min-h-screen ${className}`}>
       <div className="space-y-4 p-4">
                 {/* 控制面板 */}
         <div className="flex items-center justify-between p-3 bg-white rounded-lg shadow-sm">
                      <div className="flex items-center space-x-4">
              <h3 className="text-lg font-semibold text-gray-800">仓储控制系统</h3>
                             <div className="text-sm text-gray-600">
                 共 {stats.total} 个货位
                 {visibleRows.length === 6 ? (
                   <span className="ml-2 text-blue-600">(显示全部排)</span>
                 ) : (
                   <span className="ml-2 text-green-600">(显示第{visibleRows.join(',')}排)</span>
                 )}
               </div>
                             {selectedSlot && (
                 <div className="text-sm text-blue-600 font-medium">
                   已选中: 第{selectedSlot.row}排-{selectedSlot.column}列-{selectedSlot.level}层
                   {(() => {
                     const selectedItem = data.find(item => 
                       item.row === selectedSlot.row && 
                       item.column === selectedSlot.column && 
                       item.level === selectedSlot.level
                     );
                     const key = `${selectedSlot.row}-${selectedSlot.column}`;
                     const currentX = columnPositions[key] || 0;
                     const coordinates = getTopProductCoordinates(selectedSlot.row, selectedSlot.column);
                     
                                           let info = ` (X偏移: ${currentX.toFixed(1)})`;
                      info += ` | 最上面产品坐标: X=${coordinates.x.toFixed(2)}, Y=${coordinates.y.toFixed(2)}, Z=${coordinates.z.toFixed(2)}`;
                      
                      // 添加同步移动提示
                      if ([2, 3, 4, 5].includes(selectedSlot.row)) {
                        info += ` | 同步移动第2,3,4,5排`;
                      }
                      
                      if (selectedItem && selectedItem.productId) {
                        return ` - 产品: ${selectedItem.productId}${info}`;
                      } else {
                        return ` - 空货位${info}`;
                      }
                   })()}
                 </div>
               )}
            </div>
          
          <div className="flex items-center space-x-6 text-sm">
            <div className="flex items-center space-x-2">
              <div className="w-4 h-4 bg-gray-300 rounded"></div>
              <span>空货位 ({stats.empty})</span>
            </div>
            <div className="flex items-center space-x-2">
              <div className="w-4 h-4 bg-green-500 rounded"></div>
              <span>已存储 ({stats.stored})</span>
            </div>
            <div className="flex items-center space-x-2">
              <div className="w-4 h-4 bg-orange-500 rounded"></div>
              <span>测试中 ({stats.testing})</span>
            </div>
          </div>
        </div>

                                              {/* 排显示控制 */}


                                                                       {/* 排显示控制按钮 - 放在3D画布外边 */}
           <div className="flex items-center justify-center flex-wrap gap-2 p-3 bg-white rounded-lg shadow-sm mb-4">
             <span className="text-sm font-medium text-gray-700">排显示控制:</span>
             <button
               onClick={() => showSingleRow(1)}
               className={`px-3 py-1 rounded text-sm font-medium transition-all duration-200 ${
                 visibleRows.length === 1 && visibleRows[0] === 1 
                   ? 'bg-blue-600 text-white' 
                   : 'bg-blue-500 hover:bg-blue-600 text-white'
               }`}
             >
               第一排
             </button>
             <button
               onClick={() => showSingleRow(2)}
               className={`px-3 py-1 rounded text-sm font-medium transition-all duration-200 ${
                 visibleRows.length === 1 && visibleRows[0] === 2 
                   ? 'bg-green-600 text-white' 
                   : 'bg-green-500 hover:bg-green-600 text-white'
               }`}
             >
               第二排
             </button>
             <button
               onClick={() => showSingleRow(3)}
               className={`px-3 py-1 rounded text-sm font-medium transition-all duration-200 ${
                 visibleRows.length === 1 && visibleRows[0] === 3 
                   ? 'bg-purple-600 text-white' 
                   : 'bg-purple-500 hover:bg-purple-600 text-white'
               }`}
             >
               第三排
             </button>
             <button
               onClick={() => showSingleRow(4)}
               className={`px-3 py-1 rounded text-sm font-medium transition-all duration-200 ${
                 visibleRows.length === 1 && visibleRows[0] === 4 
                   ? 'bg-indigo-600 text-white' 
                   : 'bg-indigo-500 hover:bg-indigo-600 text-white'
               }`}
             >
               第四排
             </button>
             <button
               onClick={() => showSingleRow(5)}
               className={`px-3 py-1 rounded text-sm font-medium transition-all duration-200 ${
                 visibleRows.length === 1 && visibleRows[0] === 5 
                   ? 'bg-pink-600 text-white' 
                   : 'bg-pink-500 hover:bg-pink-600 text-white'
               }`}
             >
               第五排
             </button>
             <button
               onClick={() => showSingleRow(6)}
               className={`px-3 py-1 rounded text-sm font-medium transition-all duration-200 ${
                 visibleRows.length === 1 && visibleRows[0] === 6 
                   ? 'bg-teal-600 text-white' 
                   : 'bg-teal-500 hover:bg-teal-600 text-white'
               }`}
             >
               第六排
             </button>
             <button
               onClick={showAllRows}
               className={`px-3 py-1 rounded text-sm font-medium transition-all duration-200 ${
                 visibleRows.length === 6 
                   ? 'bg-orange-600 text-white' 
                   : 'bg-orange-500 hover:bg-orange-600 text-white'
               }`}
             >
               全部显示
             </button>
           </div>

                                                                         {/* 3D画布 */}
            <div className="w-full h-[600px] bg-gradient-to-br from-blue-50 to-indigo-100 rounded-lg overflow-hidden relative">
          <Canvas
            camera={{ 
              position: cameraPosition, 
              fov: 45,
              near: 0.1,
              far: 1000
            }}
            shadows
          >
            <ModelsPreloader />
            <Preload all />
            {/* 地面 */}
            <mesh position={[0, -0.5, -20]} rotation={[-Math.PI / 2, 0, 0]}>
              <planeGeometry args={[100, 100]} />
              <meshStandardMaterial color="#f3f4f6" />
            </mesh>

            

                                                                                                       {/* 货架渲染 */}
               {visibleRows.map((rowNumber) => {
                const columns = ROW_CONFIG[rowNumber as keyof typeof ROW_CONFIG];
                                 // 调整货架之间的距离：第二排和第三排之间为0.5，第三排和第四排背靠背，第四排和第五排之间为0.5
                 let zPosition: number;
                 if (rowNumber === 1) {
                   zPosition = -45;
                 } else if (rowNumber === 2) {
                   zPosition = -44;
                 } else if (rowNumber === 3) {
                   zPosition = -43.5; // 第二排和第三排之间距离为0.5
                 } else if (rowNumber === 4) {
                   zPosition = -43.5; // 第三排和第四排背靠背
                 } else if (rowNumber === 5) {
                   zPosition = -42.5; // 第四排和第五排之间距离为0.5
                 } else if (rowNumber === 6) {
                   zPosition = -42; // 第五排和第六排之间距离为0.5
                 } else {
                   zPosition = -45 + (rowNumber - 1) * 1;
                 }
               
               const rowShelf = (rowNumber === 1 ? shelf1Scene.getObjectByName('shelf') :
                 rowNumber === 6 ? shelf6Scene.getObjectByName('shelf') :
                 shelf2Scene.getObjectByName('shelf'))?.clone() ?? new THREE.Group();
               
                               return (
                  <group key={rowNumber} position={[0, 0, zPosition]}>
                    {/* 货架模型 */}
                    <primitive 
                      object={rowShelf} 
                      scale={[2, 2, 2]}
                      rotation={[Math.PI/2, 0, -Math.PI/2]}
                      position={[0, 1.7, 10+rowNumber*6]}
                    />

                                     {/* 三层货位 */}
                   {[3, 2, 1].map((level) => (
                     <group key={level} position={[0, (level-0.7) * 1.3, 0]}>
                       {/* 货位 */}
                                               {Array.from({ length: columns }, (_, colIndex) => {
                          const column = colIndex + 1;
                          const key = `${rowNumber}-${column}`;
                          const savedX = columnPositions[key] || 0;
                          // 镜像翻转：将第一个和倒数第一个互换，第二个和倒数第二个互换，以此类推
                          const mirroredColIndex = columns - 1 - colIndex;
                          const baseX = -(columns * COLUMN_SPACING) / 2 + mirroredColIndex * COLUMN_SPACING + COLUMN_SPACING / 2;
                          const slotPosition: [number, number, number] = [
                            baseX + savedX, 
                            0.5, 
                            10 + rowNumber * 6,
                          ];

                         const storageItem = data.find(
                           item => item.row === rowNumber && item.level === level && item.column === column
                         );

                         const isSelected = selectedSlot && 
                           selectedSlot.row === rowNumber && 
                           selectedSlot.column === column && 
                           selectedSlot.level === level;

                         return (
                           <DraggableColumn
                             key={`${rowNumber}-${column}-${level}`}
                             rowNumber={rowNumber}
                             columnNumber={column}
                             level={level}
                             storageItem={storageItem}
                             basePosition={slotPosition}
                             isSelected={isSelected}
                             onSelect={() => setSelectedSlot({ row: rowNumber, column, level })}
                             onDragStart={handleColumnDragStart}
                             onDragEnd={handleColumnDragEnd}
                             onDrag={handleColumnDrag}
                             productScene={productScene}
                           />
                         );
                       })}
                     </group>
                   ))}

                  
                </group>
              );
            })}

                         <ambientLight intensity={0.8} />
             <directionalLight
               position={[10, 15, 10]}
               intensity={1.5}
               castShadow
             />
             {/* 添加额外的环境光源 */}
             <hemisphereLight 
               intensity={0.6}
               groundColor="#ffffff"
               color="#87ceeb"
             />
            
                                      <OrbitControls
               enablePan={true}
               enableZoom={true}
               enableRotate={true}
               maxPolarAngle={Math.PI}
               minPolarAngle={0}
               maxDistance={1000}
               minDistance={0.1}
               target={cameraTarget}
               enableDamping={true}
               dampingFactor={0.05}
             />
             
                           
             
                        </Canvas>
             
                                                       
         </div>
         <div className="text-sm text-gray-600 bg-white p-2 rounded-lg">
            <p>• 仓储系统共600个货位，分6排显示</p>
            <p>• 第一排28列，第二/三/四/五排34列，第六排36列</p>
            <p>• 第二排与第三排背对背，第四排与第五排背对背</p>
            <p>• 灰色立方体：空货位 | 绿色立方体：已存储惯组 | 橙色立方体：测试中惯组</p>
            <p>• <strong>货位选中：</strong>点击货位可选中并查看详细信息，选中的货位会显示黄色边框高亮</p>
            <p>• <strong>双向同步：</strong>在储位展示界面选中的货位会同步在3D画布中显示选中状态，反之亦然</p>
            <p>• <strong>排显示控制：</strong>使用上方按钮选择显示特定排的货架或全部货架</p>
            <p>• <strong>漫游控制：</strong>鼠标拖拽旋转视角 | 滚轮缩放 | 右键拖拽平移</p>
          </div>

         {/* 3D画布控制按钮 */}
         <div className="flex items-center justify-center space-x-4 p-4 bg-white rounded-lg shadow-sm mb-4">
           <button
             onClick={() => setIsManualMode(!isManualMode)}
             className={`px-4 py-2 rounded-lg font-medium transition-all duration-200 ${
               isManualMode 
                 ? 'bg-blue-600 text-white hover:bg-blue-700' 
                 : 'bg-green-600 text-white hover:bg-green-700'
             }`}
           >
             {isManualMode ? '手动模式' : '自动模式'}
           </button>
           
           <button
             onClick={() => {
               if (selectedSlot) {
                 console.log(`出库测试: 第${selectedSlot.row}排-${selectedSlot.column}列-${selectedSlot.level}层`);
                 // 这里可以添加出库测试逻辑
               } else {
                 alert('请先选择一个货位');
               }
             }}
             className="px-4 py-2 bg-orange-600 text-white rounded-lg font-medium hover:bg-orange-700 transition-all duration-200"
           >
             出库测试
           </button>
           
           <button
             onClick={() => {
               console.log('测试完毕入库按钮被按下');
             }}
             className="px-4 py-2 bg-red-600 text-white rounded-lg font-medium hover:bg-red-700 transition-all duration-200"
           >
             测试完毕入库
           </button>
           
           <button
             onClick={() => {
               if (selectedSlot) {
                 console.log(`登记入库: 第${selectedSlot.row}排-${selectedSlot.column}列-${selectedSlot.level}层`);
                 // 这里可以添加入库登记逻辑
               } else {
                 alert('请先选择一个货位');
               }
             }}
             className="px-4 py-2 bg-purple-600 text-white rounded-lg font-medium hover:bg-purple-700 transition-all duration-200"
           >
             登记入库
           </button>
           
           <button
             onClick={() => {
               if (selectedSlot) {
                 console.log(`出厂: 第${selectedSlot.row}排-${selectedSlot.column}列-${selectedSlot.level}层`);
                 // 这里可以添加出厂逻辑
               } else {
                 alert('请先选择一个货位');
               }
             }}
             className="px-4 py-2 bg-indigo-600 text-white rounded-lg font-medium hover:bg-indigo-700 transition-all duration-200"
           >
             产品出厂
           </button>

           <button
             onClick={() => {
               if (selectedSlot) {
                 console.log(`盘点: 第${selectedSlot.row}排-${selectedSlot.column}列-${selectedSlot.level}层`);
                 // 这里可以添加出厂逻辑
               } else {
                 alert('请先选择一个货位');
               }
             }}
             className="px-4 py-2 bg-indigo-600 text-white rounded-lg font-medium hover:bg-indigo-700 transition-all duration-200"
           >
             指定货位盘点
           </button>
         </div>

                                                         {/* 说明文字 */}
          

          {/* 网页版储位展示界面 */}
          <div className="bg-white rounded-lg shadow-sm p-4">
            <h3 className="text-lg font-semibold text-gray-800 mb-4">储位展示界面</h3>
            <div className="space-y-6">
                             {/* 第一排 */}
               <div className="border border-gray-200 rounded-lg p-4">
                 <h4 className="text-md font-medium text-blue-600 mb-3">第一排 (28列 × 3层)</h4>
                 
                 {/* 第三层 */}
                 <div className="mb-2">
                   <h5 className="text-sm font-medium text-gray-600 mb-2">第三层</h5>
                   <div className="flex gap-2">
                     {/* 第1-13列 */}
                     {Array.from({ length: 13 }, (_, colIndex) => {
                       const column = colIndex + 1;
                       const storageItem = data.find(
                         item => item.row === 1 && item.column === column && item.level === 3
                       );
                       const isSelected = selectedSlot && 
                         selectedSlot.row === 1 && 
                         selectedSlot.column === column && 
                         selectedSlot.level === 3;
                       
                       return (
                         <div
                           key={`1-3-${column}`}
                           className={`
                             w-8 h-8 border border-gray-300 rounded text-xs flex items-center justify-center cursor-pointer
                             ${isSelected ? 'border-yellow-400 border-2 bg-yellow-100' : ''}
                             ${storageItem?.status === "empty" ? 'bg-gray-200' : 
                               storageItem?.status === "stored" ? 'bg-green-200' : 'bg-orange-200'}
                             text-gray-700
                           `}
                           title={`第1排-${column}列-3层: ${storageItem?.productId || '空货位'}`}
                           onClick={() => setSelectedSlot({ row: 1, column, level: 3 })}
                         >
                           {storageItem?.productId ? storageItem.productId.slice(-3) : ''}
                         </div>
                       );
                     })}
                     
                     {/* 接驳台间隔 - 4个div宽度 */}
                     {Array.from({ length: 4 }, (_, index) => (
                       <div key={`dock-gap-3-${index}`} className="w-8 h-8"></div>
                     ))}
                     
                     {/* 第14-15列 */}
                     {Array.from({ length: 2 }, (_, colIndex) => {
                       const column = colIndex + 14;
                       const storageItem = data.find(
                         item => item.row === 1 && item.column === column && item.level === 3
                       );
                       const isSelected = selectedSlot && 
                         selectedSlot.row === 1 && 
                         selectedSlot.column === column && 
                         selectedSlot.level === 3;
                       
                       return (
                         <div
                           key={`1-3-${column}`}
                           className={`
                             w-8 h-8 border border-gray-300 rounded text-xs flex items-center justify-center cursor-pointer
                             ${isSelected ? 'border-yellow-400 border-2 bg-yellow-100' : ''}
                             ${storageItem?.status === "empty" ? 'bg-gray-200' : 
                               storageItem?.status === "stored" ? 'bg-green-200' : 'bg-orange-200'}
                             text-gray-700
                           `}
                           title={`第1排-${column}列-3层: ${storageItem?.productId || '空货位'}`}
                           onClick={() => setSelectedSlot({ row: 1, column, level: 3 })}
                         >
                           {storageItem?.productId ? storageItem.productId.slice(-3) : ''}
                         </div>
                       );
                     })}
                     
                     {/* 过道间隔 - 2个div宽度 */}
                     {Array.from({ length: 2 }, (_, index) => (
                       <div key={`aisle-gap-3-${index}`} className="w-8 h-8"></div>
                     ))}
                     
                     {/* 第16-28列 */}
                     {Array.from({ length: 13 }, (_, colIndex) => {
                       const column = colIndex + 16;
                       const storageItem = data.find(
                         item => item.row === 1 && item.column === column && item.level === 3
                       );
                       const isSelected = selectedSlot && 
                         selectedSlot.row === 1 && 
                         selectedSlot.column === column && 
                         selectedSlot.level === 3;
                       
                       return (
                         <div
                           key={`1-3-${column}`}
                           className={`
                             w-8 h-8 border border-gray-300 rounded text-xs flex items-center justify-center cursor-pointer
                             ${isSelected ? 'border-yellow-400 border-2 bg-yellow-100' : ''}
                             ${storageItem?.status === "empty" ? 'bg-gray-200' : 
                               storageItem?.status === "stored" ? 'bg-green-200' : 'bg-orange-200'}
                             text-gray-700
                           `}
                           title={`第1排-${column}列-3层: ${storageItem?.productId || '空货位'}`}
                           onClick={() => setSelectedSlot({ row: 1, column, level: 3 })}
                         >
                           {storageItem?.productId ? storageItem.productId.slice(-3) : ''}
                         </div>
                       );
                     })}
                   </div>
                 </div>

                 {/* 第二层 */}
                 <div className="mb-2">
                   <h5 className="text-sm font-medium text-gray-600 mb-2">第二层</h5>
                   <div className="flex gap-2">
                     {/* 第1-13列 */}
                     {Array.from({ length: 13 }, (_, colIndex) => {
                       const column = colIndex + 1;
                       const storageItem = data.find(
                         item => item.row === 1 && item.column === column && item.level === 2
                       );
                       const isSelected = selectedSlot && 
                         selectedSlot.row === 1 && 
                         selectedSlot.column === column && 
                         selectedSlot.level === 2;
                       
                       return (
                         <div
                           key={`1-2-${column}`}
                           className={`
                             w-8 h-8 border border-gray-300 rounded text-xs flex items-center justify-center cursor-pointer
                             ${isSelected ? 'border-yellow-400 border-2 bg-yellow-100' : ''}
                             ${storageItem?.status === "empty" ? 'bg-gray-200' : 
                               storageItem?.status === "stored" ? 'bg-green-200' : 'bg-orange-200'}
                             text-gray-700
                           `}
                           title={`第1排-${column}列-2层: ${storageItem?.productId || '空货位'}`}
                           onClick={() => setSelectedSlot({ row: 1, column, level: 2 })}
                         >
                           {storageItem?.productId ? storageItem.productId.slice(-3) : ''}
                         </div>
                       );
                     })}
                     
                     {/* 接驳台间隔 - 4个div宽度 */}
                     {Array.from({ length: 4 }, (_, index) => (
                       <div key={`dock-gap-2-${index}`} className="w-8 h-8"></div>
                     ))}
                     
                     {/* 第14-15列 */}
                     {Array.from({ length: 2 }, (_, colIndex) => {
                       const column = colIndex + 14;
                       const storageItem = data.find(
                         item => item.row === 1 && item.column === column && item.level === 2
                       );
                       const isSelected = selectedSlot && 
                         selectedSlot.row === 1 && 
                         selectedSlot.column === column && 
                         selectedSlot.level === 2;
                       
                       return (
                         <div
                           key={`1-2-${column}`}
                           className={`
                             w-8 h-8 border border-gray-300 rounded text-xs flex items-center justify-center cursor-pointer
                             ${isSelected ? 'border-yellow-400 border-2 bg-yellow-100' : ''}
                             ${storageItem?.status === "empty" ? 'bg-gray-200' : 
                               storageItem?.status === "stored" ? 'bg-green-200' : 'bg-orange-200'}
                             text-gray-700
                           `}
                           title={`第1排-${column}列-2层: ${storageItem?.productId || '空货位'}`}
                           onClick={() => setSelectedSlot({ row: 1, column, level: 2 })}
                         >
                           {storageItem?.productId ? storageItem.productId.slice(-3) : ''}
                         </div>
                       );
                     })}
                     
                     {/* 过道间隔 - 2个div宽度 */}
                     {Array.from({ length: 2 }, (_, index) => (
                       <div key={`aisle-gap-2-${index}`} className="w-8 h-8"></div>
                     ))}
                     
                     {/* 第16-28列 */}
                     {Array.from({ length: 13 }, (_, colIndex) => {
                       const column = colIndex + 16;
                       const storageItem = data.find(
                         item => item.row === 1 && item.column === column && item.level === 2
                       );
                       const isSelected = selectedSlot && 
                         selectedSlot.row === 1 && 
                         selectedSlot.column === column && 
                         selectedSlot.level === 2;
                       
                       return (
                         <div
                           key={`1-2-${column}`}
                           className={`
                             w-8 h-8 border border-gray-300 rounded text-xs flex items-center justify-center cursor-pointer
                             ${isSelected ? 'border-yellow-400 border-2 bg-yellow-100' : ''}
                             ${storageItem?.status === "empty" ? 'bg-gray-200' : 
                               storageItem?.status === "stored" ? 'bg-green-200' : 'bg-orange-200'}
                             text-gray-700
                           `}
                           title={`第1排-${column}列-2层: ${storageItem?.productId || '空货位'}`}
                           onClick={() => setSelectedSlot({ row: 1, column, level: 2 })}
                         >
                           {storageItem?.productId ? storageItem.productId.slice(-3) : ''}
                         </div>
                       );
                     })}
                   </div>
                 </div>

                 {/* 第一层 */}
                 <div>
                   <h5 className="text-sm font-medium text-gray-600 mb-2">第一层</h5>
                   <div className="flex gap-2">
                     {/* 第1-13列 */}
                     {Array.from({ length: 13 }, (_, colIndex) => {
                       const column = colIndex + 1;
                       const storageItem = data.find(
                         item => item.row === 1 && item.column === column && item.level === 1
                       );
                       const isSelected = selectedSlot && 
                         selectedSlot.row === 1 && 
                         selectedSlot.column === column && 
                         selectedSlot.level === 1;
                       
                       return (
                         <div
                           key={`1-1-${column}`}
                           className={`
                             w-8 h-8 border border-gray-300 rounded text-xs flex items-center justify-center cursor-pointer
                             ${isSelected ? 'border-yellow-400 border-2 bg-yellow-100' : ''}
                             ${storageItem?.status === "empty" ? 'bg-gray-200' : 
                               storageItem?.status === "stored" ? 'bg-green-200' : 'bg-orange-200'}
                             text-gray-700
                           `}
                           title={`第1排-${column}列-1层: ${storageItem?.productId || '空货位'}`}
                           onClick={() => setSelectedSlot({ row: 1, column, level: 1 })}
                         >
                           {storageItem?.productId ? storageItem.productId.slice(-3) : ''}
                         </div>
                       );
                     })}
                     
                     {/* 接驳台间隔 - 4个div宽度 */}
                     {Array.from({ length: 4 }, (_, index) => (
                       <div key={`dock-gap-1-${index}`} className="w-8 h-8"></div>
                     ))}
                     
                     {/* 第14-15列 */}
                     {Array.from({ length: 2 }, (_, colIndex) => {
                       const column = colIndex + 14;
                       const storageItem = data.find(
                         item => item.row === 1 && item.column === column && item.level === 1
                       );
                       const isSelected = selectedSlot && 
                         selectedSlot.row === 1 && 
                         selectedSlot.column === column && 
                         selectedSlot.level === 1;
                       
                       return (
                         <div
                           key={`1-1-${column}`}
                           className={`
                             w-8 h-8 border border-gray-300 rounded text-xs flex items-center justify-center cursor-pointer
                             ${isSelected ? 'border-yellow-400 border-2 bg-yellow-100' : ''}
                             ${storageItem?.status === "empty" ? 'bg-gray-200' : 
                               storageItem?.status === "stored" ? 'bg-green-200' : 'bg-orange-200'}
                             text-gray-700
                           `}
                           title={`第1排-${column}列-1层: ${storageItem?.productId || '空货位'}`}
                           onClick={() => setSelectedSlot({ row: 1, column, level: 1 })}
                         >
                           {storageItem?.productId ? storageItem.productId.slice(-3) : ''}
                         </div>
                       );
                     })}
                     
                     {/* 过道间隔 - 2个div宽度 */}
                     {Array.from({ length: 2 }, (_, index) => (
                       <div key={`aisle-gap-1-${index}`} className="w-8 h-8"></div>
                     ))}
                     
                     {/* 第16-28列 */}
                     {Array.from({ length: 13 }, (_, colIndex) => {
                       const column = colIndex + 16;
                       const storageItem = data.find(
                         item => item.row === 1 && item.column === column && item.level === 1
                       );
                       const isSelected = selectedSlot && 
                         selectedSlot.row === 1 && 
                         selectedSlot.column === column && 
                         selectedSlot.level === 1;
                       
                       return (
                         <div
                           key={`1-1-${column}`}
                           className={`
                             w-8 h-8 border border-gray-300 rounded text-xs flex items-center justify-center cursor-pointer
                             ${isSelected ? 'border-yellow-400 border-2 bg-yellow-100' : ''}
                             ${storageItem?.status === "empty" ? 'bg-gray-200' : 
                               storageItem?.status === "stored" ? 'bg-green-200' : 'bg-orange-200'}
                             text-gray-700
                           `}
                           title={`第1排-${column}列-1层: ${storageItem?.productId || '空货位'}`}
                           onClick={() => setSelectedSlot({ row: 1, column, level: 1 })}
                         >
                           {storageItem?.productId ? storageItem.productId.slice(-3) : ''}
                         </div>
                       );
                     })}
                   </div>
                 </div>


               </div>

                             {/* 第二排 */}
               <div className="border border-gray-200 rounded-lg p-4">
                 <h4 className="text-md font-medium text-green-600 mb-3">第二排 (34列 × 3层)</h4>
                 
                 {/* 第三层 */}
                 <div className="mb-2">
                   <h5 className="text-sm font-medium text-gray-600 mb-2">第三层</h5>
                   <div className="flex gap-2">
                     {/* 第1-21列 */}
                     {Array.from({ length: 21 }, (_, colIndex) => {
                       const column = colIndex + 1;
                       const storageItem = data.find(
                         item => item.row === 2 && item.column === column && item.level === 3
                       );
                       const isSelected = selectedSlot &&
                         selectedSlot.row === 2 &&
                         selectedSlot.column === column &&
                         selectedSlot.level === 3;

                       return (
                         <div
                           key={`2-3-${column}`}
                           className={`
                             w-8 h-8 border border-gray-300 rounded text-xs flex items-center justify-center cursor-pointer
                             ${isSelected ? 'border-yellow-400 border-2 bg-yellow-100' : ''}
                             ${storageItem?.status === "empty" ? 'bg-gray-200' :
                               storageItem?.status === "stored" ? 'bg-green-200' : 'bg-orange-200'}
                             text-gray-700
                           `}
                           title={`第2排-${column}列-3层: ${storageItem?.productId || '空货位'}`}
                           onClick={() => setSelectedSlot({ row: 2, column, level: 3 })}
                         >
                           {storageItem?.productId ? storageItem.productId.slice(-3) : ''}
                         </div>
                       );
                     })}

                     {/* 新增间隔 - 2个div宽度 */}
                     {Array.from({ length: 2 }, (_, index) => (
                       <div key={`new-gap-2-3-${index}`} className="w-8 h-8"></div>
                     ))}

                     {/* 第22-23列 */}
                     {Array.from({ length: 2 }, (_, colIndex) => {
                       const column = colIndex + 22;
                       const storageItem = data.find(
                         item => item.row === 2 && item.column === column && item.level === 3
                       );
                       const isSelected = selectedSlot &&
                         selectedSlot.row === 2 &&
                         selectedSlot.column === column &&
                         selectedSlot.level === 3;

                       return (
                         <div
                           key={`2-3-${column}`}
                           className={`
                             w-8 h-8 border border-gray-300 rounded text-xs flex items-center justify-center cursor-pointer
                             ${isSelected ? 'border-yellow-400 border-2 bg-yellow-100' : ''}
                             ${storageItem?.status === "empty" ? 'bg-gray-200' :
                               storageItem?.status === "stored" ? 'bg-green-200' : 'bg-orange-200'}
                             text-gray-700
                           `}
                           title={`第2排-${column}列-3层: ${storageItem?.productId || '空货位'}`}
                           onClick={() => setSelectedSlot({ row: 2, column, level: 3 })}
                         >
                           {storageItem?.productId ? storageItem.productId.slice(-3) : ''}
                         </div>
                       );
                     })}

                     {/* 原有过道间隔 - 2个div宽度 */}
                     {Array.from({ length: 2 }, (_, index) => (
                       <div key={`aisle-gap-2-3-${index}`} className="w-8 h-8"></div>
                     ))}

                     {/* 第24-34列 */}
                     {Array.from({ length: 11 }, (_, colIndex) => {
                       const column = colIndex + 24;
                       const storageItem = data.find(
                         item => item.row === 2 && item.column === column && item.level === 3
                       );
                       const isSelected = selectedSlot &&
                         selectedSlot.row === 2 &&
                         selectedSlot.column === column &&
                         selectedSlot.level === 3;

                       return (
                         <div
                           key={`2-3-${column}`}
                           className={`
                             w-8 h-8 border border-gray-300 rounded text-xs flex items-center justify-center cursor-pointer
                             ${isSelected ? 'border-yellow-400 border-2 bg-yellow-100' : ''}
                             ${storageItem?.status === "empty" ? 'bg-gray-200' :
                               storageItem?.status === "stored" ? 'bg-green-200' : 'bg-orange-200'}
                             text-gray-700
                           `}
                           title={`第2排-${column}列-3层: ${storageItem?.productId || '空货位'}`}
                           onClick={() => setSelectedSlot({ row: 2, column, level: 3 })}
                         >
                           {storageItem?.productId ? storageItem.productId.slice(-3) : ''}
                         </div>
                       );
                     })}
                   </div>
                 </div>

                 {/* 第二层 */}
                 <div className="mb-2">
                   <h5 className="text-sm font-medium text-gray-600 mb-2">第二层</h5>
                   <div className="flex gap-2">
                     {/* 第1-21列 */}
                     {Array.from({ length: 21 }, (_, colIndex) => {
                       const column = colIndex + 1;
                       const storageItem = data.find(
                         item => item.row === 2 && item.column === column && item.level === 2
                       );
                       const isSelected = selectedSlot &&
                         selectedSlot.row === 2 &&
                         selectedSlot.column === column &&
                         selectedSlot.level === 2;

                       return (
                         <div
                           key={`2-2-${column}`}
                           className={`
                             w-8 h-8 border border-gray-300 rounded text-xs flex items-center justify-center cursor-pointer
                             ${isSelected ? 'border-yellow-400 border-2 bg-yellow-100' : ''}
                             ${storageItem?.status === "empty" ? 'bg-gray-200' :
                               storageItem?.status === "stored" ? 'bg-green-200' : 'bg-orange-200'}
                             text-gray-700
                           `}
                           title={`第2排-${column}列-2层: ${storageItem?.productId || '空货位'}`}
                           onClick={() => setSelectedSlot({ row: 2, column, level: 2 })}
                         >
                           {storageItem?.productId ? storageItem.productId.slice(-3) : ''}
                         </div>
                       );
                     })}

                     {/* 新增间隔 - 2个div宽度 */}
                     {Array.from({ length: 2 }, (_, index) => (
                       <div key={`new-gap-2-2-${index}`} className="w-8 h-8"></div>
                     ))}

                     {/* 第22-23列 */}
                     {Array.from({ length: 2 }, (_, colIndex) => {
                       const column = colIndex + 22;
                       const storageItem = data.find(
                         item => item.row === 2 && item.column === column && item.level === 2
                       );
                       const isSelected = selectedSlot &&
                         selectedSlot.row === 2 &&
                         selectedSlot.column === column &&
                         selectedSlot.level === 2;

                       return (
                         <div
                           key={`2-2-${column}`}
                           className={`
                             w-8 h-8 border border-gray-300 rounded text-xs flex items-center justify-center cursor-pointer
                             ${isSelected ? 'border-yellow-400 border-2 bg-yellow-100' : ''}
                             ${storageItem?.status === "empty" ? 'bg-gray-200' :
                               storageItem?.status === "stored" ? 'bg-green-200' : 'bg-orange-200'}
                             text-gray-700
                           `}
                           title={`第2排-${column}列-2层: ${storageItem?.productId || '空货位'}`}
                           onClick={() => setSelectedSlot({ row: 2, column, level: 2 })}
                         >
                           {storageItem?.productId ? storageItem.productId.slice(-3) : ''}
                         </div>
                       );
                     })}

                     {/* 原有过道间隔 - 2个div宽度 */}
                     {Array.from({ length: 2 }, (_, index) => (
                       <div key={`aisle-gap-2-2-${index}`} className="w-8 h-8"></div>
                     ))}

                     {/* 第24-34列 */}
                     {Array.from({ length: 11 }, (_, colIndex) => {
                       const column = colIndex + 24;
                       const storageItem = data.find(
                         item => item.row === 2 && item.column === column && item.level === 2
                       );
                       const isSelected = selectedSlot &&
                         selectedSlot.row === 2 &&
                         selectedSlot.column === column &&
                         selectedSlot.level === 2;

                       return (
                         <div
                           key={`2-2-${column}`}
                           className={`
                             w-8 h-8 border border-gray-300 rounded text-xs flex items-center justify-center cursor-pointer
                             ${isSelected ? 'border-yellow-400 border-2 bg-yellow-100' : ''}
                             ${storageItem?.status === "empty" ? 'bg-gray-200' :
                               storageItem?.status === "stored" ? 'bg-green-200' : 'bg-orange-200'}
                             text-gray-700
                           `}
                           title={`第2排-${column}列-2层: ${storageItem?.productId || '空货位'}`}
                           onClick={() => setSelectedSlot({ row: 2, column, level: 2 })}
                         >
                           {storageItem?.productId ? storageItem.productId.slice(-3) : ''}
                         </div>
                       );
                     })}
                   </div>
                 </div>

                 {/* 第一层 */}
                 <div>
                   <h5 className="text-sm font-medium text-gray-600 mb-2">第一层</h5>
                   <div className="flex gap-2">
                     {/* 第1-21列 */}
                     {Array.from({ length: 21 }, (_, colIndex) => {
                       const column = colIndex + 1;
                       const storageItem = data.find(
                         item => item.row === 2 && item.column === column && item.level === 1
                       );
                       const isSelected = selectedSlot &&
                         selectedSlot.row === 2 &&
                         selectedSlot.column === column &&
                         selectedSlot.level === 1;

                       return (
                         <div
                           key={`2-1-${column}`}
                           className={`
                             w-8 h-8 border border-gray-300 rounded text-xs flex items-center justify-center cursor-pointer
                             ${isSelected ? 'border-yellow-400 border-2 bg-yellow-100' : ''}
                             ${storageItem?.status === "empty" ? 'bg-gray-200' :
                               storageItem?.status === "stored" ? 'bg-green-200' : 'bg-orange-200'}
                             text-gray-700
                           `}
                           title={`第2排-${column}列-1层: ${storageItem?.productId || '空货位'}`}
                           onClick={() => setSelectedSlot({ row: 2, column, level: 1 })}
                         >
                           {storageItem?.productId ? storageItem.productId.slice(-3) : ''}
                         </div>
                       );
                     })}

                     {/* 新增间隔 - 2个div宽度 */}
                     {Array.from({ length: 2 }, (_, index) => (
                       <div key={`new-gap-2-1-${index}`} className="w-8 h-8"></div>
                     ))}

                     {/* 第22-23列 */}
                     {Array.from({ length: 2 }, (_, colIndex) => {
                       const column = colIndex + 22;
                       const storageItem = data.find(
                         item => item.row === 2 && item.column === column && item.level === 1
                       );
                       const isSelected = selectedSlot &&
                         selectedSlot.row === 2 &&
                         selectedSlot.column === column &&
                         selectedSlot.level === 1;

                       return (
                         <div
                           key={`2-1-${column}`}
                           className={`
                             w-8 h-8 border border-gray-300 rounded text-xs flex items-center justify-center cursor-pointer
                             ${isSelected ? 'border-yellow-400 border-2 bg-yellow-100' : ''}
                             ${storageItem?.status === "empty" ? 'bg-gray-200' :
                               storageItem?.status === "stored" ? 'bg-green-200' : 'bg-orange-200'}
                             text-gray-700
                           `}
                           title={`第2排-${column}列-1层: ${storageItem?.productId || '空货位'}`}
                           onClick={() => setSelectedSlot({ row: 2, column, level: 1 })}
                         >
                           {storageItem?.productId ? storageItem.productId.slice(-3) : ''}
                         </div>
                       );
                     })}

                     {/* 原有过道间隔 - 2个div宽度 */}
                     {Array.from({ length: 2 }, (_, index) => (
                       <div key={`aisle-gap-2-1-${index}`} className="w-8 h-8"></div>
                     ))}

                     {/* 第24-34列 */}
                     {Array.from({ length: 11 }, (_, colIndex) => {
                       const column = colIndex + 24;
                       const storageItem = data.find(
                         item => item.row === 2 && item.column === column && item.level === 1
                       );
                       const isSelected = selectedSlot &&
                         selectedSlot.row === 2 &&
                         selectedSlot.column === column &&
                         selectedSlot.level === 1;

                       return (
                         <div
                           key={`2-1-${column}`}
                           className={`
                             w-8 h-8 border border-gray-300 rounded text-xs flex items-center justify-center cursor-pointer
                             ${isSelected ? 'border-yellow-400 border-2 bg-yellow-100' : ''}
                             ${storageItem?.status === "empty" ? 'bg-gray-200' :
                               storageItem?.status === "stored" ? 'bg-green-200' : 'bg-orange-200'}
                             text-gray-700
                           `}
                           title={`第2排-${column}列-1层: ${storageItem?.productId || '空货位'}`}
                           onClick={() => setSelectedSlot({ row: 2, column, level: 1 })}
                         >
                           {storageItem?.productId ? storageItem.productId.slice(-3) : ''}
                         </div>
                       );
                     })}
                   </div>
                 </div>
               </div>

                             {/* 第三排 */}
               <div className="border border-gray-200 rounded-lg p-4">
                 <h4 className="text-md font-medium text-purple-600 mb-3">第三排 (34列 × 3层)</h4>
                 
                 {/* 第三层 */}
                 <div className="mb-2">
                   <h5 className="text-sm font-medium text-gray-600 mb-2">第三层</h5>
                   <div className="flex gap-2">
                     {/* 第1-23列 */}
                     {Array.from({ length: 23 }, (_, colIndex) => {
                       const column = colIndex + 1;
                       const storageItem = data.find(
                         item => item.row === 3 && item.column === column && item.level === 3
                       );
                       const isSelected = selectedSlot &&
                         selectedSlot.row === 3 &&
                         selectedSlot.column === column &&
                         selectedSlot.level === 3;

                       return (
                         <div
                           key={`3-3-${column}`}
                           className={`
                             w-8 h-8 border border-gray-300 rounded text-xs flex items-center justify-center cursor-pointer
                             ${isSelected ? 'border-yellow-400 border-2 bg-yellow-100' : ''}
                             ${storageItem?.status === "empty" ? 'bg-gray-200' :
                               storageItem?.status === "stored" ? 'bg-green-200' : 'bg-orange-200'}
                             text-gray-700
                           `}
                           title={`第3排-${column}列-3层: ${storageItem?.productId || '空货位'}`}
                           onClick={() => setSelectedSlot({ row: 3, column, level: 3 })}
                         >
                           {storageItem?.productId ? storageItem.productId.slice(-3) : ''}
                         </div>
                       );
                     })}

                     {/* 过道间隔 - 2个div宽度 */}
                     {Array.from({ length: 2 }, (_, index) => (
                       <div key={`aisle-gap-3-3-${index}`} className="w-8 h-8"></div>
                     ))}

                     {/* 第24-34列 */}
                     {Array.from({ length: 11 }, (_, colIndex) => {
                       const column = colIndex + 24;
                       const storageItem = data.find(
                         item => item.row === 3 && item.column === column && item.level === 3
                       );
                       const isSelected = selectedSlot &&
                         selectedSlot.row === 3 &&
                         selectedSlot.column === column &&
                         selectedSlot.level === 3;

                       return (
                         <div
                           key={`3-3-${column}`}
                           className={`
                             w-8 h-8 border border-gray-300 rounded text-xs flex items-center justify-center cursor-pointer
                             ${isSelected ? 'border-yellow-400 border-2 bg-yellow-100' : ''}
                             ${storageItem?.status === "empty" ? 'bg-gray-200' :
                               storageItem?.status === "stored" ? 'bg-green-200' : 'bg-orange-200'}
                             text-gray-700
                           `}
                           title={`第3排-${column}列-3层: ${storageItem?.productId || '空货位'}`}
                           onClick={() => setSelectedSlot({ row: 3, column, level: 3 })}
                         >
                           {storageItem?.productId ? storageItem.productId.slice(-3) : ''}
                         </div>
                       );
                     })}
                   </div>
                 </div>

                 {/* 第二层 */}
                 <div className="mb-2">
                   <h5 className="text-sm font-medium text-gray-600 mb-2">第二层</h5>
                   <div className="grid grid-cols-34 gap-1">
                     {Array.from({ length: 34 }, (_, colIndex) => {
                       const column = colIndex + 1;
                       const storageItem = data.find(
                         item => item.row === 3 && item.column === column && item.level === 2
                       );
                       const isSelected = selectedSlot && 
                         selectedSlot.row === 3 && 
                         selectedSlot.column === column && 
                         selectedSlot.level === 2;
                       
                       return (
                         <div
                           key={`3-2-${column}`}
                           className={`
                             w-8 h-8 border border-gray-300 rounded text-xs flex items-center justify-center cursor-pointer
                             ${isSelected ? 'border-yellow-400 border-2 bg-yellow-100' : ''}
                             ${storageItem?.status === "empty" ? 'bg-gray-200' : 
                               storageItem?.status === "stored" ? 'bg-green-200' : 'bg-orange-200'}
                             text-gray-700
                           `}
                           title={`第3排-${column}列-2层: ${storageItem?.productId || '空货位'}`}
                           onClick={() => setSelectedSlot({ row: 3, column, level: 2 })}
                         >
                           {storageItem?.productId ? storageItem.productId.slice(-3) : ''}
                         </div>
                       );
                     })}
                   </div>
                 </div>

                 {/* 第一层 */}
                 <div>
                   <h5 className="text-sm font-medium text-gray-600 mb-2">第一层</h5>
                   <div className="flex gap-1">
                     {/* 第1-23列 */}
                     {Array.from({ length: 23 }, (_, colIndex) => {
                       const column = colIndex + 1;
                       const storageItem = data.find(
                         item => item.row === 3 && item.column === column && item.level === 1
                       );
                       const isSelected = selectedSlot &&
                         selectedSlot.row === 3 &&
                         selectedSlot.column === column &&
                         selectedSlot.level === 1;

                       return (
                         <div
                           key={`3-1-${column}`}
                           className={`
                             w-8 h-8 border border-gray-300 rounded text-xs flex items-center justify-center cursor-pointer
                             ${isSelected ? 'border-yellow-400 border-2 bg-yellow-100' : ''}
                             ${storageItem?.status === "empty" ? 'bg-gray-200' :
                               storageItem?.status === "stored" ? 'bg-green-200' : 'bg-orange-200'}
                             text-gray-700
                           `}
                           title={`第3排-${column}列-1层: ${storageItem?.productId || '空货位'}`}
                           onClick={() => setSelectedSlot({ row: 3, column, level: 1 })}
                         >
                           {storageItem?.productId ? storageItem.productId.slice(-3) : ''}
                         </div>
                       );
                     })}

                     {/* 过道间隔 - 2个div宽度 */}
                     {Array.from({ length: 2 }, (_, index) => (
                       <div key={`aisle-gap-3-1-${index}`} className="w-8 h-8"></div>
                     ))}

                     {/* 第24-34列 */}
                     {Array.from({ length: 11 }, (_, colIndex) => {
                       const column = colIndex + 24;
                       const storageItem = data.find(
                         item => item.row === 3 && item.column === column && item.level === 1
                       );
                       const isSelected = selectedSlot &&
                         selectedSlot.row === 3 &&
                         selectedSlot.column === column &&
                         selectedSlot.level === 1;

                       return (
                         <div
                           key={`3-1-${column}`}
                           className={`
                             w-8 h-8 border border-gray-300 rounded text-xs flex items-center justify-center cursor-pointer
                             ${isSelected ? 'border-yellow-400 border-2 bg-yellow-100' : ''}
                             ${storageItem?.status === "empty" ? 'bg-gray-200' :
                               storageItem?.status === "stored" ? 'bg-green-200' : 'bg-orange-200'}
                             text-gray-700
                           `}
                           title={`第3排-${column}列-1层: ${storageItem?.productId || '空货位'}`}
                           onClick={() => setSelectedSlot({ row: 3, column, level: 1 })}
                         >
                           {storageItem?.productId ? storageItem.productId.slice(-3) : ''}
                         </div>
                       );
                     })}
                   </div>
                 </div>
               </div>

               {/* 第四排 */}
               <div className="border border-gray-200 rounded-lg p-4">
                 <h4 className="text-md font-medium text-indigo-600 mb-3">第四排 (34列 × 3层)</h4>
                 
                 {/* 第三层 */}
                 <div className="mb-2">
                   <h5 className="text-sm font-medium text-gray-600 mb-2">第三层</h5>
                   <div className="flex gap-1">
                     {/* 第1-21列 */}
                     {Array.from({ length: 21 }, (_, colIndex) => {
                       const column = colIndex + 1;
                       const storageItem = data.find(
                         item => item.row === 4 && item.column === column && item.level === 3
                       );
                       const isSelected = selectedSlot && 
                         selectedSlot.row === 4 && 
                         selectedSlot.column === column && 
                         selectedSlot.level === 3;
                       
                       return (
                         <div
                           key={`4-3-${column}`}
                           className={`
                             w-8 h-8 border border-gray-300 rounded text-xs flex items-center justify-center cursor-pointer
                             ${isSelected ? 'border-yellow-400 border-2 bg-yellow-100' : ''}
                             ${storageItem?.status === "empty" ? 'bg-gray-200' : 
                               storageItem?.status === "stored" ? 'bg-green-200' : 'bg-orange-200'}
                             text-gray-700
                           `}
                           title={`第4排-${column}列-3层: ${storageItem?.productId || '空货位'}`}
                           onClick={() => setSelectedSlot({ row: 4, column, level: 3 })}
                         >
                           {storageItem?.productId ? storageItem.productId.slice(-3) : ''}
                         </div>
                       );
                     })}
                     
                     {/* 过道间隔 - 2个div宽度 */}
                     {Array.from({ length: 2 }, (_, index) => (
                       <div key={`aisle-gap-4-3-${index}`} className="w-8 h-8"></div>
                     ))}
                     
                     {/* 第24-34列 */}
                     {Array.from({ length: 11 }, (_, colIndex) => {
                       const column = colIndex + 24;
                       const storageItem = data.find(
                         item => item.row === 4 && item.column === column && item.level === 3
                       );
                       const isSelected = selectedSlot && 
                         selectedSlot.row === 4 && 
                         selectedSlot.column === column && 
                         selectedSlot.level === 3;
                       
                       return (
                         <div
                           key={`4-3-${column}`}
                           className={`
                             w-8 h-8 border border-gray-300 rounded text-xs flex items-center justify-center cursor-pointer
                             ${isSelected ? 'border-yellow-400 border-2 bg-yellow-100' : ''}
                             ${storageItem?.status === "empty" ? 'bg-gray-200' : 
                               storageItem?.status === "stored" ? 'bg-green-200' : 'bg-orange-200'}
                             text-gray-700
                           `}
                           title={`第4排-${column}列-3层: ${storageItem?.productId || '空货位'}`}
                           onClick={() => setSelectedSlot({ row: 4, column, level: 3 })}
                         >
                           {storageItem?.productId ? storageItem.productId.slice(-3) : ''}
                         </div>
                       );
                     })}
                   </div>
                 </div>

                 {/* 第二层 */}
                 <div className="mb-2">
                   <h5 className="text-sm font-medium text-gray-600 mb-2">第二层</h5>
                   <div className="flex gap-1">
                     {/* 第1-21列 */}
                     {Array.from({ length: 21 }, (_, colIndex) => {
                       const column = colIndex + 1;
                       const storageItem = data.find(
                         item => item.row === 4 && item.column === column && item.level === 2
                       );
                       const isSelected = selectedSlot && 
                         selectedSlot.row === 4 && 
                         selectedSlot.column === column && 
                         selectedSlot.level === 2;
                       
                       return (
                         <div
                           key={`4-2-${column}`}
                           className={`
                             w-8 h-8 border border-gray-300 rounded text-xs flex items-center justify-center cursor-pointer
                             ${isSelected ? 'border-yellow-400 border-2 bg-yellow-100' : ''}
                             ${storageItem?.status === "empty" ? 'bg-gray-200' : 
                               storageItem?.status === "stored" ? 'bg-green-200' : 'bg-orange-200'}
                             text-gray-700
                           `}
                           title={`第4排-${column}列-2层: ${storageItem?.productId || '空货位'}`}
                           onClick={() => setSelectedSlot({ row: 4, column, level: 2 })}
                         >
                           {storageItem?.productId ? storageItem.productId.slice(-3) : ''}
                         </div>
                       );
                     })}
                     
                     {/* 过道间隔 - 2个div宽度 */}
                     {Array.from({ length: 2 }, (_, index) => (
                       <div key={`aisle-gap-4-2-${index}`} className="w-8 h-8"></div>
                     ))}
                     
                     {/* 第24-34列 */}
                     {Array.from({ length: 11 }, (_, colIndex) => {
                       const column = colIndex + 24;
                       const storageItem = data.find(
                         item => item.row === 4 && item.column === column && item.level === 2
                       );
                       const isSelected = selectedSlot && 
                         selectedSlot.row === 4 && 
                         selectedSlot.column === column && 
                         selectedSlot.level === 2;
                       
                       return (
                         <div
                           key={`4-2-${column}`}
                           className={`
                             w-8 h-8 border border-gray-300 rounded text-xs flex items-center justify-center cursor-pointer
                             ${isSelected ? 'border-yellow-400 border-2 bg-yellow-100' : ''}
                             ${storageItem?.status === "empty" ? 'bg-gray-200' : 
                               storageItem?.status === "stored" ? 'bg-green-200' : 'bg-orange-200'}
                             text-gray-700
                           `}
                           title={`第4排-${column}列-2层: ${storageItem?.productId || '空货位'}`}
                           onClick={() => setSelectedSlot({ row: 4, column, level: 2 })}
                         >
                           {storageItem?.productId ? storageItem.productId.slice(-3) : ''}
                         </div>
                       );
                     })}
                   </div>
                 </div>

                 {/* 第一层 */}
                 <div>
                   <h5 className="text-sm font-medium text-gray-600 mb-2">第一层</h5>
                   <div className="flex gap-1">
                     {/* 第1-21列 */}
                     {Array.from({ length: 21 }, (_, colIndex) => {
                       const column = colIndex + 1;
                       const storageItem = data.find(
                         item => item.row === 4 && item.column === column && item.level === 1
                       );
                       const isSelected = selectedSlot && 
                         selectedSlot.row === 4 && 
                         selectedSlot.column === column && 
                         selectedSlot.level === 1;
                       
                       return (
                         <div
                           key={`4-1-${column}`}
                           className={`
                             w-8 h-8 border border-gray-300 rounded text-xs flex items-center justify-center cursor-pointer
                             ${isSelected ? 'border-yellow-400 border-2 bg-yellow-100' : ''}
                             ${storageItem?.status === "empty" ? 'bg-gray-200' : 
                               storageItem?.status === "stored" ? 'bg-green-200' : 'bg-orange-200'}
                             text-gray-700
                           `}
                           title={`第4排-${column}列-1层: ${storageItem?.productId || '空货位'}`}
                           onClick={() => setSelectedSlot({ row: 4, column, level: 1 })}
                         >
                           {storageItem?.productId ? storageItem.productId.slice(-3) : ''}
                         </div>
                       );
                     })}
                     
                     {/* 过道间隔 - 2个div宽度 */}
                     {Array.from({ length: 2 }, (_, index) => (
                       <div key={`aisle-gap-4-1-${index}`} className="w-8 h-8"></div>
                     ))}
                     
                     {/* 第24-34列 */}
                     {Array.from({ length: 11 }, (_, colIndex) => {
                       const column = colIndex + 24;
                       const storageItem = data.find(
                         item => item.row === 4 && item.column === column && item.level === 1
                       );
                       const isSelected = selectedSlot && 
                         selectedSlot.row === 4 && 
                         selectedSlot.column === column && 
                         selectedSlot.level === 1;
                       
                       return (
                         <div
                           key={`4-1-${column}`}
                           className={`
                             w-8 h-8 border border-gray-300 rounded text-xs flex items-center justify-center cursor-pointer
                             ${isSelected ? 'border-yellow-400 border-2 bg-yellow-100' : ''}
                             ${storageItem?.status === "empty" ? 'bg-gray-200' : 
                               storageItem?.status === "stored" ? 'bg-green-200' : 'bg-orange-200'}
                             text-gray-700
                           `}
                           title={`第4排-${column}列-1层: ${storageItem?.productId || '空货位'}`}
                           onClick={() => setSelectedSlot({ row: 4, column, level: 1 })}
                         >
                           {storageItem?.productId ? storageItem.productId.slice(-3) : ''}
                         </div>
                       );
                     })}
                   </div>
                 </div>
               </div>

               {/* 第五排 */}
               <div className="border border-gray-200 rounded-lg p-4">
                 <h4 className="text-md font-medium text-pink-600 mb-3">第五排 (34列 × 3层)</h4>
                 
                 {/* 第三层 */}
                 <div className="mb-2">
                   <h5 className="text-sm font-medium text-gray-600 mb-2">第三层</h5>
                   <div className="flex gap-1">
                     {/* 第1-21列 */}
                     {Array.from({ length: 21 }, (_, colIndex) => {
                       const column = colIndex + 1;
                       const storageItem = data.find(
                         item => item.row === 5 && item.column === column && item.level === 3
                       );
                       const isSelected = selectedSlot && 
                         selectedSlot.row === 5 && 
                         selectedSlot.column === column && 
                         selectedSlot.level === 3;
                       
                       return (
                         <div
                           key={`5-3-${column}`}
                           className={`
                             w-8 h-8 border border-gray-300 rounded text-xs flex items-center justify-center cursor-pointer
                             ${isSelected ? 'border-yellow-400 border-2 bg-yellow-100' : ''}
                             ${storageItem?.status === "empty" ? 'bg-gray-200' : 
                               storageItem?.status === "stored" ? 'bg-green-200' : 'bg-orange-200'}
                             text-gray-700
                           `}
                           title={`第5排-${column}列-3层: ${storageItem?.productId || '空货位'}`}
                           onClick={() => setSelectedSlot({ row: 5, column, level: 3 })}
                         >
                           {storageItem?.productId ? storageItem.productId.slice(-3) : ''}
                         </div>
                       );
                     })}
                     
                     {/* 过道间隔 - 2个div宽度 */}
                     {Array.from({ length: 2 }, (_, index) => (
                       <div key={`aisle-gap-5-3-${index}`} className="w-8 h-8"></div>
                     ))}
                     
                     {/* 第24-34列 */}
                     {Array.from({ length: 11 }, (_, colIndex) => {
                       const column = colIndex + 24;
                       const storageItem = data.find(
                         item => item.row === 5 && item.column === column && item.level === 3
                       );
                       const isSelected = selectedSlot && 
                         selectedSlot.row === 5 && 
                         selectedSlot.column === column && 
                         selectedSlot.level === 3;
                       
                       return (
                         <div
                           key={`5-3-${column}`}
                           className={`
                             w-8 h-8 border border-gray-300 rounded text-xs flex items-center justify-center cursor-pointer
                             ${isSelected ? 'border-yellow-400 border-2 bg-yellow-100' : ''}
                             ${storageItem?.status === "empty" ? 'bg-gray-200' : 
                               storageItem?.status === "stored" ? 'bg-green-200' : 'bg-orange-200'}
                             text-gray-700
                           `}
                           title={`第5排-${column}列-3层: ${storageItem?.productId || '空货位'}`}
                           onClick={() => setSelectedSlot({ row: 5, column, level: 3 })}
                         >
                           {storageItem?.productId ? storageItem.productId.slice(-3) : ''}
                         </div>
                       );
                     })}
                   </div>
                 </div>

                 {/* 第二层 */}
                 <div className="mb-2">
                   <h5 className="text-sm font-medium text-gray-600 mb-2">第二层</h5>
                   <div className="flex gap-1">
                     {/* 第1-21列 */}
                     {Array.from({ length: 21 }, (_, colIndex) => {
                       const column = colIndex + 1;
                       const storageItem = data.find(
                         item => item.row === 5 && item.column === column && item.level === 2
                       );
                       const isSelected = selectedSlot && 
                         selectedSlot.row === 5 && 
                         selectedSlot.column === column && 
                         selectedSlot.level === 2;
                       
                       return (
                         <div
                           key={`5-2-${column}`}
                           className={`
                             w-8 h-8 border border-gray-300 rounded text-xs flex items-center justify-center cursor-pointer
                             ${isSelected ? 'border-yellow-400 border-2 bg-yellow-100' : ''}
                             ${storageItem?.status === "empty" ? 'bg-gray-200' : 
                               storageItem?.status === "stored" ? 'bg-green-200' : 'bg-orange-200'}
                             text-gray-700
                           `}
                           title={`第5排-${column}列-2层: ${storageItem?.productId || '空货位'}`}
                           onClick={() => setSelectedSlot({ row: 5, column, level: 2 })}
                         >
                           {storageItem?.productId ? storageItem.productId.slice(-3) : ''}
                         </div>
                       );
                     })}
                     
                     {/* 过道间隔 - 2个div宽度 */}
                     {Array.from({ length: 2 }, (_, index) => (
                       <div key={`aisle-gap-5-2-${index}`} className="w-8 h-8"></div>
                     ))}
                     
                     {/* 第24-34列 */}
                     {Array.from({ length: 11 }, (_, colIndex) => {
                       const column = colIndex + 24;
                       const storageItem = data.find(
                         item => item.row === 5 && item.column === column && item.level === 2
                       );
                       const isSelected = selectedSlot && 
                         selectedSlot.row === 5 && 
                         selectedSlot.column === column && 
                         selectedSlot.level === 2;
                       
                       return (
                         <div
                           key={`5-2-${column}`}
                           className={`
                             w-8 h-8 border border-gray-300 rounded text-xs flex items-center justify-center cursor-pointer
                             ${isSelected ? 'border-yellow-400 border-2 bg-yellow-100' : ''}
                             ${storageItem?.status === "empty" ? 'bg-gray-200' : 
                               storageItem?.status === "stored" ? 'bg-green-200' : 'bg-orange-200'}
                             text-gray-700
                           `}
                           title={`第5排-${column}列-2层: ${storageItem?.productId || '空货位'}`}
                           onClick={() => setSelectedSlot({ row: 5, column, level: 2 })}
                         >
                           {storageItem?.productId ? storageItem.productId.slice(-3) : ''}
                         </div>
                       );
                     })}
                   </div>
                 </div>

                 {/* 第一层 */}
                 <div>
                   <h5 className="text-sm font-medium text-gray-600 mb-2">第一层</h5>
                   <div className="flex gap-1">
                     {/* 第1-21列 */}
                     {Array.from({ length: 21 }, (_, colIndex) => {
                       const column = colIndex + 1;
                       const storageItem = data.find(
                         item => item.row === 5 && item.column === column && item.level === 1
                       );
                       const isSelected = selectedSlot && 
                         selectedSlot.row === 5 && 
                         selectedSlot.column === column && 
                         selectedSlot.level === 1;
                       
                       return (
                         <div
                           key={`5-1-${column}`}
                           className={`
                             w-8 h-8 border border-gray-300 rounded text-xs flex items-center justify-center cursor-pointer
                             ${isSelected ? 'border-yellow-400 border-2 bg-yellow-100' : ''}
                             ${storageItem?.status === "empty" ? 'bg-gray-200' : 
                               storageItem?.status === "stored" ? 'bg-green-200' : 'bg-orange-200'}
                             text-gray-700
                           `}
                           title={`第5排-${column}列-1层: ${storageItem?.productId || '空货位'}`}
                           onClick={() => setSelectedSlot({ row: 5, column, level: 1 })}
                         >
                           {storageItem?.productId ? storageItem.productId.slice(-3) : ''}
                         </div>
                       );
                     })}
                     
                     {/* 过道间隔 - 2个div宽度 */}
                     {Array.from({ length: 2 }, (_, index) => (
                       <div key={`aisle-gap-5-1-${index}`} className="w-8 h-8"></div>
                     ))}
                     
                     {/* 第24-34列 */}
                     {Array.from({ length: 11 }, (_, colIndex) => {
                       const column = colIndex + 24;
                       const storageItem = data.find(
                         item => item.row === 5 && item.column === column && item.level === 1
                       );
                       const isSelected = selectedSlot && 
                         selectedSlot.row === 5 && 
                         selectedSlot.column === column && 
                         selectedSlot.level === 1;
                       
                       return (
                         <div
                           key={`5-1-${column}`}
                           className={`
                             w-8 h-8 border border-gray-300 rounded text-xs flex items-center justify-center cursor-pointer
                             ${isSelected ? 'border-yellow-400 border-2 bg-yellow-100' : ''}
                             ${storageItem?.status === "empty" ? 'bg-gray-200' : 
                               storageItem?.status === "stored" ? 'bg-green-200' : 'bg-orange-200'}
                             text-gray-700
                           `}
                           title={`第5排-${column}列-1层: ${storageItem?.productId || '空货位'}`}
                           onClick={() => setSelectedSlot({ row: 5, column, level: 1 })}
                         >
                           {storageItem?.productId ? storageItem.productId.slice(-3) : ''}
                         </div>
                       );
                     })}
                   </div>
                 </div>
               </div>

               {/* 第六排 */}
               <div className="border border-gray-200 rounded-lg p-4">
                 <h4 className="text-md font-medium text-teal-600 mb-3">第六排 (36列 × 3层)</h4>
                 
                 {/* 第三层 */}
                 <div className="mb-2">
                   <h5 className="text-sm font-medium text-gray-600 mb-2">第三层</h5>
                   <div className="grid grid-cols-36 gap-1">
                     {Array.from({ length: 36 }, (_, colIndex) => {
                       const column = colIndex + 1;
                       const storageItem = data.find(
                         item => item.row === 6 && item.column === column && item.level === 3
                       );
                       
                       const isSelected = selectedSlot && 
                         selectedSlot.row === 6 && 
                         selectedSlot.column === column && 
                         selectedSlot.level === 3;
                       
                       return (
                         <div
                           key={`6-3-${column}`}
                           className={`
                             w-8 h-8 border border-gray-300 rounded text-xs flex items-center justify-center cursor-pointer
                             ${isSelected ? 'border-yellow-400 border-2 bg-yellow-100' : ''}
                             ${storageItem?.status === "empty" ? 'bg-gray-200' : 
                               storageItem?.status === "stored" ? 'bg-green-200' : 'bg-orange-200'}
                             text-gray-700
                           `}
                           title={`第6排-${column}列-3层: ${storageItem?.productId || '空货位'}`}
                           onClick={() => setSelectedSlot({ row: 6, column, level: 3 })}
                         >
                           {storageItem?.productId ? storageItem.productId.slice(-3) : ''}
                         </div>
                       );
                     })}
                   </div>
                 </div>

                 {/* 第二层 */}
                 <div className="mb-2">
                   <h5 className="text-sm font-medium text-gray-600 mb-2">第二层</h5>
                   <div className="grid grid-cols-36 gap-1">
                     {Array.from({ length: 36 }, (_, colIndex) => {
                       const column = colIndex + 1;
                       const storageItem = data.find(
                         item => item.row === 6 && item.column === column && item.level === 2
                       );
                       
                       const isSelected = selectedSlot && 
                         selectedSlot.row === 6 && 
                         selectedSlot.column === column && 
                         selectedSlot.level === 2;
                       
                       return (
                         <div
                           key={`6-2-${column}`}
                           className={`
                             w-8 h-8 border border-gray-300 rounded text-xs flex items-center justify-center cursor-pointer
                             ${isSelected ? 'border-yellow-400 border-2 bg-yellow-100' : ''}
                             ${storageItem?.status === "empty" ? 'bg-gray-200' : 
                               storageItem?.status === "stored" ? 'bg-green-200' : 'bg-orange-200'}
                             text-gray-700
                           `}
                           title={`第6排-${column}列-2层: ${storageItem?.productId || '空货位'}`}
                           onClick={() => setSelectedSlot({ row: 6, column, level: 2 })}
                         >
                           {storageItem?.productId ? storageItem.productId.slice(-3) : ''}
                         </div>
                       );
                     })}
                   </div>
                 </div>

                 {/* 第一层 */}
                 <div>
                   <h5 className="text-sm font-medium text-gray-600 mb-2">第一层</h5>
                   <div className="grid grid-cols-36 gap-1">
                     {Array.from({ length: 36 }, (_, colIndex) => {
                       const column = colIndex + 1;
                       const storageItem = data.find(
                         item => item.row === 6 && item.column === column && item.level === 1
                       );
                       
                       const isSelected = selectedSlot && 
                         selectedSlot.row === 6 && 
                         selectedSlot.column === column && 
                         selectedSlot.level === 1;
                       
                       return (
                         <div
                           key={`6-1-${column}`}
                           className={`
                             w-8 h-8 border border-gray-300 rounded text-xs flex items-center justify-center cursor-pointer
                             ${isSelected ? 'border-yellow-400 border-2 bg-yellow-100' : ''}
                             ${storageItem?.status === "empty" ? 'bg-gray-200' : 
                               storageItem?.status === "stored" ? 'bg-green-200' : 'bg-orange-200'}
                             text-gray-700
                           `}
                           title={`第6排-${column}列-1层: ${storageItem?.productId || '空货位'}`}
                           onClick={() => setSelectedSlot({ row: 6, column, level: 1 })}
                         >
                           {storageItem?.productId ? storageItem.productId.slice(-3) : ''}
                         </div>
                       );
                     })}
                   </div>
                 </div>
               </div>
            </div>

            {/* 图例说明 */}
            <div className="mt-6 p-3 bg-gray-50 rounded-lg">
              <h5 className="text-sm font-medium text-gray-700 mb-2">图例说明：</h5>
              <div className="flex flex-wrap gap-4 text-xs">
                <div className="flex items-center space-x-2">
                  <div className="w-6 h-6 bg-gray-200 border border-gray-300 rounded"></div>
                  <span>空货位</span>
                </div>
                <div className="flex items-center space-x-2">
                  <div className="w-6 h-6 bg-green-200 border border-gray-300 rounded"></div>
                  <span>已存储</span>
                </div>
                <div className="flex items-center space-x-2">
                  <div className="w-6 h-6 bg-orange-200 border border-gray-300 rounded"></div>
                  <span>测试中</span>
                </div>

                <div className="flex items-center space-x-2">
                  <div className="w-6 h-6 bg-gray-100 border border-gray-300 rounded"></div>
                  <span>留空区域</span>
                </div>

              </div>
            </div>
          </div>
      </div>
    </div>
  );
}
