import { DashboardCard } from "@/components/DashboardCard";
import { StatusChart } from "@/components/StatusChart";
import { WarehouseVisualization } from "@/components/WarehouseVisualization";
import { Badge } from "@/components/ui/badge";
import { Progress } from "@/components/ui/progress";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import {
  Activity,
  Package,
  Cog,
  AlertTriangle,
  CheckCircle,
  Clock,
  Bot,
  Truck,
  TestTube2,
  Settings,
  Battery,
  Wifi,
  WifiOff,
} from "lucide-react";
import { useState, useEffect, useRef } from "react";

const storageStatusData = [
  { name: "已存储", value: 456 },
  { name: "测试中", value: 24 },
  { name: "空货位", value: 120 },
];

// 设备状态数据
const equipmentData = {
  truss: [
    { id: "桁架-01", name: "桁架系统-01", status: "running", workStatus: "搬运中", fault: false, location: "A区-01" }
  ],
  amr: [
    { id: "AMR-01", name: "AMR小车-01", status: "running", workStatus: "运输中", fault: false, location: "接驳台-01" },
    { id: "AMR-02", name: "AMR小车-02", status: "charging", workStatus: "充电中", fault: false, location: "充电站-01" },
    { id: "AMR-03", name: "AMR小车-03", status: "running", workStatus: "运输中", fault: false, location: "测试台-05" },
    { id: "AMR-04", name: "AMR小车-04", status: "maintenance", workStatus: "维护中", fault: true, location: "维修区" },
    { id: "AMR-05", name: "AMR小车-05", status: "idle", workStatus: "待机", fault: false, location: "待命点-02" },
    { id: "AMR-06", name: "AMR小车-06", status: "running", workStatus: "运输中", fault: false, location: "接驳台-03" },
  ],
  testStations: [
    { id: "测试台-01", name: "测试台-01", status: "running", workStatus: "测试中", fault: false, productId: "P12345" },
    { id: "测试台-02", name: "测试台-02", status: "running", workStatus: "测试中", fault: false, productId: "P12346" },
    { id: "测试台-03", name: "测试台-03", status: "idle", workStatus: "待机", fault: false, productId: null },
    { id: "测试台-04", name: "测试台-04", status: "running", workStatus: "测试中", fault: false, productId: "P12347" },
    { id: "测试台-05", name: "测试台-05", status: "maintenance", workStatus: "维护中", fault: true, productId: null },
    { id: "测试台-06", name: "测试台-06", status: "running", workStatus: "测试中", fault: false, productId: "P12348" },
    { id: "测试台-07", name: "测试台-07", status: "running", workStatus: "测试中", fault: false, productId: "P12349" },
    { id: "测试台-08", name: "测试台-08", status: "idle", workStatus: "待机", fault: false, productId: null },
    { id: "测试台-09", name: "测试台-09", status: "running", workStatus: "测试中", fault: false, productId: "P12350" },
    { id: "测试台-10", name: "测试台-10", status: "running", workStatus: "测试中", fault: false, productId: "P12351" },
    { id: "测试台-11", name: "测试台-11", status: "idle", workStatus: "待机", fault: false, productId: null },
    { id: "测试台-12", name: "测试台-12", status: "running", workStatus: "测试中", fault: false, productId: "P12352" },
  ]
};



// Generate current testing products
const generateCurrentTestingProducts = () => {
  const products = [];
  const testStations = [
    "测试台-01",
    "测试台-03",
    "测试台-05",
    "测试台-07",
    "测试台-02",
    "测试台-04",
    "测试台-06",
    "测试台-01",
  ];

  for (let i = 0; i < 8; i++) {
    const productId = `P${String(Math.floor(Math.random() * 90000) + 10000)}`;
    const testTime = Math.floor(Math.random() * 40) + 1;
    products.push({
      productId,
      testStation: testStations[i],
      testTime,
    });
  }

  return products;
};

const currentTestingProducts = generateCurrentTestingProducts();

// 设备状态监控组件
const EquipmentStatusMonitor = () => {
  const getStatusBadge = (status: string, fault: boolean) => {
    if (fault) {
      return <Badge variant="destructive" className="text-xs">故障</Badge>;
    }
    switch (status) {
      case 'running':
        return <Badge variant="default" className="text-xs bg-green-500">运行中</Badge>;
      case 'idle':
        return <Badge variant="secondary" className="text-xs">待机</Badge>;
      case 'charging':
        return <Badge variant="outline" className="text-xs border-blue-500 text-blue-500">充电中</Badge>;
      case 'maintenance':
        return <Badge variant="outline" className="text-xs border-yellow-500 text-yellow-500">维护中</Badge>;
      default:
        return <Badge variant="outline" className="text-xs">未知</Badge>;
    }
  };

  const getEquipmentIcon = (type: string) => {
    switch (type) {
      case 'truss':
        return <Bot className="h-4 w-4 text-blue-500" />;
      case 'amr':
        return <Truck className="h-4 w-4 text-green-500" />;
      case 'test':
        return <TestTube2 className="h-4 w-4 text-purple-500" />;
      default:
        return <Settings className="h-4 w-4 text-gray-500" />;
    }
  };

  return (
    <Card className="blue-tech-card hover:blue-tech-glow transition-all duration-300">
      <CardHeader>
        <CardTitle className="text-slate-800">设备状态监控</CardTitle>
      </CardHeader>
      <CardContent>
        <div className="space-y-6">
          {/* 桁架系统 */}
          <div>
            <h4 className="text-sm font-medium text-slate-700 mb-3 flex items-center">
              <Bot className="mr-2 h-4 w-4 text-blue-500" />
              桁架系统 (1台)
            </h4>
            <div className="grid grid-cols-2 gap-3">
              {equipmentData.truss.map((equipment) => (
                <div key={equipment.id} className="flex items-center justify-between p-3 bg-slate-50 rounded-lg">
                  <div className="flex items-center space-x-2">
                    <Bot className="h-4 w-4 text-blue-500" />
                    <div>
                      <div className="text-sm font-medium text-slate-700">{equipment.name}</div>
                      <div className="text-xs text-slate-500">{equipment.location}</div>
                    </div>
                  </div>
                  <div className="flex flex-col items-end space-y-1">
                    {getStatusBadge(equipment.status, equipment.fault)}
                    <div className="text-xs text-slate-500">{equipment.workStatus}</div>
                  </div>
                </div>
              ))}
            </div>
          </div>

          {/* AMR小车 */}
          <div>
            <h4 className="text-sm font-medium text-slate-700 mb-3 flex items-center">
              <Truck className="mr-2 h-4 w-4 text-green-500" />
              AMR小车 (6辆)
            </h4>
            <div className="grid grid-cols-2 gap-3">
              {equipmentData.amr.map((equipment) => (
                <div key={equipment.id} className="flex items-center justify-between p-3 bg-slate-50 rounded-lg">
                  <div className="flex items-center space-x-2">
                    <Truck className="h-4 w-4 text-green-500" />
                    <div>
                      <div className="text-sm font-medium text-slate-700">{equipment.name}</div>
                      <div className="text-xs text-slate-500">{equipment.location}</div>
                    </div>
                  </div>
                  <div className="flex flex-col items-end space-y-1">
                    {getStatusBadge(equipment.status, equipment.fault)}
                    <div className="text-xs text-slate-500">{equipment.workStatus}</div>
                  </div>
                </div>
              ))}
            </div>
          </div>

          {/* 测试台 */}
          <div>
            <h4 className="text-sm font-medium text-slate-700 mb-3 flex items-center">
              <TestTube2 className="mr-2 h-4 w-4 text-purple-500" />
              测试台 (12个)
            </h4>
            <div className="grid grid-cols-3 gap-3">
              {equipmentData.testStations.map((equipment) => (
                <div key={equipment.id} className="flex items-center justify-between p-3 bg-slate-50 rounded-lg">
                  <div className="flex items-center space-x-2">
                    <TestTube2 className="h-4 w-4 text-purple-500" />
                    <div>
                      <div className="text-sm font-medium text-slate-700">{equipment.name}</div>
                      <div className="text-xs text-slate-500">
                        {equipment.productId ? `产品: ${equipment.productId}` : '无产品'}
                      </div>
                    </div>
                  </div>
                  <div className="flex flex-col items-end space-y-1">
                    {getStatusBadge(equipment.status, equipment.fault)}
                    <div className="text-xs text-slate-500">{equipment.workStatus}</div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};



export default function Dashboard() {
  return (
    <div className="space-y-6">
      {/* <div className="flex items-center justify-between">
        <h2 className="text-3xl font-bold tracking-tight text-slate-800">管理首页</h2>
        <div className="flex items-center space-x-2">
          <Badge variant="outline" className="text-green-600 border-green-300 bg-green-50">
            <CheckCircle className="mr-1 h-3 w-3" />
            系统正常
          </Badge>
        </div>
      </div> */}
        {/* 3D货架状态展示 */}
        <Card className="blue-tech-card hover:blue-tech-glow transition-all duration-300">
        <CardHeader>
          <CardTitle className="text-slate-800">仓储控制</CardTitle>
        </CardHeader>
        <CardContent>
          <WarehouseVisualization />
        </CardContent>
      </Card>

      {/* Key Metrics */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        <Card className="blue-tech-card hover:blue-tech-glow transition-all duration-300">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium text-slate-700">总惯组数量</CardTitle>
            <Package className="h-4 w-4 text-blue-500" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-slate-900">553</div>
            <p className="text-xs text-slate-500">+12 较上周</p>
          </CardContent>
        </Card>

        <Card className="blue-tech-card hover:blue-tech-glow transition-all duration-300">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium text-slate-700">今日测试</CardTitle>
            <Activity className="h-4 w-4 text-green-500" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-slate-900">24</div>
            <p className="text-xs text-slate-500">+3 较昨日</p>
          </CardContent>
        </Card>

        <Card className="blue-tech-card hover:blue-tech-glow transition-all duration-300">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium text-slate-700">设备状态</CardTitle>
            <Cog className="h-4 w-4 text-yellow-500" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-slate-900">21</div>
            <p className="text-xs text-slate-500">18 正常运行</p>
          </CardContent>
        </Card>

        <Card className="blue-tech-card hover:blue-tech-glow transition-all duration-300">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium text-slate-700">存储利用率</CardTitle>
            <AlertTriangle className="h-4 w-4 text-orange-500" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-slate-900">92%</div>
            <p className="text-xs text-slate-500">+2% 较昨日</p>
          </CardContent>
        </Card>
      </div>

      {/* Current Testing Products */}
      <Card className="blue-tech-card hover:blue-tech-glow transition-all duration-300">
        <CardHeader>
          <CardTitle className="text-slate-800">当前测试产品</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
            {currentTestingProducts.map((product, index) => (
              <div
                key={index}
                className="blue-tech-nav-item p-4 rounded-lg border border-slate-200 hover:border-slate-300 transition-all duration-300"
              >
                <div className="flex items-center justify-between mb-2">
                  <span className="text-sm font-medium text-slate-700">产品ID</span>
                  <Clock className="h-4 w-4 text-blue-500" />
                </div>
                <div className="text-lg font-bold text-slate-900 mb-1">{product.productId}</div>
                <div className="text-sm text-slate-600 mb-2">{product.testStation}</div>
                <div className="flex items-center justify-between">
                  <span className="text-xs text-slate-500">测试时间</span>
                  <span className="text-sm font-medium text-slate-700">{product.testTime}分钟</span>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

    

      {/* 设备状态监控 */}
      <EquipmentStatusMonitor />
    </div>
  );
}
