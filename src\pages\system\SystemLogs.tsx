import { useState } from "react";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import { Tabs, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import {
  Search,
  Filter,
  Download,
  RefreshCw,
  Eye,
  AlertTriangle,
  CheckCircle,
  Info,
  User,
  Clock,
  MapPin,
  Monitor,
  Database,
  Settings,
  Package,
  TestTube2,
  Wrench,
  Calendar,
  FileText,
  Trash2,
  Edit,
  Plus,
  Lock,
  Unlock,
  LogOut,
  LogIn,
} from "lucide-react";

interface SystemLog {
  id: string;
  timestamp: string;
  username: string;
  realName: string;
  action: string;
  module: string;
  description: string;
  ipAddress: string;
  userAgent: string;
  status: "success" | "warning" | "error" | "info";
  details?: string;
  affectedResource?: string;
  oldValue?: string;
  newValue?: string;
}

interface LogStatistics {
  totalLogs: number;
  todayLogs: number;
  errorLogs: number;
  warningLogs: number;
  uniqueUsers: number;
}

export default function SystemLogs() {
  const [selectedModule, setSelectedModule] = useState("all");
  const [selectedStatus, setSelectedStatus] = useState("all");
  const [selectedUser, setSelectedUser] = useState("all");
  const [searchTerm, setSearchTerm] = useState("");
  const [selectedLog, setSelectedLog] = useState<SystemLog | null>(null);
  const [dateRange, setDateRange] = useState("24h");

  // 模拟系统日志数据
  const [logs] = useState<SystemLog[]>([
    {
      id: "LOG-2024-001",
      timestamp: "2024-01-15 15:30:25",
      username: "admin",
      realName: "管理员",
      action: "登录",
      module: "系统认证",
      description: "用户成功登录系统",
      ipAddress: "*************",
      userAgent: "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36",
      status: "success",
      details: "登录成功，会话ID: SESS-2024-001",
    },
    {
      id: "LOG-2024-002",
      timestamp: "2024-01-15 15:32:10",
      username: "admin",
      realName: "管理员",
      action: "创建用户",
      module: "用户管理",
      description: "创建新用户：张三",
      ipAddress: "*************",
      userAgent: "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36",
      status: "success",
      details: "用户ID: U002，角色：测试操作员",
      affectedResource: "用户: 张三",
      newValue: "用户创建成功",
    },
    {
      id: "LOG-2024-003",
      timestamp: "2024-01-15 15:35:42",
      username: "zhangsan",
      realName: "张三",
      action: "登录",
      module: "系统认证",
      description: "用户成功登录系统",
      ipAddress: "*************",
      userAgent: "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36",
      status: "success",
      details: "登录成功，会话ID: SESS-2024-002",
    },
    {
      id: "LOG-2024-004",
      timestamp: "2024-01-15 15:40:15",
      username: "zhangsan",
      realName: "张三",
      action: "修改测试计划",
      module: "测试管理",
      description: "修改测试计划：产品A-型号X1测试",
      ipAddress: "*************",
      userAgent: "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36",
      status: "success",
      details: "测试计划ID: TP-2024-001",
      affectedResource: "测试计划: 产品A-型号X1测试",
      oldValue: "状态: 待开始",
      newValue: "状态: 进行中",
    },
    {
      id: "LOG-2024-005",
      timestamp: "2024-01-15 15:45:30",
      username: "lisi",
      realName: "李四",
      action: "登录失败",
      module: "系统认证",
      description: "用户登录失败：密码错误",
      ipAddress: "*************",
      userAgent: "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36",
      status: "error",
      details: "连续失败次数：3次，账户已锁定",
    },
    {
      id: "LOG-2024-006",
      timestamp: "2024-01-15 15:50:12",
      username: "admin",
      realName: "管理员",
      action: "解锁用户",
      module: "用户管理",
      description: "解锁用户账户：李四",
      ipAddress: "*************",
      userAgent: "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36",
      status: "success",
      details: "用户ID: U003，解锁原因：管理员手动解锁",
      affectedResource: "用户: 李四",
      oldValue: "状态: 锁定",
      newValue: "状态: 正常",
    },
    {
      id: "LOG-2024-007",
      timestamp: "2024-01-15 16:00:25",
      username: "wangwu",
      realName: "王五",
      action: "导出数据",
      module: "数据管理",
      description: "导出测试数据报告",
      ipAddress: "*************",
      userAgent: "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36",
      status: "success",
      details: "导出文件：test_report_20240115.xlsx",
      affectedResource: "数据报告",
    },
    {
      id: "LOG-2024-008",
      timestamp: "2024-01-15 16:15:40",
      username: "admin",
      realName: "管理员",
      action: "系统配置修改",
      module: "系统设置",
      description: "修改系统参数：数据库连接超时时间",
      ipAddress: "*************",
      userAgent: "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36",
      status: "warning",
      details: "参数名：db_timeout，原值：30秒，新值：60秒",
      affectedResource: "系统配置",
      oldValue: "30秒",
      newValue: "60秒",
    },
    {
      id: "LOG-2024-009",
      timestamp: "2024-01-15 16:30:15",
      username: "zhangsan",
      realName: "张三",
      action: "删除记录",
      module: "测试管理",
      description: "删除测试记录：无效数据",
      ipAddress: "*************",
      userAgent: "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36",
      status: "success",
      details: "删除记录ID: TR-2024-005，原因：数据无效",
      affectedResource: "测试记录: TR-2024-005",
    },
    {
      id: "LOG-2024-010",
      timestamp: "2024-01-15 16:45:30",
      username: "admin",
      realName: "管理员",
      action: "备份数据库",
      module: "系统维护",
      description: "执行数据库备份操作",
      ipAddress: "*************",
      userAgent: "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36",
      status: "success",
      details: "备份文件：backup_20240115_164530.sql，大小：256MB",
      affectedResource: "数据库备份",
    },
  ]);

  // 统计数据
  const statistics: LogStatistics = {
    totalLogs: logs.length,
    todayLogs: logs.filter(log => log.timestamp.includes("2024-01-15")).length,
    errorLogs: logs.filter(log => log.status === "error").length,
    warningLogs: logs.filter(log => log.status === "warning").length,
    uniqueUsers: new Set(logs.map(log => log.username)).size,
  };

  const filteredLogs = logs.filter((log) => {
    const moduleMatch = selectedModule === "all" || log.module === selectedModule;
    const statusMatch = selectedStatus === "all" || log.status === selectedStatus;
    const userMatch = selectedUser === "all" || log.username === selectedUser;
    const searchMatch =
      searchTerm === "" ||
      log.username.toLowerCase().includes(searchTerm.toLowerCase()) ||
      log.realName.toLowerCase().includes(searchTerm.toLowerCase()) ||
      log.action.toLowerCase().includes(searchTerm.toLowerCase()) ||
      log.description.toLowerCase().includes(searchTerm.toLowerCase()) ||
      log.ipAddress.includes(searchTerm);
    return moduleMatch && statusMatch && userMatch && searchMatch;
  });

  const getStatusBadge = (status: string) => {
    const statusConfig = {
      success: {
        label: "成功",
        className: "bg-green-100 text-green-800 border-green-200",
        icon: CheckCircle,
      },
      warning: {
        label: "警告",
        className: "bg-yellow-100 text-yellow-800 border-yellow-200",
        icon: AlertTriangle,
      },
      error: {
        label: "错误",
        className: "bg-red-100 text-red-800 border-red-200",
        icon: AlertTriangle,
      },
      info: {
        label: "信息",
        className: "bg-blue-100 text-blue-800 border-blue-200",
        icon: Info,
      },
    };

    const config = statusConfig[status as keyof typeof statusConfig];
    const Icon = config.icon;
    return (
      <Badge className={`${config.className} border flex items-center gap-1`}>
        <Icon className="h-3 w-3" />
        {config.label}
      </Badge>
    );
  };

  const getModuleIcon = (module: string) => {
    const icons = {
      "系统认证": <LogIn className="h-4 w-4 text-blue-600" />,
      "用户管理": <User className="h-4 w-4 text-green-600" />,
      "测试管理": <TestTube2 className="h-4 w-4 text-purple-600" />,
      "数据管理": <Database className="h-4 w-4 text-cyan-600" />,
      "系统设置": <Settings className="h-4 w-4 text-orange-600" />,
      "系统维护": <Wrench className="h-4 w-4 text-gray-600" />,
      "仓储管理": <Package className="h-4 w-4 text-indigo-600" />,
      "设备管理": <Monitor className="h-4 w-4 text-red-600" />,
    };
    return icons[module as keyof typeof icons] || <FileText className="h-4 w-4 text-gray-600" />;
  };

  const getActionIcon = (action: string) => {
    const icons = {
      "登录": <LogIn className="h-4 w-4 text-green-600" />,
      "登录失败": <LogOut className="h-4 w-4 text-red-600" />,
      "创建用户": <Plus className="h-4 w-4 text-blue-600" />,
      "修改用户": <Edit className="h-4 w-4 text-orange-600" />,
      "删除用户": <Trash2 className="h-4 w-4 text-red-600" />,
      "锁定用户": <Lock className="h-4 w-4 text-red-600" />,
      "解锁用户": <Unlock className="h-4 w-4 text-green-600" />,
      "修改测试计划": <Edit className="h-4 w-4 text-blue-600" />,
      "删除记录": <Trash2 className="h-4 w-4 text-red-600" />,
      "导出数据": <Download className="h-4 w-4 text-green-600" />,
      "系统配置修改": <Settings className="h-4 w-4 text-orange-600" />,
      "备份数据库": <Database className="h-4 w-4 text-cyan-600" />,
    };
    return icons[action as keyof typeof icons] || <FileText className="h-4 w-4 text-gray-600" />;
  };

  const uniqueUsers = Array.from(new Set(logs.map(log => log.username)));
  const uniqueModules = Array.from(new Set(logs.map(log => log.module)));

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <div>
          <h2 className="text-3xl font-bold tracking-tight">系统日志</h2>
          <p className="text-muted-foreground">
            查看系统中所有用户的操作日志和系统事件记录
          </p>
        </div>
        <div className="flex items-center space-x-3">
          <Select value={dateRange} onValueChange={setDateRange}>
            <SelectTrigger className="w-32">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="1h">1小时</SelectItem>
              <SelectItem value="24h">24小时</SelectItem>
              <SelectItem value="7d">7天</SelectItem>
              <SelectItem value="30d">30天</SelectItem>
              <SelectItem value="all">全部</SelectItem>
            </SelectContent>
          </Select>

          <Button variant="outline" size="sm">
            <RefreshCw className="mr-2 h-4 w-4" />
            刷新
          </Button>

          <Button variant="outline" size="sm">
            <Download className="mr-2 h-4 w-4" />
            导出
          </Button>
        </div>
      </div>

      {/* Statistics Cards */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-5">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">总日志数</CardTitle>
            <FileText className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{statistics.totalLogs}</div>
            <p className="text-xs text-muted-foreground">系统总日志记录</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">今日日志</CardTitle>
            <Calendar className="h-4 w-4 text-green-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-green-600">
              {statistics.todayLogs}
            </div>
            <p className="text-xs text-muted-foreground">今日新增日志</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">错误日志</CardTitle>
            <AlertTriangle className="h-4 w-4 text-red-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-red-600">
              {statistics.errorLogs}
            </div>
            <p className="text-xs text-muted-foreground">需要关注</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">警告日志</CardTitle>
            <AlertTriangle className="h-4 w-4 text-yellow-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-yellow-600">
              {statistics.warningLogs}
            </div>
            <p className="text-xs text-muted-foreground">需要注意</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">活跃用户</CardTitle>
            <User className="h-4 w-4 text-blue-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-blue-600">
              {statistics.uniqueUsers}
            </div>
            <p className="text-xs text-muted-foreground">今日活跃用户</p>
          </CardContent>
        </Card>
      </div>

      {/* Main Content */}
      <Tabs defaultValue="logs" className="space-y-4">
        <TabsList>
          <TabsTrigger value="logs">操作日志</TabsTrigger>
          <TabsTrigger value="analysis">日志分析</TabsTrigger>
          <TabsTrigger value="alerts">告警设置</TabsTrigger>
        </TabsList>

        {/* Logs Tab */}
        <TabsContent value="logs" className="space-y-4">
          {/* Filters */}
          <Card>
            <CardHeader>
              <CardTitle>筛选条件</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid gap-4 md:grid-cols-5">
                <div className="space-y-2">
                  <label className="text-sm font-medium">搜索关键词</label>
                  <div className="relative">
                    <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                    <Input
                      placeholder="用户名、操作、描述..."
                      value={searchTerm}
                      onChange={(e) => setSearchTerm(e.target.value)}
                      className="pl-10"
                    />
                  </div>
                </div>

                <div className="space-y-2">
                  <label className="text-sm font-medium">功能模块</label>
                  <Select
                    value={selectedModule}
                    onValueChange={setSelectedModule}
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">全部模块</SelectItem>
                      {uniqueModules.map((module) => (
                        <SelectItem key={module} value={module}>
                          {module}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>

                <div className="space-y-2">
                  <label className="text-sm font-medium">状态</label>
                  <Select
                    value={selectedStatus}
                    onValueChange={setSelectedStatus}
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">全部状态</SelectItem>
                      <SelectItem value="success">成功</SelectItem>
                      <SelectItem value="warning">警告</SelectItem>
                      <SelectItem value="error">错误</SelectItem>
                      <SelectItem value="info">信息</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div className="space-y-2">
                  <label className="text-sm font-medium">用户</label>
                  <Select
                    value={selectedUser}
                    onValueChange={setSelectedUser}
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">全部用户</SelectItem>
                      {uniqueUsers.map((user) => (
                        <SelectItem key={user} value={user}>
                          {user}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>

                <div className="flex items-end">
                  <Button className="w-full">
                    <Filter className="mr-2 h-4 w-4" />
                    筛选
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Logs Table */}
          <Card>
            <CardHeader>
              <CardTitle>系统日志列表</CardTitle>
              <CardDescription>
                显示 {filteredLogs.length} 条日志记录
              </CardDescription>
            </CardHeader>
            <CardContent>
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>时间</TableHead>
                    <TableHead>用户</TableHead>
                    <TableHead>操作</TableHead>
                    <TableHead>模块</TableHead>
                    <TableHead>描述</TableHead>
                    <TableHead>IP地址</TableHead>
                    <TableHead>状态</TableHead>
                    <TableHead>操作</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {filteredLogs.map((log) => (
                    <TableRow key={log.id}>
                      <TableCell>
                        <div className="flex items-center space-x-1">
                          <Clock className="h-3 w-3 text-muted-foreground" />
                          <span className="text-sm">{log.timestamp}</span>
                        </div>
                      </TableCell>
                      <TableCell>
                        <div className="flex items-center space-x-2">
                          <User className="h-3 w-3 text-muted-foreground" />
                          <div>
                            <div className="font-medium">{log.realName}</div>
                            <div className="text-xs text-muted-foreground">
                              {log.username}
                            </div>
                          </div>
                        </div>
                      </TableCell>
                      <TableCell>
                        <div className="flex items-center space-x-2">
                          {getActionIcon(log.action)}
                          <span>{log.action}</span>
                        </div>
                      </TableCell>
                      <TableCell>
                        <div className="flex items-center space-x-2">
                          {getModuleIcon(log.module)}
                          <span>{log.module}</span>
                        </div>
                      </TableCell>
                      <TableCell>
                        <div className="max-w-xs truncate" title={log.description}>
                          {log.description}
                        </div>
                      </TableCell>
                      <TableCell>
                        <div className="flex items-center space-x-1">
                          <MapPin className="h-3 w-3 text-muted-foreground" />
                          <span className="text-sm">{log.ipAddress}</span>
                        </div>
                      </TableCell>
                      <TableCell>{getStatusBadge(log.status)}</TableCell>
                      <TableCell>
                        <Dialog>
                          <DialogTrigger asChild>
                            <Button
                              variant="outline"
                              size="sm"
                              onClick={() => setSelectedLog(log)}
                            >
                              <Eye className="h-3 w-3" />
                            </Button>
                          </DialogTrigger>
                          <DialogContent className="max-w-2xl">
                            <DialogHeader>
                              <DialogTitle>日志详情</DialogTitle>
                              <DialogDescription>
                                查看操作日志的详细信息
                              </DialogDescription>
                            </DialogHeader>
                            {selectedLog && (
                              <div className="space-y-4">
                                <div className="grid grid-cols-2 gap-4">
                                  <div>
                                    <label className="text-sm font-medium">日志ID</label>
                                    <p className="text-sm text-muted-foreground">
                                      {selectedLog.id}
                                    </p>
                                  </div>
                                  <div>
                                    <label className="text-sm font-medium">时间戳</label>
                                    <p className="text-sm text-muted-foreground">
                                      {selectedLog.timestamp}
                                    </p>
                                  </div>
                                  <div>
                                    <label className="text-sm font-medium">用户名</label>
                                    <p>{selectedLog.realName} ({selectedLog.username})</p>
                                  </div>
                                  <div>
                                    <label className="text-sm font-medium">操作</label>
                                    <div className="flex items-center space-x-2">
                                      {getActionIcon(selectedLog.action)}
                                      <span>{selectedLog.action}</span>
                                    </div>
                                  </div>
                                  <div>
                                    <label className="text-sm font-medium">功能模块</label>
                                    <div className="flex items-center space-x-2">
                                      {getModuleIcon(selectedLog.module)}
                                      <span>{selectedLog.module}</span>
                                    </div>
                                  </div>
                                  <div>
                                    <label className="text-sm font-medium">状态</label>
                                    <div className="mt-1">{getStatusBadge(selectedLog.status)}</div>
                                  </div>
                                  <div>
                                    <label className="text-sm font-medium">IP地址</label>
                                    <p className="text-sm text-muted-foreground">
                                      {selectedLog.ipAddress}
                                    </p>
                                  </div>
                                  <div>
                                    <label className="text-sm font-medium">用户代理</label>
                                    <p className="text-sm text-muted-foreground">
                                      {selectedLog.userAgent}
                                    </p>
                                  </div>
                                </div>
                                <div>
                                  <label className="text-sm font-medium">操作描述</label>
                                  <p className="mt-1">{selectedLog.description}</p>
                                </div>
                                {selectedLog.details && (
                                  <div>
                                    <label className="text-sm font-medium">详细信息</label>
                                    <p className="mt-1 text-sm text-muted-foreground">
                                      {selectedLog.details}
                                    </p>
                                  </div>
                                )}
                                {selectedLog.affectedResource && (
                                  <div>
                                    <label className="text-sm font-medium">影响资源</label>
                                    <p className="mt-1 text-sm text-muted-foreground">
                                      {selectedLog.affectedResource}
                                    </p>
                                  </div>
                                )}
                                {(selectedLog.oldValue || selectedLog.newValue) && (
                                  <div className="grid grid-cols-2 gap-4">
                                    {selectedLog.oldValue && (
                                      <div>
                                        <label className="text-sm font-medium">原值</label>
                                        <p className="mt-1 text-sm text-muted-foreground">
                                          {selectedLog.oldValue}
                                        </p>
                                      </div>
                                    )}
                                    {selectedLog.newValue && (
                                      <div>
                                        <label className="text-sm font-medium">新值</label>
                                        <p className="mt-1 text-sm text-muted-foreground">
                                          {selectedLog.newValue}
                                        </p>
                                      </div>
                                    )}
                                  </div>
                                )}
                              </div>
                            )}
                          </DialogContent>
                        </Dialog>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Analysis Tab */}
        <TabsContent value="analysis" className="space-y-4">
          <div className="grid gap-4 md:grid-cols-2">
            <Card>
              <CardHeader>
                <CardTitle>操作类型分布</CardTitle>
                <CardDescription>
                  各类操作的数量统计
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  {Object.entries(
                    logs.reduce((acc, log) => {
                      acc[log.action] = (acc[log.action] || 0) + 1;
                      return acc;
                    }, {} as Record<string, number>)
                  ).map(([action, count]) => (
                    <div key={action} className="flex items-center justify-between">
                      <span className="text-sm">{action}</span>
                      <Badge variant="outline">{count}</Badge>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>状态分布</CardTitle>
                <CardDescription>
                  日志状态统计
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  {Object.entries(
                    logs.reduce((acc, log) => {
                      acc[log.status] = (acc[log.status] || 0) + 1;
                      return acc;
                    }, {} as Record<string, number>)
                  ).map(([status, count]) => (
                    <div key={status} className="flex items-center justify-between">
                      <span className="text-sm">{status}</span>
                      <Badge variant="outline">{count}</Badge>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        {/* Alerts Tab */}
        <TabsContent value="alerts" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>告警设置</CardTitle>
              <CardDescription>
                配置系统日志告警规则
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="grid gap-4 md:grid-cols-2">
                  <div className="space-y-2">
                    <label className="text-sm font-medium">错误日志告警</label>
                    <Select defaultValue="enabled">
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="enabled">启用</SelectItem>
                        <SelectItem value="disabled">禁用</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                  <div className="space-y-2">
                    <label className="text-sm font-medium">登录失败告警</label>
                    <Select defaultValue="enabled">
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="enabled">启用</SelectItem>
                        <SelectItem value="disabled">禁用</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </div>
                <div className="space-y-2">
                  <label className="text-sm font-medium">告警邮箱</label>
                  <Input placeholder="<EMAIL>" />
                </div>
                <Button>保存设置</Button>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
} 