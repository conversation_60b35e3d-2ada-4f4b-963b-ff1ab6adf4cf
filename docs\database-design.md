# 惯组产品智能化存储及月稳测试系统 - 数据库设计文档

## 1. 数据库概述

### 1.1 数据库类型
- **主数据库**: MySQL 8.0+
- **缓存数据库**: Redis 6.0+
- **时序数据库**: InfluxDB 2.0+ (用于设备监控数据)

### 1.2 命名规范
- 表名: 小写字母，下划线分隔，如 `user_info`
- 字段名: 小写字母，下划线分隔，如 `user_name`
- 主键: 统一使用 `id`，类型为 `BIGINT UNSIGNED AUTO_INCREMENT`
- 外键: 格式为 `表名_id`，如 `user_id`
- 时间字段: 统一使用 `created_at`, `updated_at`, `deleted_at`

## 2. 核心数据表设计

### 2.1 用户权限管理

#### 2.1.1 用户表 (users)
```sql
CREATE TABLE users (
    id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    username VARCHAR(50) NOT NULL UNIQUE COMMENT '用户名',
    password VARCHAR(255) NOT NULL COMMENT '密码(加密)',
    real_name VARCHAR(100) NOT NULL COMMENT '真实姓名',
    email VARCHAR(100) COMMENT '邮箱',
    phone VARCHAR(20) COMMENT '手机号',
    department_id BIGINT UNSIGNED COMMENT '部门ID',
    role_id BIGINT UNSIGNED NOT NULL COMMENT '角色ID',
    status ENUM('active', 'inactive', 'locked') DEFAULT 'active' COMMENT '状态',
    last_login_at TIMESTAMP NULL COMMENT '最后登录时间',
    last_login_ip VARCHAR(45) COMMENT '最后登录IP',
    login_attempts INT DEFAULT 0 COMMENT '登录尝试次数',
    locked_at TIMESTAMP NULL COMMENT '锁定时间',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    deleted_at TIMESTAMP NULL,
    INDEX idx_username (username),
    INDEX idx_email (email),
    INDEX idx_department (department_id),
    INDEX idx_role (role_id),
    INDEX idx_status (status)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户表';
```

#### 2.1.2 角色表 (roles)
```sql
CREATE TABLE roles (
    id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(50) NOT NULL UNIQUE COMMENT '角色名称',
    description TEXT COMMENT '角色描述',
    permissions JSON COMMENT '权限配置(JSON格式)',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    deleted_at TIMESTAMP NULL,
    INDEX idx_name (name)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='角色表';
```

#### 2.1.3 部门表 (departments)
```sql
CREATE TABLE departments (
    id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(100) NOT NULL COMMENT '部门名称',
    parent_id BIGINT UNSIGNED NULL COMMENT '父部门ID',
    level INT DEFAULT 1 COMMENT '部门层级',
    sort_order INT DEFAULT 0 COMMENT '排序',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    deleted_at TIMESTAMP NULL,
    INDEX idx_parent (parent_id),
    INDEX idx_level (level)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='部门表';
```

### 2.2 产品管理

#### 2.2.1 产品表 (products)
```sql
CREATE TABLE products (
    id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    product_code VARCHAR(50) NOT NULL UNIQUE COMMENT '产品编码',
    product_name VARCHAR(200) NOT NULL COMMENT '产品名称',
    product_type VARCHAR(50) NOT NULL COMMENT '产品类型',
    specification TEXT COMMENT '产品规格',
    manufacturer VARCHAR(100) COMMENT '制造商',
    model_number VARCHAR(100) COMMENT '型号',
    batch_number VARCHAR(50) COMMENT '批次号',
    production_date DATE COMMENT '生产日期',
    expiry_date DATE COMMENT '有效期',
    status ENUM('new', 'testing', 'tested', 'qualified', 'unqualified', 'scrapped') DEFAULT 'new' COMMENT '产品状态',
    current_location_id BIGINT UNSIGNED COMMENT '当前位置ID',
    test_count INT DEFAULT 0 COMMENT '测试次数',
    last_test_at TIMESTAMP NULL COMMENT '最后测试时间',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    deleted_at TIMESTAMP NULL,
    INDEX idx_product_code (product_code),
    INDEX idx_product_type (product_type),
    INDEX idx_status (status),
    INDEX idx_location (current_location_id),
    INDEX idx_batch (batch_number)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='产品表';
```

#### 2.2.2 库位表 (storage_locations)
```sql
CREATE TABLE storage_locations (
    id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    location_code VARCHAR(50) NOT NULL UNIQUE COMMENT '库位编码',
    location_name VARCHAR(100) NOT NULL COMMENT '库位名称',
    location_type ENUM('shelf', 'conveyor', 'test_station', 'charging_station') NOT NULL COMMENT '库位类型',
    area_id BIGINT UNSIGNED COMMENT '区域ID',
    row_number INT COMMENT '行号',
    column_number INT COMMENT '列号',
    level_number INT COMMENT '层号',
    capacity INT DEFAULT 1 COMMENT '容量',
    occupied_count INT DEFAULT 0 COMMENT '已占用数量',
    status ENUM('available', 'occupied', 'maintenance', 'disabled') DEFAULT 'available' COMMENT '状态',
    light_control_id BIGINT UNSIGNED COMMENT '三色灯控制ID',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    deleted_at TIMESTAMP NULL,
    INDEX idx_location_code (location_code),
    INDEX idx_location_type (location_type),
    INDEX idx_area (area_id),
    INDEX idx_status (status)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='库位表';
```

### 2.3 设备管理

#### 2.3.1 设备表 (equipment)
```sql
CREATE TABLE equipment (
    id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    equipment_code VARCHAR(50) NOT NULL UNIQUE COMMENT '设备编码',
    equipment_name VARCHAR(100) NOT NULL COMMENT '设备名称',
    equipment_type ENUM('truss', 'agv', 'test_station', 'charging_station', 'light_control') NOT NULL COMMENT '设备类型',
    model VARCHAR(100) COMMENT '设备型号',
    manufacturer VARCHAR(100) COMMENT '制造商',
    ip_address VARCHAR(45) COMMENT 'IP地址',
    port INT COMMENT '端口号',
    status ENUM('online', 'offline', 'maintenance', 'fault', 'disabled') DEFAULT 'offline' COMMENT '设备状态',
    location_id BIGINT UNSIGNED COMMENT '当前位置ID',
    parameters JSON COMMENT '设备参数(JSON格式)',
    last_heartbeat_at TIMESTAMP NULL COMMENT '最后心跳时间',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    deleted_at TIMESTAMP NULL,
    INDEX idx_equipment_code (equipment_code),
    INDEX idx_equipment_type (equipment_type),
    INDEX idx_status (status),
    INDEX idx_location (location_id),
    INDEX idx_ip (ip_address)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='设备表';
```

#### 2.3.2 AGV设备表 (agv_equipment)
```sql
CREATE TABLE agv_equipment (
    id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    equipment_id BIGINT UNSIGNED NOT NULL COMMENT '设备ID',
    battery_level DECIMAL(5,2) DEFAULT 100.00 COMMENT '电池电量(%)',
    battery_voltage DECIMAL(8,2) COMMENT '电池电压(V)',
    battery_current DECIMAL(8,2) COMMENT '电池电流(A)',
    charging_status ENUM('idle', 'charging', 'full', 'error') DEFAULT 'idle' COMMENT '充电状态',
    current_task_id BIGINT UNSIGNED COMMENT '当前任务ID',
    speed DECIMAL(8,2) COMMENT '当前速度(m/s)',
    position_x DECIMAL(10,3) COMMENT 'X坐标',
    position_y DECIMAL(10,3) COMMENT 'Y坐标',
    orientation DECIMAL(8,2) COMMENT '方向角(度)',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (equipment_id) REFERENCES equipment(id),
    INDEX idx_equipment (equipment_id),
    INDEX idx_battery (battery_level),
    INDEX idx_charging (charging_status),
    INDEX idx_task (current_task_id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='AGV设备表';
```

#### 2.3.3 三色灯控制表 (light_controls)
```sql
CREATE TABLE light_controls (
    id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    control_code VARCHAR(50) NOT NULL UNIQUE COMMENT '控制板编码',
    control_name VARCHAR(100) NOT NULL COMMENT '控制板名称',
    ip_address VARCHAR(45) NOT NULL COMMENT 'IP地址',
    port INT DEFAULT 8080 COMMENT '端口号',
    total_lights INT DEFAULT 50 COMMENT '总灯数',
    status ENUM('online', 'offline', 'error') DEFAULT 'offline' COMMENT '状态',
    last_heartbeat_at TIMESTAMP NULL COMMENT '最后心跳时间',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    deleted_at TIMESTAMP NULL,
    INDEX idx_control_code (control_code),
    INDEX idx_ip (ip_address),
    INDEX idx_status (status)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='三色灯控制表';
```

#### 2.3.4 三色灯映射表 (light_mappings)
```sql
CREATE TABLE light_mappings (
    id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    light_control_id BIGINT UNSIGNED NOT NULL COMMENT '控制板ID',
    light_number INT NOT NULL COMMENT '灯编号',
    location_id BIGINT UNSIGNED NOT NULL COMMENT '库位ID',
    color ENUM('red', 'yellow', 'green', 'off') DEFAULT 'off' COMMENT '当前颜色',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (light_control_id) REFERENCES light_controls(id),
    FOREIGN KEY (location_id) REFERENCES storage_locations(id),
    UNIQUE KEY uk_light_location (light_control_id, light_number),
    INDEX idx_control (light_control_id),
    INDEX idx_location (location_id),
    INDEX idx_color (color)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='三色灯映射表';
```

### 2.4 测试管理

#### 2.4.1 测试任务表 (test_tasks)
```sql
CREATE TABLE test_tasks (
    id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    task_code VARCHAR(50) NOT NULL UNIQUE COMMENT '任务编码',
    task_type ENUM('outbound_test', 'inbound_storage', 'vehicle_charging') NOT NULL COMMENT '任务类型',
    product_id BIGINT UNSIGNED COMMENT '产品ID',
    vehicle_id BIGINT UNSIGNED COMMENT 'AGV设备ID',
    priority INT DEFAULT 0 COMMENT '优先级(数字越大优先级越高)',
    status ENUM('pending', 'running', 'paused', 'completed', 'failed', 'cancelled') DEFAULT 'pending' COMMENT '任务状态',
    current_step INT DEFAULT 1 COMMENT '当前步骤',
    total_steps INT NOT NULL COMMENT '总步骤数',
    progress DECIMAL(5,2) DEFAULT 0.00 COMMENT '进度(%)',
    estimated_duration INT COMMENT '预计耗时(秒)',
    actual_duration INT COMMENT '实际耗时(秒)',
    start_time TIMESTAMP NULL COMMENT '开始时间',
    end_time TIMESTAMP NULL COMMENT '结束时间',
    created_by BIGINT UNSIGNED NOT NULL COMMENT '创建人ID',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    deleted_at TIMESTAMP NULL,
    FOREIGN KEY (product_id) REFERENCES products(id),
    FOREIGN KEY (vehicle_id) REFERENCES agv_equipment(id),
    FOREIGN KEY (created_by) REFERENCES users(id),
    INDEX idx_task_code (task_code),
    INDEX idx_task_type (task_type),
    INDEX idx_status (status),
    INDEX idx_product (product_id),
    INDEX idx_vehicle (vehicle_id),
    INDEX idx_priority (priority),
    INDEX idx_created_by (created_by)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='测试任务表';
```

#### 2.4.2 测试步骤表 (test_steps)
```sql
CREATE TABLE test_steps (
    id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    task_id BIGINT UNSIGNED NOT NULL COMMENT '任务ID',
    step_number INT NOT NULL COMMENT '步骤编号',
    step_name VARCHAR(200) NOT NULL COMMENT '步骤名称',
    step_description TEXT COMMENT '步骤描述',
    status ENUM('pending', 'running', 'completed', 'failed', 'skipped') DEFAULT 'pending' COMMENT '步骤状态',
    start_time TIMESTAMP NULL COMMENT '开始时间',
    end_time TIMESTAMP NULL COMMENT '结束时间',
    duration INT COMMENT '执行时长(秒)',
    result_data JSON COMMENT '执行结果数据(JSON格式)',
    error_message TEXT COMMENT '错误信息',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (task_id) REFERENCES test_tasks(id),
    INDEX idx_task (task_id),
    INDEX idx_step_number (step_number),
    INDEX idx_status (status),
    UNIQUE KEY uk_task_step (task_id, step_number)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='测试步骤表';
```

#### 2.4.3 测试计划表 (test_plans)
```sql
CREATE TABLE test_plans (
    id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    plan_code VARCHAR(50) NOT NULL UNIQUE COMMENT '计划编码',
    plan_name VARCHAR(200) NOT NULL COMMENT '计划名称',
    plan_type ENUM('daily', 'weekly', 'monthly', 'custom') NOT NULL COMMENT '计划类型',
    start_date DATE NOT NULL COMMENT '开始日期',
    end_date DATE NOT NULL COMMENT '结束日期',
    status ENUM('draft', 'approved', 'running', 'completed', 'cancelled') DEFAULT 'draft' COMMENT '计划状态',
    total_products INT DEFAULT 0 COMMENT '总产品数量',
    completed_products INT DEFAULT 0 COMMENT '已完成数量',
    created_by BIGINT UNSIGNED NOT NULL COMMENT '创建人ID',
    approved_by BIGINT UNSIGNED COMMENT '审批人ID',
    approved_at TIMESTAMP NULL COMMENT '审批时间',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    deleted_at TIMESTAMP NULL,
    FOREIGN KEY (created_by) REFERENCES users(id),
    FOREIGN KEY (approved_by) REFERENCES users(id),
    INDEX idx_plan_code (plan_code),
    INDEX idx_plan_type (plan_type),
    INDEX idx_status (status),
    INDEX idx_date_range (start_date, end_date),
    INDEX idx_created_by (created_by)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='测试计划表';
```

#### 2.4.4 测试计划明细表 (test_plan_details)
```sql
CREATE TABLE test_plan_details (
    id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    plan_id BIGINT UNSIGNED NOT NULL COMMENT '计划ID',
    product_type VARCHAR(50) NOT NULL COMMENT '产品类型',
    quantity INT NOT NULL COMMENT '数量',
    priority INT DEFAULT 0 COMMENT '优先级',
    scheduled_date DATE COMMENT '计划日期',
    status ENUM('pending', 'in_progress', 'completed', 'cancelled') DEFAULT 'pending' COMMENT '状态',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (plan_id) REFERENCES test_plans(id),
    INDEX idx_plan (plan_id),
    INDEX idx_product_type (product_type),
    INDEX idx_status (status),
    INDEX idx_scheduled_date (scheduled_date)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='测试计划明细表';
```

### 2.5 出入库管理

#### 2.5.1 出入库记录表 (storage_records)
```sql
CREATE TABLE storage_records (
    id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    record_code VARCHAR(50) NOT NULL UNIQUE COMMENT '记录编码',
    record_type ENUM('in', 'out') NOT NULL COMMENT '记录类型(入库/出库)',
    product_id BIGINT UNSIGNED NOT NULL COMMENT '产品ID',
    from_location_id BIGINT UNSIGNED COMMENT '来源库位ID',
    to_location_id BIGINT UNSIGNED COMMENT '目标库位ID',
    reason ENUM('new_product', 'test_complete', 'test_start', 'maintenance', 'transfer', 'scrap') NOT NULL COMMENT '原因',
    quantity INT DEFAULT 1 COMMENT '数量',
    status ENUM('pending', 'processing', 'completed', 'failed', 'cancelled') DEFAULT 'pending' COMMENT '状态',
    task_id BIGINT UNSIGNED COMMENT '关联任务ID',
    operator_id BIGINT UNSIGNED NOT NULL COMMENT '操作员ID',
    operation_time TIMESTAMP NULL COMMENT '操作时间',
    notes TEXT COMMENT '备注',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (product_id) REFERENCES products(id),
    FOREIGN KEY (from_location_id) REFERENCES storage_locations(id),
    FOREIGN KEY (to_location_id) REFERENCES storage_locations(id),
    FOREIGN KEY (task_id) REFERENCES test_tasks(id),
    FOREIGN KEY (operator_id) REFERENCES users(id),
    INDEX idx_record_code (record_code),
    INDEX idx_record_type (record_type),
    INDEX idx_product (product_id),
    INDEX idx_status (status),
    INDEX idx_task (task_id),
    INDEX idx_operator (operator_id),
    INDEX idx_operation_time (operation_time)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='出入库记录表';
```

### 2.6 系统配置

#### 2.6.1 系统配置表 (system_configs)
```sql
CREATE TABLE system_configs (
    id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    config_key VARCHAR(100) NOT NULL UNIQUE COMMENT '配置键',
    config_value TEXT COMMENT '配置值',
    config_type ENUM('string', 'number', 'boolean', 'json') DEFAULT 'string' COMMENT '配置类型',
    description TEXT COMMENT '配置描述',
    category VARCHAR(50) DEFAULT 'general' COMMENT '配置分类',
    is_system BOOLEAN DEFAULT FALSE COMMENT '是否系统配置',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_config_key (config_key),
    INDEX idx_category (category)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='系统配置表';
```

#### 2.6.2 操作日志表 (operation_logs)
```sql
CREATE TABLE operation_logs (
    id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    user_id BIGINT UNSIGNED COMMENT '用户ID',
    module VARCHAR(50) NOT NULL COMMENT '模块名称',
    action VARCHAR(50) NOT NULL COMMENT '操作类型',
    resource_type VARCHAR(50) COMMENT '资源类型',
    resource_id BIGINT UNSIGNED COMMENT '资源ID',
    description TEXT COMMENT '操作描述',
    ip_address VARCHAR(45) COMMENT 'IP地址',
    user_agent TEXT COMMENT '用户代理',
    request_data JSON COMMENT '请求数据',
    response_data JSON COMMENT '响应数据',
    status ENUM('success', 'failed') DEFAULT 'success' COMMENT '操作状态',
    error_message TEXT COMMENT '错误信息',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    INDEX idx_user (user_id),
    INDEX idx_module (module),
    INDEX idx_action (action),
    INDEX idx_resource (resource_type, resource_id),
    INDEX idx_created_at (created_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='操作日志表';
```

## 3. 时序数据表设计

### 3.1 设备监控数据 (InfluxDB)

#### 3.1.1 设备心跳数据
```sql
-- InfluxDB Measurement: equipment_heartbeat
-- Tags: equipment_id, equipment_type, status
-- Fields: battery_level, temperature, cpu_usage, memory_usage
-- Timestamp: 自动生成
```

#### 3.1.2 AGV位置数据
```sql
-- InfluxDB Measurement: agv_position
-- Tags: equipment_id, task_id
-- Fields: position_x, position_y, orientation, speed, battery_level
-- Timestamp: 自动生成
```

#### 3.1.3 测试数据
```sql
-- InfluxDB Measurement: test_data
-- Tags: task_id, product_id, test_type
-- Fields: test_value, expected_value, tolerance, result
-- Timestamp: 自动生成
```

## 4. 索引优化建议

### 4.1 复合索引
```sql
-- 用户查询优化
CREATE INDEX idx_users_department_status ON users(department_id, status);

-- 产品查询优化
CREATE INDEX idx_products_type_status_location ON products(product_type, status, current_location_id);

-- 任务查询优化
CREATE INDEX idx_tasks_type_status_priority ON test_tasks(task_type, status, priority);

-- 设备查询优化
CREATE INDEX idx_equipment_type_status_location ON equipment(equipment_type, status, location_id);
```

### 4.2 分区表建议
```sql
-- 操作日志表按月分区
ALTER TABLE operation_logs PARTITION BY RANGE (YEAR(created_at) * 100 + MONTH(created_at)) (
    PARTITION p202401 VALUES LESS THAN (202402),
    PARTITION p202402 VALUES LESS THAN (202403),
    -- ... 更多分区
);
```

## 5. 数据字典

### 5.1 枚举值定义

#### 5.1.1 用户状态 (user_status)
- `active`: 正常
- `inactive`: 停用
- `locked`: 锁定

#### 5.1.2 产品状态 (product_status)
- `new`: 新品
- `testing`: 测试中
- `tested`: 已测试
- `qualified`: 合格
- `unqualified`: 不合格
- `scrapped`: 报废

#### 5.1.3 任务状态 (task_status)
- `pending`: 等待中
- `running`: 执行中
- `paused`: 已暂停
- `completed`: 已完成
- `failed`: 执行失败
- `cancelled`: 已取消

#### 5.1.4 设备状态 (equipment_status)
- `online`: 在线
- `offline`: 离线
- `maintenance`: 维护中
- `fault`: 故障
- `disabled`: 禁用

## 6. 数据备份策略

### 6.1 备份计划
- **全量备份**: 每日凌晨2点
- **增量备份**: 每小时
- **日志备份**: 实时

### 6.2 备份保留
- **全量备份**: 保留30天
- **增量备份**: 保留7天
- **日志备份**: 保留90天

## 7. 性能优化建议

### 7.1 查询优化
- 使用适当的索引
- 避免SELECT *
- 使用LIMIT限制结果集
- 合理使用JOIN

### 7.2 表结构优化
- 选择合适的数据类型
- 使用NOT NULL约束
- 合理设置字段长度
- 使用ENUM替代字符串

### 7.3 配置优化
- 调整MySQL配置参数
- 使用连接池
- 启用查询缓存
- 定期分析表统计信息 