import { useState } from "react";
import { DashboardCard } from "@/components/DashboardCard";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Badge } from "@/components/ui/badge";
import { Ta<PERSON>, <PERSON><PERSON>Content, TabsList, TabsTrigger } from "@/components/ui/tabs";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import { Textarea } from "@/components/ui/textarea";
import { Progress } from "@/components/ui/progress";
import {
  Upload,
  Download,
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON>,
  <PERSON>Circle,
  <PERSON>ert<PERSON><PERSON>gle,
  Play,
  Pause,
  RotateCcw,
} from "lucide-react";

interface Operation {
  id: string;
  type: "inbound" | "outbound";
  productId: string;
  fromLocation?: string;
  toLocation: string;
  status: "pending" | "in_progress" | "completed" | "failed";
  priority: "high" | "medium" | "low";
  requestTime: string;
  startTime?: string;
  completionTime?: string;
  operator: string;
  progress: number;
  notes?: string;
}

const mockOperations: Operation[] = [
  {
    id: "OP001",
    type: "inbound",
    productId: "A-2024-015",
    toLocation: "A区-01-15",
    status: "in_progress",
    priority: "high",
    requestTime: "2024-01-15 14:30",
    startTime: "2024-01-15 14:35",
    operator: "张三",
    progress: 65,
    notes: "新产品入库",
  },
  {
    id: "OP002",
    type: "outbound",
    productId: "A-2024-008",
    fromLocation: "B区-01-08",
    toLocation: "接驳台-01",
    status: "pending",
    priority: "medium",
    requestTime: "2024-01-15 15:00",
    operator: "系统自动",
    progress: 0,
    notes: "出库到接驳台",
  },
  {
    id: "OP003",
    type: "inbound",
    productId: "A-2024-012",
    fromLocation: "接驳台-02",
    toLocation: "A区-01-12",
    status: "completed",
    priority: "low",
    requestTime: "2024-01-15 13:20",
    startTime: "2024-01-15 13:25",
    completionTime: "2024-01-15 13:45",
    operator: "李四",
    progress: 100,
    notes: "从接驳台入库",
  },
  {
    id: "OP004",
    type: "outbound",
    productId: "A-2024-020",
    fromLocation: "A区-01-05",
    toLocation: "出货区-01",
    status: "pending",
    priority: "high",
    requestTime: "2024-01-15 15:30",
    operator: "王五",
    progress: 0,
    notes: "最终出库",
  },
  {
    id: "OP005",
    type: "inbound",
    productId: "A-2024-025",
    toLocation: "B区-01-01",
    status: "pending",
    priority: "medium",
    requestTime: "2024-01-15 15:45",
    operator: "系统自动",
    progress: 0,
    notes: "新产品入库",
  },
  {
    id: "OP006",
    type: "outbound",
    productId: "A-2024-018",
    fromLocation: "A区-01-02",
    toLocation: "接驳台-02",
    status: "completed",
    priority: "low",
    requestTime: "2024-01-15 14:00",
    startTime: "2024-01-15 14:05",
    completionTime: "2024-01-15 14:20",
    operator: "赵六",
    progress: 100,
    notes: "出库到接驳台",
  },
  {
    id: "OP007",
    type: "inbound",
    productId: "A-2024-030",
    fromLocation: "接驳台-01",
    toLocation: "A区-01-04",
    status: "failed",
    priority: "high",
    requestTime: "2024-01-15 16:00",
    startTime: "2024-01-15 16:05",
    operator: "钱七",
    progress: 30,
    notes: "入库失败，需要重新操作",
  },
];

const availableLocations = [
  { id: "A区-01-01", name: "A区-01-01", type: "storage", status: "empty" },
  {
    id: "A区-01-02",
    name: "A区-01-02",
    type: "storage",
    status: "occupied",
  },
  { id: "A区-01-03", name: "A区-01-03", type: "storage", status: "empty" },
  { id: "A区-01-04", name: "A区-01-04", type: "storage", status: "empty" },
  { id: "A区-01-05", name: "A区-01-05", type: "storage", status: "occupied" },
  { id: "B区-01-01", name: "B区-01-01", type: "storage", status: "empty" },
  { id: "B区-01-02", name: "B区-01-02", type: "storage", status: "occupied" },
  { id: "B区-01-03", name: "B区-01-03", type: "storage", status: "empty" },
  { id: "B区-02-01", name: "B区-02-01", type: "storage", status: "empty" },
  { id: "B区-02-02", name: "B区-02-02", type: "storage", status: "empty" },
  { id: "C区-01-01", name: "C区-01-01", type: "storage", status: "empty" },
  { id: "C区-01-02", name: "C区-01-02", type: "storage", status: "empty" },
  { id: "C区-02-01", name: "C区-02-01", type: "storage", status: "empty" },
  { id: "C区-02-02", name: "C区-02-02", type: "storage", status: "empty" },
  { id: "bridge-01", name: "接驳台-01", type: "bridge", status: "empty" },
  { id: "bridge-02", name: "接驳台-02", type: "bridge", status: "occupied" },
  { id: "bridge-03", name: "接驳台-03", type: "bridge", status: "occupied" },
  { id: "shipping-01", name: "出货区-01", type: "shipping", status: "empty" }
];

export default function InOutOperations() {
  const [operations, setOperations] = useState(mockOperations);
  const [selectedOperation, setSelectedOperation] = useState<Operation | null>(
    null,
  );

  const getStatusBadge = (status: string) => {
    const statusConfig = {
      pending: { label: "待执行", variant: "secondary" as const, icon: Clock },
      in_progress: { label: "执行中", variant: "default" as const, icon: Play },
      completed: {
        label: "已完成",
        variant: "default" as const,
        icon: CheckCircle,
      },
      failed: {
        label: "失败",
        variant: "destructive" as const,
        icon: AlertTriangle,
      },
    };

    const config = statusConfig[status as keyof typeof statusConfig];
    const Icon = config.icon;
    return (
      <Badge variant={config.variant} className="flex items-center gap-1">
        <Icon className="h-3 w-3" />
        {config.label}
      </Badge>
    );
  };

  const getTypeBadge = (type: string) => {
    return type === "inbound" ? (
      <Badge className="bg-green-100 text-green-800 flex items-center gap-1">
        <Upload className="h-3 w-3" />
        入库
      </Badge>
    ) : (
      <Badge className="bg-blue-100 text-blue-800 flex items-center gap-1">
        <Download className="h-3 w-3" />
        出库
      </Badge>
    );
  };

  const getPriorityBadge = (priority: string) => {
    const priorityConfig = {
      high: { label: "高", variant: "destructive" as const },
      medium: { label: "中", variant: "default" as const },
      low: { label: "低", variant: "secondary" as const },
    };

    const config = priorityConfig[priority as keyof typeof priorityConfig];
    return <Badge variant={config.variant}>{config.label}</Badge>;
  };

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h2 className="text-3xl font-bold tracking-tight">出入库操作</h2>
        <div className="flex items-center space-x-2">
          <Button variant="outline">
            <Scan className="mr-2 h-4 w-4" />
            扫码操作
          </Button>
          <Dialog>
            <DialogTrigger asChild>
              <Button>新建操作</Button>
            </DialogTrigger>
            <DialogContent className="max-w-2xl">
              <DialogHeader>
                <DialogTitle>创建出入库操作</DialogTitle>
                <DialogDescription>
                  手动创建产品出入库操作任务
                </DialogDescription>
              </DialogHeader>
              <div className="grid gap-4 py-4">
                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="operationType">操作类型</Label>
                    <Select>
                      <SelectTrigger>
                        <SelectValue placeholder="选择操作类型" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="inbound">入库</SelectItem>
                        <SelectItem value="outbound">出库</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="productIdInput">产品编号</Label>
                    <Input id="productIdInput" placeholder="A-2024-XXX" />
                  </div>
                </div>
                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="fromLocationInput">源位置</Label>
                    <Select>
                      <SelectTrigger>
                        <SelectValue placeholder="选择源位置" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="manual">手动输入</SelectItem>
                        {availableLocations.map((location) => (
                          <SelectItem key={location.id} value={location.id}>
                            {location.name}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="toLocationInput">目标位置</Label>
                    <Select>
                      <SelectTrigger>
                        <SelectValue placeholder="选择目标位置" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="auto">自动分配</SelectItem>
                        {availableLocations
                          .filter((l) => l.status === "empty")
                          .map((location) => (
                            <SelectItem key={location.id} value={location.id}>
                              {location.name}
                            </SelectItem>
                          ))}
                      </SelectContent>
                    </Select>
                  </div>
                </div>
                <div className="space-y-2">
                  <Label htmlFor="priority">优先级</Label>
                  <Select>
                    <SelectTrigger>
                      <SelectValue placeholder="选择优先级" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="high">高</SelectItem>
                      <SelectItem value="medium">中</SelectItem>
                      <SelectItem value="low">低</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <div className="space-y-2">
                  <Label htmlFor="operationNotes">备注</Label>
                  <Textarea id="operationNotes" placeholder="操作备注..." />
                </div>
              </div>
              <div className="flex justify-end space-x-2">
                <Button variant="outline">取消</Button>
                <Button>创建操作</Button>
              </div>
            </DialogContent>
          </Dialog>
        </div>
      </div>

      <Tabs defaultValue="operations" className="space-y-4">
        <TabsList>
          <TabsTrigger value="operations">操作队列</TabsTrigger>
          <TabsTrigger value="manual">手动操作</TabsTrigger>
          <TabsTrigger value="locations">货位管理</TabsTrigger>
          <TabsTrigger value="monitor">实时监控</TabsTrigger>
        </TabsList>

        <TabsContent value="operations">
          <div className="space-y-6">
            {/* Operation Queue */}
            <Card>
              <CardHeader>
                <CardTitle>操作队列</CardTitle>
              </CardHeader>
              <CardContent>
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>操作编号</TableHead>
                      <TableHead>类型</TableHead>
                      <TableHead>产品编号</TableHead>
                      <TableHead>源位置</TableHead>
                      <TableHead>目标位置</TableHead>
                      <TableHead>优先级</TableHead>
                      <TableHead>状态</TableHead>
                      <TableHead>进度</TableHead>
                      <TableHead>请求时间</TableHead>
                      <TableHead>操作</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {operations.map((operation) => (
                      <TableRow key={operation.id}>
                        <TableCell className="font-medium">
                          {operation.id}
                        </TableCell>
                        <TableCell>{getTypeBadge(operation.type)}</TableCell>
                        <TableCell>{operation.productId}</TableCell>
                        <TableCell>{operation.fromLocation || "-"}</TableCell>
                        <TableCell>{operation.toLocation}</TableCell>
                        <TableCell>
                          {getPriorityBadge(operation.priority)}
                        </TableCell>
                        <TableCell>
                          {getStatusBadge(operation.status)}
                        </TableCell>
                        <TableCell>
                          <div className="flex items-center space-x-2">
                            <Progress
                              value={operation.progress}
                              className="w-16 h-2"
                            />
                            <span className="text-sm">
                              {operation.progress}%
                            </span>
                          </div>
                        </TableCell>
                        <TableCell>{operation.requestTime}</TableCell>
                        <TableCell>
                          <div className="flex space-x-2">
                            {operation.status === "in_progress" && (
                              <Button size="sm" variant="outline">
                                <Pause className="h-3 w-3" />
                              </Button>
                            )}
                            {operation.status === "pending" && (
                              <Button size="sm" variant="outline">
                                <Play className="h-3 w-3" />
                              </Button>
                            )}
                            <Button
                              size="sm"
                              variant="outline"
                              onClick={() => setSelectedOperation(operation)}
                            >
                              详情
                            </Button>
                          </div>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </CardContent>
            </Card>

            {/* Queue Statistics */}
            <div className="grid gap-4 md:grid-cols-4">
              <Card>
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium">待执行</CardTitle>
                  <Clock className="h-4 w-4 text-muted-foreground" />
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">5</div>
                  <p className="text-xs text-muted-foreground">
                    平均等待 15分钟
                  </p>
                </CardContent>
              </Card>

              <Card>
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium">执行中</CardTitle>
                  <Play className="h-4 w-4 text-muted-foreground" />
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">2</div>
                  <p className="text-xs text-muted-foreground">
                    桁架系统运行中
                  </p>
                </CardContent>
              </Card>

              <Card>
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium">
                    今日完成
                  </CardTitle>
                  <CheckCircle className="h-4 w-4 text-muted-foreground" />
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">28</div>
                  <p className="text-xs text-muted-foreground">+5 较昨日</p>
                </CardContent>
              </Card>

              <Card>
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium">
                    平均时长
                  </CardTitle>
                  <Clock className="h-4 w-4 text-muted-foreground" />
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">8.5分</div>
                  <p className="text-xs text-muted-foreground">-1.2分 较上周</p>
                </CardContent>
              </Card>
            </div>
          </div>
        </TabsContent>

        <TabsContent value="manual">
          <div className="grid gap-6 md:grid-cols-2">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <Upload className="mr-2 h-4 w-4" />
                  手动入库操作
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="manualInProduct">产品编号</Label>
                  <div className="flex space-x-2">
                    <Input
                      id="manualInProduct"
                      placeholder="扫描或输入产品编号"
                    />
                    <Button size="icon" variant="outline">
                      <Scan className="h-4 w-4" />
                    </Button>
                  </div>
                </div>
                <div className="space-y-2">
                  <Label htmlFor="manualInLocation">目标货位</Label>
                  <Select>
                    <SelectTrigger>
                      <SelectValue placeholder="选择或自动分配货位" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="auto">自动分配最优货位</SelectItem>
                      {availableLocations
                        .filter(
                          (l) => l.type === "storage" && l.status === "empty",
                        )
                        .map((location) => (
                          <SelectItem key={location.id} value={location.id}>
                            {location.name}
                          </SelectItem>
                        ))}
                    </SelectContent>
                  </Select>
                </div>
                <div className="space-y-2">
                  <Label htmlFor="manualInNotes">操作备注</Label>
                  <Textarea id="manualInNotes" placeholder="备注信息..." />
                </div>
                <Button className="w-full">执行入库操作</Button>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <Download className="mr-2 h-4 w-4" />
                  手动出库操作
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="manualOutProduct">产品编号</Label>
                  <div className="flex space-x-2">
                    <Input
                      id="manualOutProduct"
                      placeholder="扫描或输入产品编号"
                    />
                    <Button size="icon" variant="outline">
                      <Scan className="h-4 w-4" />
                    </Button>
                  </div>
                </div>
                <div className="space-y-2">
                  <Label htmlFor="manualOutDestination">目标位置</Label>
                  <Select>
                    <SelectTrigger>
                      <SelectValue placeholder="选择目标位置" />
                    </SelectTrigger>
                                        <SelectContent>
                      <SelectItem value="shipping">出货区</SelectItem>
                      <SelectItem value="maintenance">维护区</SelectItem>
                      <SelectItem value="transfer">中转区</SelectItem>
                      {availableLocations
                        .filter((l) => l.type !== "storage" && l.type !== "test")
                        .map((location) => (
                          <SelectItem key={location.id} value={location.id}>
                            {location.name}
                          </SelectItem>
                        ))}
                    </SelectContent>
                  </Select>
                </div>
                <div className="space-y-2">
                  <Label htmlFor="manualOutReason">出库原因</Label>
                  <Select>
                    <SelectTrigger>
                      <SelectValue placeholder="选择出库原因" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="shipping">最终出库</SelectItem>
                      <SelectItem value="maintenance">维护检查</SelectItem>
                      <SelectItem value="transfer">位置转移</SelectItem>
                      <SelectItem value="inspection">质量检查</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <Button className="w-full" variant="outline">
                  执行出库操作
                </Button>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="locations">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <MapPin className="mr-2 h-4 w-4" />
                货位管理
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="grid gap-4 md:grid-cols-3">
                  <Card className="p-4">
                    <h4 className="font-semibold mb-2">货架区域</h4>
                    <div className="space-y-2">
                      <div className="flex justify-between text-sm">
                        <span>总货位:</span>
                        <span>600</span>
                      </div>
                      <div className="flex justify-between text-sm">
                        <span>已占用:</span>
                        <span className="text-blue-600">553</span>
                      </div>
                      <div className="flex justify-between text-sm">
                        <span>空货位:</span>
                        <span className="text-green-600">47</span>
                      </div>
                      <Progress value={76} className="h-2" />
                    </div>
                  </Card>

                  <Card className="p-4">
                    <h4 className="font-semibold mb-2">接驳区域</h4>
                    <div className="space-y-2">
                      <div className="flex justify-between text-sm">
                        <span>接驳台:</span>
                        <span>3</span>
                      </div>
                      <div className="flex justify-between text-sm">
                        <span>使用中:</span>
                        <span className="text-blue-600">1</span>
                      </div>
                      <div className="flex justify-between text-sm">
                        <span>空闲:</span>
                        <span className="text-green-600">2</span>
                      </div>
                      <Progress value={25} className="h-2" />
                    </div>
                  </Card>

                  <Card className="p-4">
                    <h4 className="font-semibold mb-2">出货区域</h4>
                    <div className="space-y-2">
                      <div className="flex justify-between text-sm">
                        <span>出货区:</span>
                        <span>1</span>
                      </div>
                      <div className="flex justify-between text-sm">
                        <span>使用中:</span>
                        <span className="text-blue-600">0</span>
                      </div>
                      <div className="flex justify-between text-sm">
                        <span>空闲:</span>
                        <span className="text-green-600">1</span>
                      </div>
                      <Progress value={0} className="h-2" />
                    </div>
                  </Card>
                </div>

                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>位置编号</TableHead>
                      <TableHead>位置名称</TableHead>
                      <TableHead>区域类型</TableHead>
                      <TableHead>状态</TableHead>
                      <TableHead>当前产品</TableHead>
                      <TableHead>最后更新</TableHead>
                      <TableHead>操作</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {availableLocations.map((location) => (
                      <TableRow key={location.id}>
                        <TableCell className="font-medium">
                          {location.id}
                        </TableCell>
                        <TableCell>{location.name}</TableCell>
                        <TableCell>
                          <Badge variant="outline">
                            {location.type === "storage"
                              ? "存储"
                              : location.type === "bridge"
                                ? "接驳"
                              : location.type === "shipping"
                                ? "出货"
                              : "其他"}
                          </Badge>
                        </TableCell>
                        <TableCell>
                          <Badge
                            variant={
                              location.status === "empty"
                                ? "secondary"
                                : "default"
                            }
                          >
                            {location.status === "empty" ? "空闲" : "占用"}
                          </Badge>
                        </TableCell>
                        <TableCell>
                          {location.status === "occupied" ? "A-2024-XXX" : "-"}
                        </TableCell>
                        <TableCell>2分钟前</TableCell>
                        <TableCell>
                          <Button size="sm" variant="outline">
                            查看详情
                          </Button>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="monitor">
          <div className="space-y-6">
            <div className="grid gap-6 md:grid-cols-2">
              <Card>
                <CardHeader>
                  <CardTitle>桁架系统状态</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    <div className="flex items-center justify-between">
                      <span className="text-sm font-medium">系统状态</span>
                      <Badge className="bg-green-100 text-green-800">
                        正常运行
                      </Badge>
                    </div>
                    <div className="flex items-center justify-between">
                      <span className="text-sm font-medium">当前任务</span>
                      <span className="text-sm">OP001 - 入库操作</span>
                    </div>
                    <div className="flex items-center justify-between">
                      <span className="text-sm font-medium">执行进度</span>
                      <div className="flex items-center space-x-2">
                        <Progress value={65} className="w-20 h-2" />
                        <span className="text-sm">65%</span>
                      </div>
                    </div>
                    <div className="flex items-center justify-between">
                      <span className="text-sm font-medium">预计完成</span>
                      <span className="text-sm">3分钟后</span>
                    </div>
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle>AMR调度状态</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-3">
                    {[1, 2, 3, 4, 5, 6].map((amrId) => (
                                              <div
                          key={amrId}
                        className="flex items-center justify-between"
                      >
                        <span className="text-sm">
                          AMR-{String(amrId).padStart(2, "0")}
                        </span>
                        <div className="flex items-center space-x-2">
                          <Badge
                            variant={
                              amrId === 2
                                ? "destructive"
                                : amrId <= 3
                                  ? "default"
                                  : "secondary"
                            }
                          >
                            {amrId === 2
                              ? "维护中"
                              : amrId <= 3
                                ? "运行中"
                                : "待机"}
                          </Badge>
                          {amrId <= 3 && amrId !== 2 && (
                            <span className="text-xs text-gray-500">
                              搬运中
                            </span>
                          )}
                        </div>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            </div>

            <Card>
              <CardHeader>
                <CardTitle>实时操作日志</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-2 max-h-64 overflow-y-auto">
                  <div className="text-xs space-y-1">
                    <div className="flex items-center space-x-2">
                      <span className="text-gray-500">15:30:25</span>
                      <Badge variant="secondary">桁架</Badge>
                      <span>OP001 执行中 接驳台-01 移动到货位 货架1-A-15</span>
                    </div>
                    <div className="flex items-center space-x-2">
                      <span className="text-gray-500">15:30:15</span>
                                              <Badge variant="default">AMR</Badge>
                        <span>AMR-01 到达接驳台-01</span>
                    </div>
                    <div className="flex items-center space-x-2">
                      <span className="text-gray-500">15:30:10</span>
                      <Badge variant="secondary">桁架</Badge>
                      <span>OP001 开始执行 - 产品 A-2024-015</span>
                    </div>
                    <div className="flex items-center space-x-2">
                      <span className="text-gray-500">15:29:45</span>
                      <Badge variant="default">系统</Badge>
                      <span>OP002 加入队列 - 出库操作</span>
                    </div>
                    <div className="flex items-center space-x-2">
                      <span className="text-gray-500">15:29:20</span>
                                              <Badge variant="default">AMR</Badge>
                        <span>AMR-03 完成搬运任务</span>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>
      </Tabs>

      {/* Operation Detail Dialog */}
      {selectedOperation && (
        <Dialog
          open={!!selectedOperation}
          onOpenChange={() => setSelectedOperation(null)}
        >
          <DialogContent>
            <DialogHeader>
              <DialogTitle>操作详情 - {selectedOperation.id}</DialogTitle>
            </DialogHeader>
            <div className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label className="text-sm font-medium">操作类型</Label>
                  <div>{getTypeBadge(selectedOperation.type)}</div>
                </div>
                <div>
                  <Label className="text-sm font-medium">产品编号</Label>
                  <div>{selectedOperation.productId}</div>
                </div>
              </div>
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label className="text-sm font-medium">源位置</Label>
                  <div>{selectedOperation.fromLocation || "-"}</div>
                </div>
                <div>
                  <Label className="text-sm font-medium">目标位置</Label>
                  <div>{selectedOperation.toLocation}</div>
                </div>
              </div>
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label className="text-sm font-medium">优先级</Label>
                  <div>{getPriorityBadge(selectedOperation.priority)}</div>
                </div>
                <div>
                  <Label className="text-sm font-medium">状态</Label>
                  <div>{getStatusBadge(selectedOperation.status)}</div>
                </div>
              </div>
              <div>
                <Label className="text-sm font-medium">执行进度</Label>
                <div className="flex items-center space-x-2 mt-1">
                  <Progress
                    value={selectedOperation.progress}
                    className="flex-1"
                  />
                  <span className="text-sm">{selectedOperation.progress}%</span>
                </div>
              </div>
              {selectedOperation.notes && (
                <div>
                  <Label className="text-sm font-medium">备注</Label>
                  <div className="text-sm text-gray-600">
                    {selectedOperation.notes}
                  </div>
                </div>
              )}
            </div>
          </DialogContent>
        </Dialog>
      )}
    </div>
  );
}
