import { useState, useEffect } from "react";
import {
  <PERSON>,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, Ta<PERSON>Trigger } from "@/components/ui/tabs";
import { StatusChart } from "@/components/StatusChart";
import { Progress } from "@/components/ui/progress";
import {
  Activity,
  Search,
  Clock,
  RefreshCw,
  Download,
  AlertTriangle,
  CheckCircle,
  TrendingUp,
  TrendingDown,
  Zap,
  Network,
  Database,
  Server,
  FileText,
  Filter,
  Eye,
  ArrowRight,
  Timer,
  Users,
  Globe,
} from "lucide-react";

interface TraceSpan {
  id: string;
  traceId: string;
  parentId?: string;
  name: string;
  service: string;
  startTime: number;
  endTime: number;
  duration: number;
  status: "success" | "error" | "timeout";
  tags: Record<string, string>;
  logs: {
    timestamp: number;
    level: "info" | "warn" | "error";
    message: string;
  }[];
}

interface Trace {
  traceId: string;
  rootSpan: TraceSpan;
  spans: TraceSpan[];
  startTime: number;
  endTime: number;
  duration: number;
  status: "success" | "error" | "timeout";
  serviceCount: number;
  spanCount: number;
  userId?: string;
  requestPath: string;
  method: string;
  userAgent: string;
  ipAddress: string;
}

interface TraceStatistics {
  totalTraces: number;
  successRate: number;
  averageDuration: number;
  errorCount: number;
  timeoutCount: number;
  topServices: {
    service: string;
    count: number;
    avgDuration: number;
    errorRate: number;
  }[];
  topEndpoints: {
    path: string;
    method: string;
    count: number;
    avgDuration: number;
    errorRate: number;
  }[];
}

export default function RequestTracing() {
  const [traces, setTraces] = useState<Trace[]>([]);
  const [selectedTrace, setSelectedTrace] = useState<Trace | null>(null);
  const [statistics, setStatistics] = useState<TraceStatistics | null>(null);
  const [searchTerm, setSearchTerm] = useState("");
  const [filterStatus, setFilterStatus] = useState<string>("all");
  const [filterService, setFilterService] = useState<string>("all");
  const [isAutoRefresh, setIsAutoRefresh] = useState(true);

  // 模拟追踪数据
  useEffect(() => {
    const generateTrace = (): Trace => {
      const traceId = `trace-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
      const startTime = Date.now() - Math.random() * 60000;
      const duration = 100 + Math.random() * 1000;
      const status = Math.random() > 0.9 ? "error" : Math.random() > 0.95 ? "timeout" : "success";
      
      const services = ["wms-api", "wms-db", "wms-cache", "wms-auth", "wms-file"];
      const endpoints = [
        { path: "/api/warehouse/inventory", method: "GET" },
        { path: "/api/warehouse/orders", method: "POST" },
        { path: "/api/users/profile", method: "GET" },
        { path: "/api/equipment/status", method: "PUT" },
        { path: "/api/reports/analytics", method: "GET" },
      ];
      
      const selectedEndpoint = endpoints[Math.floor(Math.random() * endpoints.length)];
      
      const spans: TraceSpan[] = [];
      let currentTime = startTime;
      
      // 生成根span
      const rootSpan: TraceSpan = {
        id: `${traceId}-root`,
        traceId,
        name: `${selectedEndpoint.method} ${selectedEndpoint.path}`,
        service: "wms-api",
        startTime: currentTime,
        endTime: currentTime + duration,
        duration,
        status,
        tags: {
          "http.method": selectedEndpoint.method,
          "http.path": selectedEndpoint.path,
          "http.status_code": status === "success" ? "200" : status === "error" ? "500" : "408",
        },
        logs: [],
      };
      spans.push(rootSpan);
      
      // 生成子span
      const childServices = services.filter(s => s !== "wms-api").slice(0, 2 + Math.floor(Math.random() * 3));
      childServices.forEach((service, index) => {
        const spanDuration = 20 + Math.random() * 200;
        const spanStart = currentTime + 10 + Math.random() * (duration - spanDuration - 20);
        const span: TraceSpan = {
          id: `${traceId}-${index + 1}`,
          traceId,
          parentId: rootSpan.id,
          name: `${service} operation`,
          service,
          startTime: spanStart,
          endTime: spanStart + spanDuration,
          duration: spanDuration,
          status: Math.random() > 0.95 ? "error" : "success",
          tags: {
            "db.type": service.includes("db") ? "mysql" : service.includes("cache") ? "redis" : "http",
            "operation": service.includes("db") ? "query" : service.includes("cache") ? "get" : "request",
          },
          logs: [],
        };
        spans.push(span);
      });
      
      return {
        traceId,
        rootSpan,
        spans,
        startTime,
        endTime: startTime + duration,
        duration,
        status,
        serviceCount: new Set(spans.map(s => s.service)).size,
        spanCount: spans.length,
        userId: Math.random() > 0.3 ? `user-${Math.floor(Math.random() * 1000)}` : undefined,
        requestPath: selectedEndpoint.path,
        method: selectedEndpoint.method,
        userAgent: "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36",
        ipAddress: `192.168.1.${Math.floor(Math.random() * 255)}`,
      };
    };

    const updateTraces = () => {
      const newTraces = Array.from({ length: 20 }, () => generateTrace());
      setTraces(prev => [...newTraces, ...prev.slice(0, 80)]); // 保留最近100条
      
      // 更新统计信息
      const allTraces = [...newTraces, ...traces.slice(0, 80)];
      const successCount = allTraces.filter(t => t.status === "success").length;
      const errorCount = allTraces.filter(t => t.status === "error").length;
      const timeoutCount = allTraces.filter(t => t.status === "timeout").length;
      
      const serviceStats = allTraces.reduce((acc, trace) => {
        trace.spans.forEach(span => {
          if (!acc[span.service]) {
            acc[span.service] = { count: 0, totalDuration: 0, errors: 0 };
          }
          acc[span.service].count++;
          acc[span.service].totalDuration += span.duration;
          if (span.status === "error") acc[span.service].errors++;
        });
        return acc;
      }, {} as Record<string, { count: number; totalDuration: number; errors: number }>);
      
      const endpointStats = allTraces.reduce((acc, trace) => {
        const key = `${trace.method} ${trace.requestPath}`;
        if (!acc[key]) {
          acc[key] = { count: 0, totalDuration: 0, errors: 0, method: trace.method, path: trace.requestPath };
        }
        acc[key].count++;
        acc[key].totalDuration += trace.duration;
        if (trace.status === "error") acc[key].errors++;
        return acc;
      }, {} as Record<string, { count: number; totalDuration: number; errors: number; method: string; path: string }>);
      
      setStatistics({
        totalTraces: allTraces.length,
        successRate: (successCount / allTraces.length) * 100,
        averageDuration: allTraces.reduce((sum, t) => sum + t.duration, 0) / allTraces.length,
        errorCount,
        timeoutCount,
        topServices: Object.entries(serviceStats)
          .map(([service, stats]) => ({
            service,
            count: stats.count,
            avgDuration: stats.totalDuration / stats.count,
            errorRate: (stats.errors / stats.count) * 100,
          }))
          .sort((a, b) => b.count - a.count)
          .slice(0, 5),
        topEndpoints: Object.values(endpointStats)
          .map(stats => ({
            path: stats.path,
            method: stats.method,
            count: stats.count,
            avgDuration: stats.totalDuration / stats.count,
            errorRate: (stats.errors / stats.count) * 100,
          }))
          .sort((a, b) => b.count - a.count)
          .slice(0, 5),
      });
    };

    updateTraces();
    const interval = setInterval(updateTraces, 10000);

    return () => clearInterval(interval);
  }, [traces]);

  const getStatusBadge = (status: string) => {
    const statusConfig = {
      success: { label: "成功", className: "bg-green-100 text-green-800 border-green-200" },
      error: { label: "错误", className: "bg-red-100 text-red-800 border-red-200" },
      timeout: { label: "超时", className: "bg-yellow-100 text-yellow-800 border-yellow-200" },
    };
    const config = statusConfig[status as keyof typeof statusConfig];
    return <Badge className={`${config.className} border`}>{config.label}</Badge>;
  };

  const getServiceIcon = (service: string) => {
    if (service.includes("api")) return <Server className="h-4 w-4" />;
    if (service.includes("db")) return <Database className="h-4 w-4" />;
    if (service.includes("cache")) return <Zap className="h-4 w-4" />;
    if (service.includes("auth")) return <Users className="h-4 w-4" />;
    if (service.includes("file")) return <FileText className="h-4 w-4" />;
    return <Network className="h-4 w-4" />;
  };

  const filteredTraces = traces.filter(trace => {
    const matchesSearch = trace.traceId.includes(searchTerm) || 
                         trace.requestPath.includes(searchTerm) ||
                         trace.userId?.includes(searchTerm);
    const matchesStatus = filterStatus === "all" || trace.status === filterStatus;
    const matchesService = filterService === "all" || 
                          trace.spans.some(span => span.service === filterService);
    return matchesSearch && matchesStatus && matchesService;
  });

  if (!statistics) return <div>加载中...</div>;

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <div>
          <h2 className="text-3xl font-bold tracking-tight">请求追踪</h2>
          <p className="text-muted-foreground">
            分布式追踪系统，监控请求链路、性能分析和错误诊断
          </p>
        </div>
        <div className="flex items-center space-x-3">
          <Button
            variant="outline"
            size="sm"
            onClick={() => setIsAutoRefresh(!isAutoRefresh)}
          >
            <RefreshCw className={`mr-2 h-4 w-4 ${isAutoRefresh ? 'animate-spin' : ''}`} />
            {isAutoRefresh ? '自动刷新' : '手动刷新'}
          </Button>
          <Button variant="outline" size="sm">
            <Download className="mr-2 h-4 w-4" />
            导出报告
          </Button>
        </div>
      </div>

      {/* 关键指标卡片 */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">总追踪数</CardTitle>
            <Activity className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-blue-600">
              {statistics.totalTraces.toLocaleString()}
            </div>
            <div className="flex items-center justify-between mt-2">
              <Progress value={Math.min(statistics.totalTraces / 1000 * 100, 100)} className="flex-1 mr-2" />
              <Badge className="bg-blue-100 text-blue-800 border-blue-200">活跃</Badge>
            </div>
            <p className="text-xs text-muted-foreground mt-2">
              最近追踪: {traces.length > 0 ? traces[0].traceId.slice(-8) : "N/A"}
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">成功率</CardTitle>
            <CheckCircle className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className={`text-2xl font-bold ${statistics.successRate > 95 ? 'text-green-600' : statistics.successRate > 90 ? 'text-yellow-600' : 'text-red-600'}`}>
              {statistics.successRate.toFixed(1)}%
            </div>
            <div className="flex items-center justify-between mt-2">
              <Progress value={statistics.successRate} className="flex-1 mr-2" />
              <Badge className={statistics.successRate > 95 ? 'bg-green-100 text-green-800 border-green-200' : statistics.successRate > 90 ? 'bg-yellow-100 text-yellow-800 border-yellow-200' : 'bg-red-100 text-red-800 border-red-200'}>
                {statistics.successRate > 95 ? '优秀' : statistics.successRate > 90 ? '良好' : '需优化'}
              </Badge>
            </div>
            <p className="text-xs text-muted-foreground mt-2">
              错误: {statistics.errorCount} / 超时: {statistics.timeoutCount}
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">平均响应时间</CardTitle>
            <Timer className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className={`text-2xl font-bold ${statistics.averageDuration < 200 ? 'text-green-600' : statistics.averageDuration < 500 ? 'text-yellow-600' : 'text-red-600'}`}>
              {statistics.averageDuration.toFixed(0)}ms
            </div>
            <div className="flex items-center justify-between mt-2">
              <Progress value={Math.min(statistics.averageDuration / 1000 * 100, 100)} className="flex-1 mr-2" />
              <Badge className={statistics.averageDuration < 200 ? 'bg-green-100 text-green-800 border-green-200' : statistics.averageDuration < 500 ? 'bg-yellow-100 text-yellow-800 border-yellow-200' : 'bg-red-100 text-red-800 border-red-200'}>
                {statistics.averageDuration < 200 ? '快速' : statistics.averageDuration < 500 ? '正常' : '较慢'}
              </Badge>
            </div>
            <p className="text-xs text-muted-foreground mt-2">
              最快: {Math.min(...traces.map(t => t.duration)).toFixed(0)}ms
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">活跃服务</CardTitle>
            <Server className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-purple-600">
              {statistics.topServices.length}
            </div>
            <div className="flex items-center justify-between mt-2">
              <Progress value={Math.min(statistics.topServices.length / 10 * 100, 100)} className="flex-1 mr-2" />
              <Badge className="bg-purple-100 text-purple-800 border-purple-200">运行中</Badge>
            </div>
            <p className="text-xs text-muted-foreground mt-2">
              主要: {statistics.topServices[0]?.service || "N/A"}
            </p>
          </CardContent>
        </Card>
      </div>

      {/* 主要内容区域 */}
      <Tabs defaultValue="traces" className="space-y-4">
        <TabsList>
          <TabsTrigger value="traces">追踪列表</TabsTrigger>
          <TabsTrigger value="statistics">统计分析</TabsTrigger>
          <TabsTrigger value="services">服务监控</TabsTrigger>
          <TabsTrigger value="endpoints">端点分析</TabsTrigger>
        </TabsList>

        {/* 追踪列表 */}
        <TabsContent value="traces" className="space-y-4">
          {/* 搜索和过滤 */}
          <Card>
            <CardHeader>
              <CardTitle>搜索和过滤</CardTitle>
              <CardDescription>根据条件筛选追踪记录</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="flex items-center space-x-4">
                <div className="flex-1">
                  <div className="relative">
                    <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                    <input
                      type="text"
                      placeholder="搜索追踪ID、路径或用户ID..."
                      value={searchTerm}
                      onChange={(e) => setSearchTerm(e.target.value)}
                      className="w-full pl-10 pr-4 py-2 border rounded-md"
                    />
                  </div>
                </div>
                <select
                  value={filterStatus}
                  onChange={(e) => setFilterStatus(e.target.value)}
                  className="px-3 py-2 border rounded-md"
                >
                  <option value="all">所有状态</option>
                  <option value="success">成功</option>
                  <option value="error">错误</option>
                  <option value="timeout">超时</option>
                </select>
                <select
                  value={filterService}
                  onChange={(e) => setFilterService(e.target.value)}
                  className="px-3 py-2 border rounded-md"
                >
                  <option value="all">所有服务</option>
                  <option value="wms-api">WMS API</option>
                  <option value="wms-db">WMS Database</option>
                  <option value="wms-cache">WMS Cache</option>
                  <option value="wms-auth">WMS Auth</option>
                  <option value="wms-file">WMS File</option>
                </select>
              </div>
            </CardContent>
          </Card>

          {/* 追踪列表 */}
          <Card>
            <CardHeader>
              <CardTitle>追踪记录</CardTitle>
              <CardDescription>最近的请求追踪记录</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {filteredTraces.slice(0, 20).map((trace) => (
                  <div
                    key={trace.traceId}
                    className="flex items-center justify-between p-4 border rounded-lg hover:bg-gray-50 cursor-pointer"
                    onClick={() => setSelectedTrace(trace)}
                  >
                    <div className="flex items-center space-x-4">
                      <div>
                        <p className="font-medium">{trace.method} {trace.requestPath}</p>
                        <p className="text-sm text-muted-foreground">
                          {trace.traceId} • {new Date(trace.startTime).toLocaleString()}
                        </p>
                      </div>
                      {getStatusBadge(trace.status)}
                    </div>
                    <div className="text-right">
                      <div className="grid grid-cols-3 gap-4 text-sm">
                        <div>
                          <p className="font-medium">{trace.duration}ms</p>
                          <p className="text-xs text-muted-foreground">响应时间</p>
                        </div>
                        <div>
                          <p className="font-medium">{trace.serviceCount}</p>
                          <p className="text-xs text-muted-foreground">服务数</p>
                        </div>
                        <div>
                          <p className="font-medium">{trace.spanCount}</p>
                          <p className="text-xs text-muted-foreground">Span数</p>
                        </div>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>

          {/* 追踪详情 */}
          {selectedTrace && (
            <Card>
              <CardHeader>
                <CardTitle>追踪详情 - {selectedTrace.traceId}</CardTitle>
                <CardDescription>详细的请求链路信息</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-6">
                  {/* 基本信息 */}
                  <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                    <div>
                      <label className="text-sm font-medium">状态</label>
                      <p className="text-lg">{getStatusBadge(selectedTrace.status)}</p>
                    </div>
                    <div>
                      <label className="text-sm font-medium">响应时间</label>
                      <p className="text-lg font-semibold">{selectedTrace.duration}ms</p>
                    </div>
                    <div>
                      <label className="text-sm font-medium">服务数量</label>
                      <p className="text-lg font-semibold">{selectedTrace.serviceCount}</p>
                    </div>
                    <div>
                      <label className="text-sm font-medium">Span数量</label>
                      <p className="text-lg font-semibold">{selectedTrace.spanCount}</p>
                    </div>
                  </div>

                  {/* 请求信息 */}
                  <div className="space-y-3">
                    <h4 className="font-medium">请求信息</h4>
                    <div className="grid grid-cols-2 gap-4 text-sm">
                      <div>
                        <span className="font-medium">方法:</span> {selectedTrace.method}
                      </div>
                      <div>
                        <span className="font-medium">路径:</span> {selectedTrace.requestPath}
                      </div>
                      <div>
                        <span className="font-medium">用户ID:</span> {selectedTrace.userId || "匿名"}
                      </div>
                      <div>
                        <span className="font-medium">IP地址:</span> {selectedTrace.ipAddress}
                      </div>
                    </div>
                  </div>

                  {/* Span时间线 */}
                  <div className="space-y-3">
                    <h4 className="font-medium">Span时间线</h4>
                    <div className="space-y-2">
                      {selectedTrace.spans.map((span) => (
                        <div key={span.id} className="flex items-center space-x-4 p-3 border rounded">
                          <div className="flex items-center space-x-2">
                            {getServiceIcon(span.service)}
                            <span className="font-medium">{span.service}</span>
                          </div>
                          <div className="flex-1">
                            <div className="flex justify-between text-sm">
                              <span>{span.name}</span>
                              <span>{span.duration}ms</span>
                            </div>
                            <div className="mt-1 bg-gray-200 rounded-full h-2">
                              <div
                                className="bg-blue-600 h-2 rounded-full"
                                style={{
                                  width: `${(span.duration / selectedTrace.duration) * 100}%`,
                                  marginLeft: `${((span.startTime - selectedTrace.startTime) / selectedTrace.duration) * 100}%`,
                                }}
                              />
                            </div>
                          </div>
                          {getStatusBadge(span.status)}
                        </div>
                      ))}
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          )}
        </TabsContent>

        {/* 统计分析 */}
        <TabsContent value="statistics" className="space-y-4">
          <div className="grid gap-4 md:grid-cols-2">
            <Card>
              <CardHeader>
                <CardTitle>追踪趋势</CardTitle>
                <CardDescription>追踪数量和成功率变化</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="h-80">
                  <StatusChart
                    type="line"
                    data={traces.slice(0, 20).reverse().map((trace, index) => ({
                      time: new Date(trace.startTime).toLocaleTimeString(),
                      duration: trace.duration,
                      success: trace.status === "success" ? 1 : 0,
                    }))}
                    dataKey="duration"
                    nameKey="time"
                    colors={["#3b82f6", "#10b981"]}
                  />
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>性能分布</CardTitle>
                <CardDescription>响应时间分布统计</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="h-80">
                  <StatusChart
                    type="bar"
                    data={[
                      { range: "0-100ms", count: traces.filter(t => t.duration < 100).length },
                      { range: "100-500ms", count: traces.filter(t => t.duration >= 100 && t.duration < 500).length },
                      { range: "500-1000ms", count: traces.filter(t => t.duration >= 500 && t.duration < 1000).length },
                      { range: "1000ms+", count: traces.filter(t => t.duration >= 1000).length },
                    ]}
                    dataKey="count"
                    nameKey="range"
                    colors={["#3b82f6"]}
                  />
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        {/* 服务监控 */}
        <TabsContent value="services" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>服务性能统计</CardTitle>
              <CardDescription>各服务的调用次数、平均响应时间和错误率</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {statistics.topServices.map((service) => (
                  <div key={service.service} className="flex items-center justify-between p-4 border rounded-lg">
                    <div className="flex items-center space-x-4">
                      <div className="flex items-center space-x-2">
                        {getServiceIcon(service.service)}
                        <span className="font-medium">{service.service}</span>
                      </div>
                    </div>
                    <div className="grid grid-cols-4 gap-4 text-sm">
                      <div>
                        <p className="font-medium">{service.count}</p>
                        <p className="text-xs text-muted-foreground">调用次数</p>
                      </div>
                      <div>
                        <p className="font-medium">{service.avgDuration.toFixed(0)}ms</p>
                        <p className="text-xs text-muted-foreground">平均响应</p>
                      </div>
                      <div>
                        <p className={`font-medium ${service.errorRate > 5 ? 'text-red-600' : service.errorRate > 1 ? 'text-yellow-600' : 'text-green-600'}`}>
                          {service.errorRate.toFixed(2)}%
                        </p>
                        <p className="text-xs text-muted-foreground">错误率</p>
                      </div>
                      <div>
                        <Progress value={Math.min(service.errorRate * 10, 100)} className="w-16" />
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* 端点分析 */}
        <TabsContent value="endpoints" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>端点性能统计</CardTitle>
              <CardDescription>各API端点的调用统计和性能指标</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {statistics.topEndpoints.map((endpoint) => (
                  <div key={`${endpoint.method}-${endpoint.path}`} className="flex items-center justify-between p-4 border rounded-lg">
                    <div className="flex items-center space-x-4">
                      <Badge className="bg-blue-100 text-blue-800 border-blue-200">
                        {endpoint.method}
                      </Badge>
                      <span className="font-medium">{endpoint.path}</span>
                    </div>
                    <div className="grid grid-cols-4 gap-4 text-sm">
                      <div>
                        <p className="font-medium">{endpoint.count}</p>
                        <p className="text-xs text-muted-foreground">调用次数</p>
                      </div>
                      <div>
                        <p className="font-medium">{endpoint.avgDuration.toFixed(0)}ms</p>
                        <p className="text-xs text-muted-foreground">平均响应</p>
                      </div>
                      <div>
                        <p className={`font-medium ${endpoint.errorRate > 5 ? 'text-red-600' : endpoint.errorRate > 1 ? 'text-yellow-600' : 'text-green-600'}`}>
                          {endpoint.errorRate.toFixed(2)}%
                        </p>
                        <p className="text-xs text-muted-foreground">错误率</p>
                      </div>
                      <div>
                        <Progress value={Math.min(endpoint.errorRate * 10, 100)} className="w-16" />
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
} 