import { Toaster } from "@/components/ui/toaster";
import { Toaster as Sonner } from "@/components/ui/sonner";
import { TooltipProvider } from "@/components/ui/tooltip";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { BrowserRouter, Routes, Route, Outlet } from "react-router-dom";
import Layout from "./components/Layout";
import Dashboard from "./pages/Dashboard";
import TestControl from "./pages/test/TestControl";
import TestPlanning from "./pages/test/TestPlanning";
import TestMonitoring from "./pages/test/TestMonitoring";
import DataAnalysis from "./pages/test/DataAnalysis";
import ComprehensiveDashboard from "./pages/test/ComprehensiveDashboard";
import ProductManagement from "./pages/test/ProductManagement";
import BaseInfo from "./pages/equipment/BaseInfo";
import ChargingManagement from "./pages/equipment/ChargingManagement";
import LightControlManagement from "./pages/equipment/LightControlManagement";
import EquipmentData from "./pages/equipment/EquipmentData";
import InOutOperations from "./pages/warehouse/InOutOperations";
import WarehouseRecords from "./pages/warehouse/WarehouseRecords";
import UserManagement from "./pages/system/UserManagement";
import ShelfManagement from "./pages/equipment/ShelfManagement";
import DigitalTwin from "./pages/system/DigitalTwin";
import SystemLogs from "./pages/system/SystemLogs";
import PerformanceMonitor from "./pages/operation/PerformanceMonitor";
import RedisMonitor from "./pages/operation/RedisMonitor";
import TomcatMonitor from "./pages/operation/TomcatMonitor";
import JVMMonitor from "./pages/operation/JVMMonitor";
import RequestTracing from "./pages/operation/RequestTracing";
import DiskMonitor from "./pages/operation/DiskMonitor";
import MaintenancePlan from "./pages/equipment/MaintenancePlan";
import EquipmentAnalysis from "./pages/equipment/EquipmentAnalysis";
import IndustrialDashboard from "./pages/IndustrialDashboard";
import ComprehensiveDisplay from "./pages/ComprehensiveDisplay";
import Test from "./pages/Test";
import Diagnostic from "./pages/Diagnostic";
import NotFound from "./pages/NotFound";
import MESDispatchRecords from "./pages/test/MESDispatchRecords";
import SpecialProductInventory from "./pages/special-products/SpecialProductInventory";
import SpecialProductInbound from "./pages/special-products/SpecialProductInbound";
import SpecialProductOutbound from "./pages/special-products/SpecialProductOutbound";
import ProductInbound from "./pages/products/ProductInbound";
import ProductInventory from "./pages/products/ProductInventory";
import ProductLedger from "./pages/products/ProductLedger";

const queryClient = new QueryClient();

const App = () => (
  <QueryClientProvider client={queryClient}>
    <TooltipProvider>
      <Toaster />
      <Sonner />
      <BrowserRouter>
        <Routes>
          {/* 综合显示页面独立路由，不使用Layout */}
          <Route path="/comprehensive-display" element={<ComprehensiveDisplay />} />
          
          {/* 其他页面使用Layout */}
          <Route path="/" element={<Layout><Outlet /></Layout>}>
            <Route index element={<Dashboard />} />
            <Route path="industrial" element={<IndustrialDashboard />} />
            <Route path="test" element={<Test />} />
            <Route path="diagnostic" element={<Diagnostic />} />
            <Route path="test/control" element={<TestControl />} />
            <Route path="test/planning" element={<TestPlanning />} />
            <Route path="test/monitoring" element={<TestMonitoring />} />
            <Route path="test/analysis" element={<DataAnalysis />} />
            <Route path="test/dashboard" element={<ComprehensiveDashboard />} />
            <Route path="test/products" element={<ProductManagement />} />
            <Route path="test/mes-dispatch" element={<MESDispatchRecords />} />
            
            {/* Product Management routes */}
            <Route path="products/inbound" element={<ProductInbound />} />
            <Route path="products/inventory" element={<ProductInventory />} />
            <Route path="products/ledger" element={<ProductLedger />} />
            
            {/* Equipment Management routes */}
            <Route path="equipment/base-info" element={<BaseInfo />} />
            <Route path="equipment/charging" element={<ChargingManagement />} />
            <Route path="equipment/lights" element={<LightControlManagement />} />
            <Route path="equipment/maintenance" element={<MaintenancePlan />} />
            <Route path="equipment/analysis" element={<EquipmentAnalysis />} />
            <Route path="equipment/data" element={<EquipmentData />} />
            
            {/* Warehouse Management routes */}
            <Route path="warehouse/operations" element={<InOutOperations />} />
            <Route path="warehouse/records" element={<WarehouseRecords />} />
            
            {/* Special Product Management routes */}
            <Route path="special-products/inventory" element={<SpecialProductInventory />} />
            <Route path="special-products/inbound" element={<SpecialProductInbound />} />
            <Route path="special-products/outbound" element={<SpecialProductOutbound />} />
            
            {/* System Management routes */}
            <Route path="system/users" element={<UserManagement />} />
            <Route path="system/roles" element={<UserManagement />} />
            <Route path="system/settings" element={<UserManagement />} />
            <Route path="system/logs" element={<SystemLogs />} />
            <Route path="system/digital-twin" element={<DigitalTwin />} />
            
            {/* Equipment Management routes - moved from system */}
            <Route path="equipment/shelves" element={<ShelfManagement />} />
            
            {/* Operation Management routes */}
            <Route path="operation/performance" element={<PerformanceMonitor />} />
            <Route path="operation/redis" element={<RedisMonitor />} />
            <Route path="operation/tomcat" element={<TomcatMonitor />} />
            <Route path="operation/jvm" element={<JVMMonitor />} />
            <Route path="operation/tracing" element={<RequestTracing />} />
            <Route path="operation/disk" element={<DiskMonitor />} />
            
            {/* Catch-all route */}
            <Route path="*" element={<NotFound />} />
          </Route>
        </Routes>
      </BrowserRouter>
    </TooltipProvider>
  </QueryClientProvider>
);

export default App;
