# 惯组产品智能化存储及月稳测试系统 - 功能分析文档

## 1. 系统总体架构

### 1.1 系统概述
惯组产品智能化存储及月稳测试系统是一套专为惯性导航组件生产企业设计的智能仓储与测试管理平台。系统集成了仓储管理、自动化测试、设备监控、数据分析等功能，实现了惯组产品从入库、存储、出库测试到数据分析的全流程自动化管理。

### 1.2 系统架构图
```
┌─────────────────────────────────────────────────────────────┐
│                    前端展示层                                │
├─────────────────────────────────────────────────────────────┤
│  Web界面  │  工业大屏  │  移动端  │  管理后台                │
├─────────────────────────────────────────────────────────────┤
│                    业务逻辑层                                │
├─────────────────────────────────────────────────────────────┤
│ 测试管理 │ 设备管理 │ 仓储管理 │ 用户管理 │ 系统管理          │
├─────────────────────────────────────────────────────────────┤
│                    数据访问层                                │
├─────────────────────────────────────────────────────────────┤
│  MySQL  │  Redis  │  InfluxDB  │  文件存储                  │
├─────────────────────────────────────────────────────────────┤
│                    设备控制层                                │
├─────────────────────────────────────────────────────────────┤
│  桁架  │  AGV  │  测试台  │  充电桩  │  三色灯              │
└─────────────────────────────────────────────────────────────┘
```

## 2. 核心功能模块分析

### 2.1 工业大屏可视化模块

#### 2.1.1 功能描述
工业大屏模块提供仓库和设备的3D可视化展示，适合在大屏幕上实时监控系统运行状态。

#### 2.1.2 主要功能
- **3D仓库可视化**: 展示仓库布局、货架位置、设备分布
- **设备状态监控**: 实时显示设备运行状态、位置信息
- **任务进度展示**: 显示当前执行任务的进度和状态
- **告警信息推送**: 实时显示系统告警和异常信息
- **环境数据展示**: 显示仓库环境参数（温度、湿度等）

#### 2.1.3 业务流程
1. 系统启动时加载3D模型和初始数据
2. 通过WebSocket接收实时数据更新
3. 根据设备状态更新3D模型显示
4. 处理用户交互（视角切换、图层控制等）
5. 定时刷新数据和状态

#### 2.1.4 数据需求
- 设备位置和状态数据
- 任务执行进度数据
- 告警信息数据
- 环境监控数据

### 2.2 测试管理系统

#### 2.2.1 测试控制中心

**功能描述**: 测试控制中心是系统的核心模块，负责管理和控制所有测试任务。

**主要功能**:
- **任务创建**: 创建不同类型的测试任务
- **任务控制**: 启动、暂停、终止、恢复任务
- **模式切换**: 自动模式和手动模式切换
- **实时监控**: 监控任务执行状态和进度
- **故障处理**: 一键还原功能处理任务故障

**业务流程**:
1. 用户创建测试任务，选择产品、测试类型等参数
2. 系统验证任务参数和资源可用性
3. 任务进入队列等待执行
4. 系统按优先级调度任务执行
5. 任务执行过程中实时更新状态
6. 任务完成后更新产品状态和库存

**数据需求**:
- 产品信息数据
- 设备状态数据
- 任务执行数据
- 测试结果数据

#### 2.2.2 测试计划管理

**功能描述**: 测试计划管理用于排程和管理测试计划，支持多种计划类型。

**主要功能**:
- **计划创建**: 创建日计划、周计划、月计划等
- **资源分配**: 分配测试资源（设备、人员等）
- **计划审批**: 计划创建后需要审批流程
- **计划执行**: 按计划自动执行测试任务
- **计划调整**: 支持计划修改和重新排程

**业务流程**:
1. 用户创建测试计划，设置产品类型、数量、时间等
2. 系统检查资源冲突和可用性
3. 计划提交审批流程
4. 审批通过后计划生效
5. 系统按计划自动创建和执行任务
6. 计划执行过程中可进行调整

#### 2.2.3 测试监控

**功能描述**: 测试监控页面用于实时监控测试任务的执行状态和进度。

**主要功能**:
- **任务列表**: 显示当前所有执行中的任务
- **任务详情**: 查看任务的详细执行步骤
- **一键还原**: 故障快速恢复功能
- **任务控制**: 暂停、继续、终止任务
- **实时更新**: 自动刷新任务状态

**业务流程**:
1. 页面加载时获取当前任务列表
2. 定时刷新任务状态和进度
3. 用户可查看任务详情和步骤
4. 发现故障时可使用一键还原
5. 支持手动控制任务执行

#### 2.2.4 数据分析查询

**功能描述**: 数据分析查询用于查询和分析历史测试数据，支持多种分析维度。

**主要功能**:
- **数据查询**: 按条件查询历史测试数据
- **统计分析**: 生成测试数据统计图表
- **趋势分析**: 分析测试参数的历史趋势
- **报表导出**: 支持多种格式的数据导出
- **告警设置**: 设置数据异常告警阈值

### 2.3 设备管理系统

#### 2.3.1 设备基准信息

**功能描述**: 设备基准信息管理用于维护系统中所有设备的基本信息。

**主要功能**:
- **设备档案**: 维护设备基本信息、技术参数
- **设备分类**: 按类型管理设备（桁架、AGV、测试台等）
- **设备状态**: 监控设备在线状态和运行状态
- **设备参数**: 配置设备运行参数
- **维护记录**: 记录设备维护历史

#### 2.3.2 AGV充电管理

**功能描述**: AGV充电管理负责AGV的充电策略和状态监控。

**主要功能**:
- **电量监控**: 实时监控AGV电池电量
- **充电策略**: 设置自动充电阈值和策略
- **充电调度**: 自动调度AGV前往充电桩
- **充电状态**: 监控充电过程和状态
- **路径优化**: 优化AGV充电路径

**业务流程**:
1. 系统监控AGV电池电量
2. 电量低于阈值时触发充电策略
3. 选择空闲的充电桩
4. 调度AGV前往充电桩
5. 监控充电过程
6. 充电完成后恢复AGV可用状态

#### 2.3.3 三色灯控制

**功能描述**: 三色灯控制系统用于指示库位状态，提供视觉化的状态展示。

**主要功能**:
- **控制板管理**: 管理三色灯控制板
- **灯位映射**: 配置灯与库位的映射关系
- **状态控制**: 根据库位状态控制灯的颜色
- **批量控制**: 支持批量控制多个灯
- **状态监控**: 监控控制板和灯的状态

**业务流程**:
1. 配置控制板IP地址和通信参数
2. 建立灯与库位的映射关系
3. 根据库位状态自动控制灯颜色
4. 支持手动控制灯的状态
5. 监控控制板连接状态

#### 2.3.4 设备维护计划

**功能描述**: 设备维护计划用于管理设备的预防性维护。

**主要功能**:
- **维护计划**: 制定设备维护计划
- **维护提醒**: 自动提醒维护时间
- **维护记录**: 记录维护内容和结果
- **维护统计**: 统计维护历史和成本
- **维护分析**: 分析设备维护趋势

#### 2.3.5 设备数据分析

**功能描述**: 设备数据分析用于分析设备的运行数据和性能。

**主要功能**:
- **运行数据**: 收集和分析设备运行数据
- **性能分析**: 分析设备性能指标
- **故障分析**: 分析设备故障原因和频率
- **寿命预测**: 预测设备使用寿命
- **优化建议**: 提供设备优化建议

### 2.4 出入库管理

#### 2.4.1 出入库操作

**功能描述**: 出入库操作模块负责管理产品的出入库流程。

**主要功能**:
- **产品出库**: 执行产品出库操作
- **产品入库**: 执行产品入库操作
- **库位查询**: 查询产品所在位置
- **库存盘点**: 执行库存盘点操作
- **异常处理**: 处理出入库异常情况

**业务流程**:
1. 用户选择出入库操作类型
2. 扫描或输入产品编码
3. 系统验证产品信息
4. 选择目标库位
5. 执行出入库操作
6. 更新库存记录

#### 2.4.2 出入库记录

**功能描述**: 出入库记录模块用于查询和管理历史出入库记录。

**主要功能**:
- **记录查询**: 按条件查询出入库记录
- **记录统计**: 统计出入库数据
- **报表生成**: 生成出入库报表
- **数据导出**: 导出出入库数据
- **趋势分析**: 分析出入库趋势

### 2.5 系统管理

#### 2.5.1 用户管理

**功能描述**: 用户管理模块负责管理系统用户和权限。

**主要功能**:
- **用户创建**: 创建新用户账号
- **用户编辑**: 编辑用户信息
- **密码管理**: 重置用户密码
- **状态管理**: 管理用户状态
- **权限分配**: 分配用户权限

#### 2.5.2 角色管理

**功能描述**: 角色管理模块用于管理系统角色和权限配置。

**主要功能**:
- **角色创建**: 创建新的系统角色
- **权限配置**: 配置角色权限
- **角色编辑**: 编辑角色信息
- **角色删除**: 删除不需要的角色
- **权限继承**: 支持权限继承关系

#### 2.5.3 系统设置

**功能描述**: 系统设置模块用于配置系统参数和选项。

**主要功能**:
- **基本设置**: 配置系统基本信息
- **安全设置**: 配置安全相关参数
- **备份设置**: 配置数据备份策略
- **日志设置**: 配置日志记录选项
- **通知设置**: 配置通知和告警

#### 2.5.4 数字孪生

**功能描述**: 数字孪生模块用于管理系统的数字孪生模型。

**主要功能**:
- **模型管理**: 管理3D模型和参数
- **数据映射**: 映射实际设备数据
- **模拟测试**: 在虚拟环境中测试
- **优化分析**: 基于模型进行优化分析

## 3. 业务流程分析

### 3.1 产品出库测试流程

**流程描述**: 产品从仓库出库进行测试的完整流程。

**详细步骤**:
1. **测试计划创建**: 在测试计划管理中创建测试计划
2. **任务生成**: 系统根据计划自动生成测试任务
3. **MES通知**: ICS通知MES系统开始出库测试
4. **桁架操作**: 桁架系统将产品从库位搬运到接驳台
5. **AGV运输**: AGV将产品从接驳台运输到测试台
6. **测试连接**: 测试台机械臂连接测试电缆
7. **测试执行**: 执行产品测试
8. **测试完成**: 测试完成后断开连接
9. **产品入库**: AGV将产品运输回仓库
10. **库存更新**: 更新产品状态和库存记录

**关键节点**:
- 每个步骤都有状态监控和异常处理
- 支持手动干预和故障恢复
- 记录详细的执行日志和时间戳

### 3.2 设备故障处理流程

**流程描述**: 当设备出现故障时的处理流程。

**详细步骤**:
1. **故障检测**: 系统检测到设备故障
2. **告警通知**: 发送告警通知给相关人员
3. **故障分析**: 分析故障原因和影响范围
4. **任务暂停**: 暂停受影响的测试任务
5. **设备维护**: 执行设备维护和修复
6. **功能测试**: 测试设备功能是否恢复
7. **任务恢复**: 恢复暂停的测试任务
8. **故障记录**: 记录故障处理过程和结果

### 3.3 用户权限管理流程

**流程描述**: 用户权限的申请、审批和管理流程。

**详细步骤**:
1. **权限申请**: 用户申请特定权限
2. **申请审批**: 管理员审批权限申请
3. **权限分配**: 分配相应的系统权限
4. **权限验证**: 验证权限分配是否正确
5. **权限监控**: 监控用户权限使用情况
6. **权限回收**: 根据需要回收用户权限

## 4. 数据流分析

### 4.1 实时数据流

**数据源**: 设备传感器、PLC、SCADA系统
**数据流向**: 设备 → 数据采集 → 数据处理 → 数据库 → 前端展示
**数据特点**: 高频、实时、结构化

### 4.2 业务数据流

**数据源**: 用户操作、系统自动生成
**数据流向**: 前端操作 → 业务逻辑 → 数据库 → 报表分析
**数据特点**: 低频、事务性、关联性强

### 4.3 历史数据流

**数据源**: 实时数据、业务数据
**数据流向**: 实时数据 → 数据仓库 → 数据分析 → 报表展示
**数据特点**: 大容量、分析性、时间序列

## 5. 系统集成分析

### 5.1 与MES系统集成

**集成方式**: RESTful API
**数据交换**: 生产计划、产品信息、测试结果
**集成点**: 测试任务创建、结果反馈、状态同步

### 5.2 与WMS系统集成

**集成方式**: RESTful API
**数据交换**: 库存信息、库位状态、出入库指令
**集成点**: 库存查询、出入库操作、库位管理

### 5.3 与设备控制系统集成

**集成方式**: OPC UA、Modbus TCP
**数据交换**: 设备状态、控制指令、传感器数据
**集成点**: 设备监控、远程控制、数据采集

## 6. 性能要求分析

### 6.1 响应时间要求

- **页面加载**: < 3秒
- **数据查询**: < 1秒
- **实时数据更新**: < 500ms
- **设备控制响应**: < 200ms

### 6.2 并发用户要求

- **同时在线用户**: 100+
- **并发操作**: 50+
- **WebSocket连接**: 200+

### 6.3 数据处理要求

- **实时数据处理**: 1000+ 条/秒
- **历史数据存储**: 5年+
- **数据备份**: 每日增量，每周全量

## 7. 安全要求分析

### 7.1 访问控制

- **用户认证**: JWT Token认证
- **权限控制**: 基于角色的访问控制(RBAC)
- **会话管理**: 会话超时和自动登出

### 7.2 数据安全

- **数据加密**: 敏感数据加密存储
- **传输安全**: HTTPS加密传输
- **数据备份**: 定期数据备份和恢复

### 7.3 操作审计

- **操作日志**: 记录所有用户操作
- **登录日志**: 记录用户登录信息
- **异常监控**: 监控异常操作和访问

## 8. 扩展性分析

### 8.1 功能扩展

- **模块化设计**: 支持新功能模块的添加
- **插件机制**: 支持第三方插件集成
- **API开放**: 提供开放的API接口

### 8.2 性能扩展

- **水平扩展**: 支持多实例部署
- **负载均衡**: 支持负载均衡和集群
- **缓存机制**: 支持多级缓存优化

### 8.3 集成扩展

- **标准接口**: 支持标准化的集成接口
- **协议支持**: 支持多种通信协议
- **数据格式**: 支持多种数据格式交换 