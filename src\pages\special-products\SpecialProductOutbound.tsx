import React, { useState } from "react";
import { <PERSON>, Card<PERSON>ontent, Card<PERSON>eader, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog";
import { Checkbox } from "@/components/ui/checkbox";
import { 
  Package, 
  Search, 
  MapPin, 
  Calendar,
  Truck,
  CheckCircle,
  AlertCircle,
  Clock,
  Archive,
  Download,
  Eye
} from "lucide-react";

// 模拟在库特殊产品数据
const mockInStockProducts = [
  {
    id: "SP001",
    productCode: "SP-2024-001",
    productType: "精密仪器",
    status: "正常",
          area: "B区",
    shelfNumber: "E-01",
    positionNumber: "E-01-01",
    registrationTime: "2024-01-15 09:30:00",
    rfidTag: "RFID-E001-001",
    description: "高精度测量仪器",
    outboundStatus: "在库"
  },
  {
    id: "SP002",
    productCode: "SP-2024-002",
    productType: "光学设备",
    status: "正常",
          area: "C区",
    shelfNumber: "F-02",
    positionNumber: "F-02-03",
    registrationTime: "2024-01-16 14:20:00",
    rfidTag: "RFID-F002-003",
    description: "光学检测设备",
    outboundStatus: "在库"
  },
  {
    id: "SP003",
    productCode: "SP-2024-003",
    productType: "电子元件",
    status: "正常",
    area: "G区",
    shelfNumber: "G-03",
    positionNumber: "G-03-02",
    registrationTime: "2024-01-17 11:45:00",
    rfidTag: "RFID-G003-002",
    description: "高价值电子元件",
    outboundStatus: "在库"
  }
];

export default function SpecialProductOutbound() {
  const [products, setProducts] = useState(mockInStockProducts);
  const [searchTerm, setSearchTerm] = useState("");
  const [areaFilter, setAreaFilter] = useState("全部");
  const [selectedProducts, setSelectedProducts] = useState<string[]>([]);
  const [selectedDock, setSelectedDock] = useState("");
  const [isOutboundDialogOpen, setIsOutboundDialogOpen] = useState(false);

  // 统计数据
  const totalProducts = products.length;
  const outboundProducts = products.filter(p => p.outboundStatus === "出库中").length;
  const completedOutbound = products.filter(p => p.outboundStatus === "已出库").length;

  // 过滤产品
  const filteredProducts = products.filter(product => {
    const matchesSearch = product.productCode.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         product.productType.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         product.rfidTag.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesArea = areaFilter === "全部" || product.area === areaFilter;
    
    return matchesSearch && matchesArea;
  });

  // 处理产品选择
  const handleProductSelection = (productId: string, checked: boolean) => {
    if (checked) {
      setSelectedProducts([...selectedProducts, productId]);
    } else {
      setSelectedProducts(selectedProducts.filter(id => id !== productId));
    }
  };

  // 处理全选
  const handleSelectAll = (checked: boolean) => {
    if (checked) {
      setSelectedProducts(filteredProducts.map(p => p.id));
    } else {
      setSelectedProducts([]);
    }
  };

  // 处理出库操作
  const handleOutboundOperation = () => {
    if (selectedProducts.length === 0) {
      alert("请选择要出库的产品");
      return;
    }
    if (!selectedDock) {
      alert("请选择接驳台");
      return;
    }

    // 更新产品状态为出库中
    setProducts(products.map(product => 
      selectedProducts.includes(product.id)
        ? { ...product, outboundStatus: "出库中" }
        : product
    ));

    // 模拟出库过程
    setTimeout(() => {
      setProducts(products.map(product => 
        selectedProducts.includes(product.id)
          ? { ...product, outboundStatus: "已出库" }
          : product
      ));
      setSelectedProducts([]);
      setIsOutboundDialogOpen(false);
    }, 5000);
  };

  const getOutboundStatusBadge = (status: string) => {
    switch (status) {
      case "在库":
        return <Badge className="bg-green-100 text-green-800 border-green-200">在库</Badge>;
      case "出库中":
        return <Badge className="bg-blue-100 text-blue-800 border-blue-200">出库中</Badge>;
      case "已出库":
        return <Badge className="bg-gray-100 text-gray-800 border-gray-200">已出库</Badge>;
      default:
        return <Badge variant="outline">{status}</Badge>;
    }
  };

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h2 className="text-3xl font-bold tracking-tight text-slate-800">特殊产品出库</h2>
        <div className="flex items-center space-x-2">
          <Button 
            variant="outline" 
            className="flex items-center gap-2"
            disabled={selectedProducts.length === 0}
            onClick={() => setIsOutboundDialogOpen(true)}
          >
            <Truck className="h-4 w-4" />
            批量出库 ({selectedProducts.length})
          </Button>
          <Button variant="outline" className="flex items-center gap-2">
            <Download className="h-4 w-4" />
            导出记录
          </Button>
        </div>
      </div>

      {/* 统计卡片 */}
      <div className="grid gap-4 md:grid-cols-4">
        <Card className="blue-tech-card">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium text-slate-700">在库产品</CardTitle>
            <Package className="h-4 w-4 text-blue-500" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-slate-900">{totalProducts}</div>
            <p className="text-xs text-slate-500">可出库产品</p>
          </CardContent>
        </Card>

        <Card className="blue-tech-card">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium text-slate-700">出库中</CardTitle>
            <Truck className="h-4 w-4 text-yellow-500" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-slate-900">{outboundProducts}</div>
            <p className="text-xs text-slate-500">正在出库</p>
          </CardContent>
        </Card>

        <Card className="blue-tech-card">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium text-slate-700">今日出库</CardTitle>
            <CheckCircle className="h-4 w-4 text-green-500" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-slate-900">{completedOutbound}</div>
            <p className="text-xs text-slate-500">已完成出库</p>
          </CardContent>
        </Card>

        <Card className="blue-tech-card">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium text-slate-700">接驳台状态</CardTitle>
            <Archive className="h-4 w-4 text-purple-500" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-slate-900">3/5</div>
            <p className="text-xs text-slate-500">可用接驳台</p>
          </CardContent>
        </Card>
      </div>

      {/* 搜索和筛选 */}
      <Card className="blue-tech-card">
        <CardHeader>
          <CardTitle className="text-slate-800">搜索和筛选</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex flex-col md:flex-row gap-4">
            <div className="flex-1">
              <div className="relative">
                <Search className="absolute left-3 top-3 h-4 w-4 text-slate-400" />
                <Input
                  placeholder="搜索产品编号、类型或RFID标签..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10"
                />
              </div>
            </div>
            <Select value={areaFilter} onValueChange={setAreaFilter}>
              <SelectTrigger className="w-full md:w-40">
                <SelectValue placeholder="区域筛选" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="全部">全部区域</SelectItem>
                                       <SelectItem value="B区">B区</SelectItem>
                       <SelectItem value="C区">C区</SelectItem>
                <SelectItem value="G区">G区</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </CardContent>
      </Card>

      {/* 产品列表 */}
      <Card className="blue-tech-card">
        <CardHeader>
          <CardTitle className="text-slate-800">在库特殊产品列表</CardTitle>
        </CardHeader>
        <CardContent>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead className="w-12">
                  <Checkbox 
                    checked={selectedProducts.length === filteredProducts.length && filteredProducts.length > 0}
                    onCheckedChange={handleSelectAll}
                  />
                </TableHead>
                <TableHead>产品编号</TableHead>
                <TableHead>产品类型</TableHead>
                <TableHead>RFID标签</TableHead>
                <TableHead>当前位置</TableHead>
                <TableHead>出库状态</TableHead>
                <TableHead>登记时间</TableHead>
                <TableHead>操作</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {filteredProducts.map((product) => (
                <TableRow key={product.id}>
                  <TableCell>
                    <Checkbox 
                      checked={selectedProducts.includes(product.id)}
                      onCheckedChange={(checked) => handleProductSelection(product.id, checked as boolean)}
                      disabled={product.outboundStatus !== "在库"}
                    />
                  </TableCell>
                  <TableCell className="font-medium">{product.productCode}</TableCell>
                  <TableCell>{product.productType}</TableCell>
                  <TableCell className="font-mono text-sm">{product.rfidTag}</TableCell>
                  <TableCell>
                    <div className="flex items-center gap-1">
                      <MapPin className="h-3 w-3 text-slate-500" />
                      <span>{product.area} {product.shelfNumber} {product.positionNumber}</span>
                    </div>
                  </TableCell>
                  <TableCell>{getOutboundStatusBadge(product.outboundStatus)}</TableCell>
                  <TableCell>
                    <div className="flex items-center gap-1">
                      <Calendar className="h-3 w-3 text-slate-500" />
                      <span className="text-sm">{product.registrationTime}</span>
                    </div>
                  </TableCell>
                  <TableCell>
                    <div className="flex items-center gap-2">
                      <Dialog>
                        <DialogTrigger asChild>
                          <Button variant="outline" size="sm">
                            <Eye className="h-4 w-4" />
                          </Button>
                        </DialogTrigger>
                        <DialogContent>
                          <DialogHeader>
                            <DialogTitle>产品详情</DialogTitle>
                          </DialogHeader>
                          <div className="space-y-4">
                            <div className="grid grid-cols-2 gap-4">
                              <div>
                                <label className="text-sm font-medium text-slate-600">产品编号</label>
                                <p className="text-sm">{product.productCode}</p>
                              </div>
                              <div>
                                <label className="text-sm font-medium text-slate-600">产品类型</label>
                                <p className="text-sm">{product.productType}</p>
                              </div>
                              <div>
                                <label className="text-sm font-medium text-slate-600">RFID标签</label>
                                <p className="text-sm font-mono">{product.rfidTag}</p>
                              </div>
                              <div>
                                <label className="text-sm font-medium text-slate-600">出库状态</label>
                                <div>{getOutboundStatusBadge(product.outboundStatus)}</div>
                              </div>
                              <div>
                                <label className="text-sm font-medium text-slate-600">区域</label>
                                <p className="text-sm">{product.area}</p>
                              </div>
                              <div>
                                <label className="text-sm font-medium text-slate-600">货架号</label>
                                <p className="text-sm">{product.shelfNumber}</p>
                              </div>
                              <div>
                                <label className="text-sm font-medium text-slate-600">货位号</label>
                                <p className="text-sm">{product.positionNumber}</p>
                              </div>
                              <div>
                                <label className="text-sm font-medium text-slate-600">登记时间</label>
                                <p className="text-sm">{product.registrationTime}</p>
                              </div>
                            </div>
                            <div>
                              <label className="text-sm font-medium text-slate-600">产品描述</label>
                              <p className="text-sm">{product.description}</p>
                            </div>
                          </div>
                        </DialogContent>
                      </Dialog>
                      {product.outboundStatus === "在库" && (
                        <Button 
                          size="sm" 
                          variant="outline" 
                          className="text-orange-600 border-orange-200 hover:bg-orange-50"
                          onClick={() => {
                            setSelectedProducts([product.id]);
                            setIsOutboundDialogOpen(true);
                          }}
                        >
                          出库操作
                        </Button>
                      )}
                      {product.outboundStatus === "出库中" && (
                        <div className="flex items-center gap-2">
                          <AlertCircle className="h-4 w-4 text-blue-500 animate-pulse" />
                          <span className="text-sm text-blue-600">出库中...</span>
                        </div>
                      )}
                      {product.outboundStatus === "已出库" && (
                        <div className="flex items-center gap-2">
                          <CheckCircle className="h-4 w-4 text-green-500" />
                          <span className="text-sm text-green-600">已出库</span>
                        </div>
                      )}
                    </div>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </CardContent>
      </Card>

      {/* 出库操作对话框 */}
      <Dialog open={isOutboundDialogOpen} onOpenChange={setIsOutboundDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>出库操作</DialogTitle>
          </DialogHeader>
          <div className="space-y-4">
            <div>
              <label className="text-sm font-medium text-slate-600">选择接驳台</label>
              <Select value={selectedDock} onValueChange={setSelectedDock}>
                <SelectTrigger>
                  <SelectValue placeholder="选择接驳台" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="接驳台-01">接驳台-01</SelectItem>
                  <SelectItem value="接驳台-02">接驳台-02</SelectItem>
                  <SelectItem value="接驳台-03">接驳台-03</SelectItem>
                </SelectContent>
              </Select>
            </div>
            
            <div>
              <label className="text-sm font-medium text-slate-600">选中产品</label>
              <div className="mt-2 space-y-2 max-h-40 overflow-y-auto">
                {selectedProducts.map(productId => {
                  const product = products.find(p => p.id === productId);
                  return product ? (
                    <div key={productId} className="flex items-center justify-between p-2 bg-slate-50 rounded">
                      <span className="text-sm">{product.productCode}</span>
                      <span className="text-xs text-slate-500">{product.area} {product.shelfNumber} {product.positionNumber}</span>
                    </div>
                  ) : null;
                })}
              </div>
            </div>

            <div className="flex justify-end space-x-2">
              <Button variant="outline" onClick={() => setIsOutboundDialogOpen(false)}>
                取消
              </Button>
              <Button onClick={handleOutboundOperation}>
                确认出库
              </Button>
            </div>
          </div>
        </DialogContent>
      </Dialog>
    </div>
  );
} 