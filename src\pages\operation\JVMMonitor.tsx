import { useState, useEffect } from "react";
import {
  <PERSON>,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/tabs";
import { StatusChart } from "@/components/StatusChart";
import { Progress } from "@/components/ui/progress";
import {
  Cpu,
  Activity,
  MemoryStick,
  Clock,
  RefreshCw,
  Download,
  AlertTriangle,
  CheckCircle,
  TrendingUp,
  TrendingDown,
  Zap,
  Database,
  FileText,
  Settings,
  Gauge,
  Layers,
  Package,
  Trash2,
} from "lucide-react";

interface JVMMetrics {
  timestamp: string;
  runtime: {
    name: string;
    version: string;
    vendor: string;
    startTime: number;
    uptime: number;
    availableProcessors: number;
  };
  memory: {
    heapMemoryUsage: {
      init: number;
      used: number;
      committed: number;
      max: number;
    };
    nonHeapMemoryUsage: {
      init: number;
      used: number;
      committed: number;
      max: number;
    };
    objectPendingFinalizationCount: number;
  };
  threads: {
    threadCount: number;
    peakThreadCount: number;
    daemonThreadCount: number;
    totalStartedThreadCount: number;
    deadlockedThreads: number;
    deadlockedThreadsCount: number;
  };
  classLoading: {
    loadedClassCount: number;
    totalLoadedClassCount: number;
    unloadedClassCount: number;
  };
  garbageCollection: {
    collectors: {
      name: string;
      collectionCount: number;
      collectionTime: number;
    }[];
  };
  operatingSystem: {
    name: string;
    version: string;
    arch: string;
    availableProcessors: number;
    systemLoadAverage: number;
    committedVirtualMemorySize: number;
    totalPhysicalMemorySize: number;
    freePhysicalMemorySize: number;
    totalSwapSpaceSize: number;
    freeSwapSpaceSize: number;
  };
  compilation: {
    totalCompilationTime: number;
  };
}

export default function JVMMonitor() {
  const [metrics, setMetrics] = useState<JVMMetrics[]>([]);
  const [currentMetrics, setCurrentMetrics] = useState<JVMMetrics | null>(null);
  const [isAutoRefresh, setIsAutoRefresh] = useState(true);

  // 模拟JVM数据
  useEffect(() => {
    const generateMetrics = (): JVMMetrics => ({
      timestamp: new Date().toLocaleString(),
      runtime: {
        name: "Java(TM) SE Runtime Environment",
        version: "17.0.8+9-LTS-211",
        vendor: "Oracle Corporation",
        startTime: Date.now() - (86400 + Math.random() * 3600) * 1000,
        uptime: 86400 + Math.random() * 3600,
        availableProcessors: 8,
      },
      memory: {
        heapMemoryUsage: {
          init: 268435456, // 256MB
          used: 1073741824 + Math.random() * 536870912, // 1-1.5GB
          committed: 2147483648, // 2GB
          max: 4294967296, // 4GB
        },
        nonHeapMemoryUsage: {
          init: 67108864, // 64MB
          used: 134217728 + Math.random() * 67108864, // 128-192MB
          committed: 268435456, // 256MB
          max: 536870912, // 512MB
        },
        objectPendingFinalizationCount: Math.floor(Math.random() * 10),
      },
      threads: {
        threadCount: 50 + Math.random() * 30,
        peakThreadCount: 80 + Math.random() * 20,
        daemonThreadCount: 30 + Math.random() * 15,
        totalStartedThreadCount: 200 + Math.random() * 100,
        deadlockedThreads: Math.floor(Math.random() * 5),
        deadlockedThreadsCount: Math.floor(Math.random() * 3),
      },
      classLoading: {
        loadedClassCount: 8000 + Math.random() * 2000,
        totalLoadedClassCount: 10000 + Math.random() * 3000,
        unloadedClassCount: 500 + Math.random() * 200,
      },
      garbageCollection: {
        collectors: [
          {
            name: "G1 Young Generation",
            collectionCount: 100 + Math.random() * 50,
            collectionTime: 5000 + Math.random() * 3000,
          },
          {
            name: "G1 Old Generation",
            collectionCount: 10 + Math.random() * 5,
            collectionTime: 20000 + Math.random() * 10000,
          },
        ],
      },
      operatingSystem: {
        name: "Linux",
        version: "5.15.0-91-generic",
        arch: "amd64",
        availableProcessors: 8,
        systemLoadAverage: 0.5 + Math.random() * 2,
        committedVirtualMemorySize: 8589934592 + Math.random() * 1073741824, // 8-9GB
        totalPhysicalMemorySize: 17179869184, // 16GB
        freePhysicalMemorySize: 5368709120 + Math.random() * 2147483648, // 5-7GB
        totalSwapSpaceSize: 8589934592, // 8GB
        freeSwapSpaceSize: 6442450944 + Math.random() * 2147483648, // 6-8GB
      },
      compilation: {
        totalCompilationTime: 30000 + Math.random() * 20000,
      },
    });

    const updateMetrics = () => {
      const newMetrics = generateMetrics();
      setCurrentMetrics(newMetrics);
      setMetrics(prev => [...prev.slice(-59), newMetrics]);
    };

    updateMetrics();
    const interval = setInterval(updateMetrics, 5000);

    return () => clearInterval(interval);
  }, []);

  if (!currentMetrics) return <div>加载中...</div>;

  const heapUsage = (currentMetrics.memory.heapMemoryUsage.used / currentMetrics.memory.heapMemoryUsage.max) * 100;
  const nonHeapUsage = (currentMetrics.memory.nonHeapMemoryUsage.used / currentMetrics.memory.nonHeapMemoryUsage.max) * 100;
  const systemMemoryUsage = ((currentMetrics.operatingSystem.totalPhysicalMemorySize - currentMetrics.operatingSystem.freePhysicalMemorySize) / currentMetrics.operatingSystem.totalPhysicalMemorySize) * 100;

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <div>
          <h2 className="text-3xl font-bold tracking-tight">JVM监控</h2>
          <p className="text-muted-foreground">
            监控Java虚拟机性能指标、内存使用、垃圾回收和线程状态
          </p>
        </div>
        <div className="flex items-center space-x-3">
          <Button
            variant="outline"
            size="sm"
            onClick={() => setIsAutoRefresh(!isAutoRefresh)}
          >
            <RefreshCw className={`mr-2 h-4 w-4 ${isAutoRefresh ? 'animate-spin' : ''}`} />
            {isAutoRefresh ? '自动刷新' : '手动刷新'}
          </Button>
          <Button variant="outline" size="sm">
            <Download className="mr-2 h-4 w-4" />
            导出报告
          </Button>
        </div>
      </div>

      {/* 关键指标卡片 */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">堆内存使用率</CardTitle>
            <MemoryStick className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className={`text-2xl font-bold ${heapUsage > 80 ? 'text-red-600' : heapUsage > 60 ? 'text-yellow-600' : 'text-green-600'}`}>
              {heapUsage.toFixed(1)}%
            </div>
            <div className="flex items-center justify-between mt-2">
              <Progress value={heapUsage} className="flex-1 mr-2" />
              <Badge className={heapUsage > 80 ? 'bg-red-100 text-red-800 border-red-200' : heapUsage > 60 ? 'bg-yellow-100 text-yellow-800 border-yellow-200' : 'bg-green-100 text-green-800 border-green-200'}>
                {heapUsage > 80 ? '高' : heapUsage > 60 ? '中' : '正常'}
              </Badge>
            </div>
            <p className="text-xs text-muted-foreground mt-2">
              已用: {(currentMetrics.memory.heapMemoryUsage.used / 1024 / 1024 / 1024).toFixed(1)}GB / {(currentMetrics.memory.heapMemoryUsage.max / 1024 / 1024 / 1024).toFixed(1)}GB
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">非堆内存使用率</CardTitle>
            <Database className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className={`text-2xl font-bold ${nonHeapUsage > 80 ? 'text-red-600' : nonHeapUsage > 60 ? 'text-yellow-600' : 'text-green-600'}`}>
              {nonHeapUsage.toFixed(1)}%
            </div>
            <div className="flex items-center justify-between mt-2">
              <Progress value={nonHeapUsage} className="flex-1 mr-2" />
              <Badge className={nonHeapUsage > 80 ? 'bg-red-100 text-red-800 border-red-200' : nonHeapUsage > 60 ? 'bg-yellow-100 text-yellow-800 border-yellow-200' : 'bg-green-100 text-green-800 border-green-200'}>
                {nonHeapUsage > 80 ? '高' : nonHeapUsage > 60 ? '中' : '正常'}
              </Badge>
            </div>
            <p className="text-xs text-muted-foreground mt-2">
              已用: {(currentMetrics.memory.nonHeapMemoryUsage.used / 1024 / 1024).toFixed(0)}MB / {(currentMetrics.memory.nonHeapMemoryUsage.max / 1024 / 1024).toFixed(0)}MB
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">活跃线程数</CardTitle>
            <Activity className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-blue-600">
              {currentMetrics.threads.threadCount}
            </div>
            <div className="flex items-center justify-between mt-2">
              <Progress value={Math.min(currentMetrics.threads.threadCount / 200 * 100, 100)} className="flex-1 mr-2" />
              <Badge className="bg-blue-100 text-blue-800 border-blue-200">正常</Badge>
            </div>
            <p className="text-xs text-muted-foreground mt-2">
              峰值: {currentMetrics.threads.peakThreadCount}
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">系统负载</CardTitle>
            <Gauge className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className={`text-2xl font-bold ${currentMetrics.operatingSystem.systemLoadAverage > 2 ? 'text-red-600' : currentMetrics.operatingSystem.systemLoadAverage > 1 ? 'text-yellow-600' : 'text-green-600'}`}>
              {currentMetrics.operatingSystem.systemLoadAverage.toFixed(2)}
            </div>
            <div className="flex items-center justify-between mt-2">
              <Progress value={Math.min(currentMetrics.operatingSystem.systemLoadAverage / 4 * 100, 100)} className="flex-1 mr-2" />
              <Badge className={currentMetrics.operatingSystem.systemLoadAverage > 2 ? 'bg-red-100 text-red-800 border-red-200' : currentMetrics.operatingSystem.systemLoadAverage > 1 ? 'bg-yellow-100 text-yellow-800 border-yellow-200' : 'bg-green-100 text-green-800 border-green-200'}>
                {currentMetrics.operatingSystem.systemLoadAverage > 2 ? '高' : currentMetrics.operatingSystem.systemLoadAverage > 1 ? '中' : '低'}
              </Badge>
            </div>
            <p className="text-xs text-muted-foreground mt-2">
              CPU核心数: {currentMetrics.operatingSystem.availableProcessors}
            </p>
          </CardContent>
        </Card>
      </div>

      {/* 主要内容区域 */}
      <Tabs defaultValue="overview" className="space-y-4">
        <TabsList>
          <TabsTrigger value="overview">概览</TabsTrigger>
          <TabsTrigger value="memory">内存监控</TabsTrigger>
          <TabsTrigger value="threads">线程监控</TabsTrigger>
          <TabsTrigger value="gc">垃圾回收</TabsTrigger>
          <TabsTrigger value="classes">类加载</TabsTrigger>
          <TabsTrigger value="system">系统信息</TabsTrigger>
        </TabsList>

        {/* 概览 */}
        <TabsContent value="overview" className="space-y-4">
          <div className="grid gap-4 md:grid-cols-2">
            <Card>
              <CardHeader>
                <CardTitle>JVM性能趋势</CardTitle>
                <CardDescription>内存使用、线程数和系统负载变化</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="h-80">
                  <StatusChart
                    type="line"
                    data={metrics.map(m => ({
                      time: m.timestamp,
                      heapUsage: (m.memory.heapMemoryUsage.used / m.memory.heapMemoryUsage.max) * 100,
                      threadCount: m.threads.threadCount,
                      systemLoad: m.operatingSystem.systemLoadAverage,
                    }))}
                    dataKey="heapUsage"
                    nameKey="time"
                    colors={["#ef4444", "#3b82f6", "#10b981"]}
                  />
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>JVM基本信息</CardTitle>
                <CardDescription>Java虚拟机基本配置信息</CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <label className="text-sm font-medium">JVM版本</label>
                    <p className="text-lg font-semibold">{currentMetrics.runtime.version}</p>
                  </div>
                  <div>
                    <label className="text-sm font-medium">供应商</label>
                    <p className="text-lg font-semibold">{currentMetrics.runtime.vendor}</p>
                  </div>
                  <div>
                    <label className="text-sm font-medium">运行时间</label>
                    <p className="text-lg font-semibold">{Math.floor(currentMetrics.runtime.uptime / 3600)}小时</p>
                  </div>
                  <div>
                    <label className="text-sm font-medium">可用处理器</label>
                    <p className="text-lg font-semibold">{currentMetrics.runtime.availableProcessors}</p>
                  </div>
                </div>
                <div className="space-y-3">
                  <div className="flex justify-between">
                    <span>启动时间</span>
                    <span>{new Date(currentMetrics.runtime.startTime).toLocaleString()}</span>
                  </div>
                  <div className="flex justify-between">
                    <span>已加载类数</span>
                    <span>{currentMetrics.classLoading.loadedClassCount.toLocaleString()}</span>
                  </div>
                  <div className="flex justify-between">
                    <span>总编译时间</span>
                    <span>{currentMetrics.compilation.totalCompilationTime}ms</span>
                  </div>
                  <div className="flex justify-between">
                    <span>死锁线程数</span>
                    <span className={currentMetrics.threads.deadlockedThreadsCount > 0 ? 'text-red-600' : ''}>
                      {currentMetrics.threads.deadlockedThreadsCount}
                    </span>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        {/* 内存监控 */}
        <TabsContent value="memory" className="space-y-4">
          <div className="grid gap-4 md:grid-cols-2">
            <Card>
              <CardHeader>
                <CardTitle>内存使用趋势</CardTitle>
                <CardDescription>堆内存和非堆内存使用情况</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="h-80">
                  <StatusChart
                    type="line"
                    data={metrics.map(m => ({
                      time: m.timestamp,
                      heapUsed: m.memory.heapMemoryUsage.used / 1024 / 1024 / 1024,
                      heapCommitted: m.memory.heapMemoryUsage.committed / 1024 / 1024 / 1024,
                      nonHeapUsed: m.memory.nonHeapMemoryUsage.used / 1024 / 1024,
                    }))}
                    dataKey="heapUsed"
                    nameKey="time"
                    colors={["#3b82f6", "#10b981", "#f59e0b"]}
                  />
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>内存详细信息</CardTitle>
                <CardDescription>JVM内存使用情况详细分析</CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div>
                  <h4 className="font-medium mb-3">堆内存</h4>
                  <div className="space-y-3">
                    <div className="flex justify-between">
                      <span>已用内存</span>
                      <span>{(currentMetrics.memory.heapMemoryUsage.used / 1024 / 1024 / 1024).toFixed(1)}GB</span>
                    </div>
                    <Progress value={(currentMetrics.memory.heapMemoryUsage.used / currentMetrics.memory.heapMemoryUsage.max) * 100} />
                    
                    <div className="flex justify-between">
                      <span>已提交内存</span>
                      <span>{(currentMetrics.memory.heapMemoryUsage.committed / 1024 / 1024 / 1024).toFixed(1)}GB</span>
                    </div>
                    <Progress value={(currentMetrics.memory.heapMemoryUsage.committed / currentMetrics.memory.heapMemoryUsage.max) * 100} />
                    
                    <div className="flex justify-between">
                      <span>最大内存</span>
                      <span>{(currentMetrics.memory.heapMemoryUsage.max / 1024 / 1024 / 1024).toFixed(1)}GB</span>
                    </div>
                    <Progress value={100} />
                  </div>
                </div>
                
                <div className="pt-4 border-t">
                  <h4 className="font-medium mb-3">非堆内存</h4>
                  <div className="space-y-3">
                    <div className="flex justify-between">
                      <span>已用内存</span>
                      <span>{(currentMetrics.memory.nonHeapMemoryUsage.used / 1024 / 1024).toFixed(0)}MB</span>
                    </div>
                    <Progress value={(currentMetrics.memory.nonHeapMemoryUsage.used / currentMetrics.memory.nonHeapMemoryUsage.max) * 100} />
                    
                    <div className="flex justify-between">
                      <span>已提交内存</span>
                      <span>{(currentMetrics.memory.nonHeapMemoryUsage.committed / 1024 / 1024).toFixed(0)}MB</span>
                    </div>
                    <Progress value={(currentMetrics.memory.nonHeapMemoryUsage.committed / currentMetrics.memory.nonHeapMemoryUsage.max) * 100} />
                  </div>
                </div>

                <div className="pt-4 border-t">
                  <div className="flex justify-between">
                    <span>待终结对象数</span>
                    <span>{currentMetrics.memory.objectPendingFinalizationCount}</span>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        {/* 线程监控 */}
        <TabsContent value="threads" className="space-y-4">
          <div className="grid gap-4 md:grid-cols-2">
            <Card>
              <CardHeader>
                <CardTitle>线程状态趋势</CardTitle>
                <CardDescription>线程数量变化和峰值</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="h-80">
                  <StatusChart
                    type="line"
                    data={metrics.map(m => ({
                      time: m.timestamp,
                      current: m.threads.threadCount,
                      peak: m.threads.peakThreadCount,
                      daemon: m.threads.daemonThreadCount,
                    }))}
                    dataKey="current"
                    nameKey="time"
                    colors={["#3b82f6", "#ef4444", "#10b981"]}
                  />
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>线程统计</CardTitle>
                <CardDescription>线程池详细统计信息</CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <label className="text-sm font-medium">当前线程数</label>
                    <p className="text-2xl font-bold">{currentMetrics.threads.threadCount}</p>
                  </div>
                  <div>
                    <label className="text-sm font-medium">峰值线程数</label>
                    <p className="text-2xl font-bold">{currentMetrics.threads.peakThreadCount}</p>
                  </div>
                  <div>
                    <label className="text-sm font-medium">守护线程数</label>
                    <p className="text-2xl font-bold">{currentMetrics.threads.daemonThreadCount}</p>
                  </div>
                  <div>
                    <label className="text-sm font-medium">总启动线程数</label>
                    <p className="text-2xl font-bold">{currentMetrics.threads.totalStartedThreadCount}</p>
                  </div>
                </div>
                <div className="space-y-3">
                  <div className="flex justify-between">
                    <span>死锁线程数</span>
                    <span className={currentMetrics.threads.deadlockedThreadsCount > 0 ? 'text-red-600 font-semibold' : ''}>
                      {currentMetrics.threads.deadlockedThreadsCount}
                    </span>
                  </div>
                  <Progress value={Math.min(currentMetrics.threads.deadlockedThreadsCount / 10 * 100, 100)} />
                  
                  <div className="flex justify-between">
                    <span>用户线程数</span>
                    <span>{currentMetrics.threads.threadCount - currentMetrics.threads.daemonThreadCount}</span>
                  </div>
                  <Progress value={((currentMetrics.threads.threadCount - currentMetrics.threads.daemonThreadCount) / currentMetrics.threads.threadCount) * 100} />
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        {/* 垃圾回收 */}
        <TabsContent value="gc" className="space-y-4">
          <div className="grid gap-4 md:grid-cols-2">
            <Card>
              <CardHeader>
                <CardTitle>垃圾回收统计</CardTitle>
                <CardDescription>各代垃圾回收器性能统计</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="h-80">
                  <StatusChart
                    type="bar"
                    data={currentMetrics.garbageCollection.collectors.map(collector => ({
                      name: collector.name,
                      count: collector.collectionCount,
                      time: collector.collectionTime,
                    }))}
                    dataKey="count"
                    nameKey="name"
                    colors={["#3b82f6", "#ef4444"]}
                  />
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>垃圾回收器详情</CardTitle>
                <CardDescription>各代垃圾回收器详细信息</CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                {currentMetrics.garbageCollection.collectors.map((collector, index) => (
                  <div key={collector.name} className="space-y-3">
                    <div className="flex items-center justify-between">
                      <h4 className="font-medium">{collector.name}</h4>
                      <Badge className="bg-blue-100 text-blue-800 border-blue-200">
                        {index === 0 ? '年轻代' : '老年代'}
                      </Badge>
                    </div>
                    <div className="grid grid-cols-2 gap-4">
                      <div>
                        <label className="text-sm font-medium">回收次数</label>
                        <p className="text-lg font-semibold">{collector.collectionCount}</p>
                      </div>
                      <div>
                        <label className="text-sm font-medium">回收时间</label>
                        <p className="text-lg font-semibold">{collector.collectionTime}ms</p>
                      </div>
                    </div>
                    <div className="space-y-2">
                      <div className="flex justify-between">
                        <span>平均回收时间</span>
                        <span>{(collector.collectionTime / collector.collectionCount).toFixed(2)}ms</span>
                      </div>
                      <Progress value={Math.min((collector.collectionTime / 100000) * 100, 100)} />
                    </div>
                  </div>
                ))}
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        {/* 类加载 */}
        <TabsContent value="classes" className="space-y-4">
          <div className="grid gap-4 md:grid-cols-2">
            <Card>
              <CardHeader>
                <CardTitle>类加载统计</CardTitle>
                <CardDescription>类加载和卸载统计信息</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="h-80">
                  <StatusChart
                    type="line"
                    data={metrics.map(m => ({
                      time: m.timestamp,
                      loaded: m.classLoading.loadedClassCount,
                      total: m.classLoading.totalLoadedClassCount,
                      unloaded: m.classLoading.unloadedClassCount,
                    }))}
                    dataKey="loaded"
                    nameKey="time"
                    colors={["#3b82f6", "#10b981", "#f59e0b"]}
                  />
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>类加载详情</CardTitle>
                <CardDescription>类加载器详细统计</CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <label className="text-sm font-medium">当前加载类数</label>
                    <p className="text-2xl font-bold">{currentMetrics.classLoading.loadedClassCount.toLocaleString()}</p>
                  </div>
                  <div>
                    <label className="text-sm font-medium">总加载类数</label>
                    <p className="text-2xl font-bold">{currentMetrics.classLoading.totalLoadedClassCount.toLocaleString()}</p>
                  </div>
                  <div>
                    <label className="text-sm font-medium">已卸载类数</label>
                    <p className="text-2xl font-bold">{currentMetrics.classLoading.unloadedClassCount.toLocaleString()}</p>
                  </div>
                  <div>
                    <label className="text-sm font-medium">净增长类数</label>
                    <p className="text-2xl font-bold">{(currentMetrics.classLoading.totalLoadedClassCount - currentMetrics.classLoading.unloadedClassCount).toLocaleString()}</p>
                  </div>
                </div>
                <div className="space-y-3">
                  <div className="flex justify-between">
                    <span>类加载率</span>
                    <span>{((currentMetrics.classLoading.loadedClassCount / currentMetrics.classLoading.totalLoadedClassCount) * 100).toFixed(1)}%</span>
                  </div>
                  <Progress value={(currentMetrics.classLoading.loadedClassCount / currentMetrics.classLoading.totalLoadedClassCount) * 100} />
                  
                  <div className="flex justify-between">
                    <span>类卸载率</span>
                    <span>{((currentMetrics.classLoading.unloadedClassCount / currentMetrics.classLoading.totalLoadedClassCount) * 100).toFixed(1)}%</span>
                  </div>
                  <Progress value={(currentMetrics.classLoading.unloadedClassCount / currentMetrics.classLoading.totalLoadedClassCount) * 100} />
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        {/* 系统信息 */}
        <TabsContent value="system" className="space-y-4">
          <div className="grid gap-4 md:grid-cols-2">
            <Card>
              <CardHeader>
                <CardTitle>系统资源使用</CardTitle>
                <CardDescription>操作系统资源使用情况</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="h-80">
                  <StatusChart
                    type="line"
                    data={metrics.map(m => ({
                      time: m.timestamp,
                      systemLoad: m.operatingSystem.systemLoadAverage,
                      memoryUsage: ((m.operatingSystem.totalPhysicalMemorySize - m.operatingSystem.freePhysicalMemorySize) / m.operatingSystem.totalPhysicalMemorySize) * 100,
                      swapUsage: ((m.operatingSystem.totalSwapSpaceSize - m.operatingSystem.freeSwapSpaceSize) / m.operatingSystem.totalSwapSpaceSize) * 100,
                    }))}
                    dataKey="systemLoad"
                    nameKey="time"
                    colors={["#3b82f6", "#ef4444", "#f59e0b"]}
                  />
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>系统详细信息</CardTitle>
                <CardDescription>操作系统和硬件信息</CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <label className="text-sm font-medium">操作系统</label>
                    <p className="text-lg font-semibold">{currentMetrics.operatingSystem.name}</p>
                  </div>
                  <div>
                    <label className="text-sm font-medium">架构</label>
                    <p className="text-lg font-semibold">{currentMetrics.operatingSystem.arch}</p>
                  </div>
                  <div>
                    <label className="text-sm font-medium">版本</label>
                    <p className="text-lg font-semibold">{currentMetrics.operatingSystem.version}</p>
                  </div>
                  <div>
                    <label className="text-sm font-medium">处理器数</label>
                    <p className="text-lg font-semibold">{currentMetrics.operatingSystem.availableProcessors}</p>
                  </div>
                </div>
                <div className="space-y-3">
                  <div className="flex justify-between">
                    <span>物理内存使用</span>
                    <span>{systemMemoryUsage.toFixed(1)}%</span>
                  </div>
                  <Progress value={systemMemoryUsage} />
                  
                  <div className="flex justify-between">
                    <span>交换空间使用</span>
                    <span>{((currentMetrics.operatingSystem.totalSwapSpaceSize - currentMetrics.operatingSystem.freeSwapSpaceSize) / currentMetrics.operatingSystem.totalSwapSpaceSize * 100).toFixed(1)}%</span>
                  </div>
                  <Progress value={(currentMetrics.operatingSystem.totalSwapSpaceSize - currentMetrics.operatingSystem.freeSwapSpaceSize) / currentMetrics.operatingSystem.totalSwapSpaceSize * 100} />
                  
                  <div className="flex justify-between">
                    <span>虚拟内存</span>
                    <span>{(currentMetrics.operatingSystem.committedVirtualMemorySize / 1024 / 1024 / 1024).toFixed(1)}GB</span>
                  </div>
                  <Progress value={Math.min((currentMetrics.operatingSystem.committedVirtualMemorySize / 1024 / 1024 / 1024 / 16) * 100, 100)} />
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>
      </Tabs>
    </div>
  );
} 