@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 222.2 84% 4.9%;

    --card: 0 0% 100%;
    --card-foreground: 222.2 84% 4.9%;

    --popover: 0 0% 100%;
    --popover-foreground: 222.2 84% 4.9%;

    --primary: 210 100% 50%;
    --primary-foreground: 210 40% 98%;

    --secondary: 262 83% 58%;
    --secondary-foreground: 210 40% 98%;

    --muted: 210 40% 96.1%;
    --muted-foreground: 215.4 16.3% 46.9%;

    --accent: 262 83% 58%;
    --accent-foreground: 210 40% 98%;

    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;

    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;
    --ring: 222.2 84% 4.9%;

    --radius: 0.5rem;

    --sidebar-background: 210 50% 98%;
    --sidebar-foreground: 240 5.3% 26.1%;
    --sidebar-primary: 210 100% 50%;
    --sidebar-primary-foreground: 0 0% 98%;
    --sidebar-accent: 262 83% 95%;
    --sidebar-accent-foreground: 240 5.9% 10%;
    --sidebar-border: 220 13% 91%;
    --sidebar-ring: 217.2 91.2% 59.8%;
  }

  .dark {
    --background: 222.2 84% 4.9%;
    --foreground: 210 40% 98%;

    --card: 222.2 84% 4.9%;
    --card-foreground: 210 40% 98%;

    --popover: 222.2 84% 4.9%;
    --popover-foreground: 210 40% 98%;

    --primary: 210 100% 50%;
    --primary-foreground: 0 0% 100%;

    --secondary: 262 83% 58%;
    --secondary-foreground: 0 0% 100%;

    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;

    --accent: 262 83% 58%;
    --accent-foreground: 0 0% 100%;

    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;

    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 212.7 26.8% 83.9%;
    --sidebar-background: 240 5.9% 10%;
    --sidebar-foreground: 240 4.8% 95.9%;
    --sidebar-primary: 210 100% 50%;
    --sidebar-primary-foreground: 0 0% 100%;
    --sidebar-accent: 262 83% 25%;
    --sidebar-accent-foreground: 240 4.8% 95.9%;
    --sidebar-border: 240 3.7% 15.9%;
    --sidebar-ring: 217.2 91.2% 59.8%;
  }

  /* 清新科技风格主题 */
  .blue-tech {
    --background: 0 0% 100%;
    --foreground: 222.2 84% 4.9%;
    --card: 0 0% 100%;
    --card-foreground: 222.2 84% 4.9%;
    --popover: 0 0% 100%;
    --popover-foreground: 222.2 84% 4.9%;
    --primary: 210 100% 60%;
    --primary-foreground: 0 0% 100%;
    --secondary: 45 100% 60%;
    --secondary-foreground: 0 0% 100%;
    --muted: 210 40% 96.1%;
    --muted-foreground: 215.4 16.3% 46.9%;
    --accent: 210 100% 60%;
    --accent-foreground: 0 0% 100%;
    --destructive: 0 84% 60%;
    --destructive-foreground: 0 0% 100%;
    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;
    --ring: 210 100% 60%;
    --sidebar-background: 210 50% 98%;
    --sidebar-foreground: 240 5.3% 26.1%;
    --sidebar-primary: 210 100% 60%;
    --sidebar-primary-foreground: 0 0% 100%;
    --sidebar-accent: 45 100% 60%;
    --sidebar-accent-foreground: 0 0% 100%;
    --sidebar-border: 220 13% 91%;
    --sidebar-ring: 210 100% 60%;
  }
}

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply bg-background text-foreground;
  }

  /* 清新科技风格全局样式 */
  .blue-tech-theme {
    background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 50%, #e2e8f0 100%);
    color: #334155;
    position: relative;
    overflow-x: hidden;
  }

  .blue-tech-theme::before {
    content: '';
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: 
      radial-gradient(circle at 20% 80%, rgba(59, 130, 246, 0.05) 0%, transparent 50%),
      radial-gradient(circle at 80% 20%, rgba(251, 191, 36, 0.05) 0%, transparent 50%),
      radial-gradient(circle at 40% 40%, rgba(34, 197, 94, 0.03) 0%, transparent 50%);
    pointer-events: none;
    z-index: -1;
  }

  .blue-tech-card {
    background: #ffffff;
    border: 1px solid #e2e8f0;
    /* border-radius: 12px; */
    box-shadow: 
      0 4px 6px -1px rgba(0, 0, 0, 0.1),
      0 2px 4px -1px rgba(0, 0, 0, 0.06);
    position: relative;
    overflow: hidden;
  }

  .blue-tech-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: linear-gradient(90deg, #3b82f6, #fbbf24, #22c55e);
  }

  .blue-tech-glow {
    box-shadow: 
      0 10px 15px -3px rgba(59, 130, 246, 0.1),
      0 4px 6px -2px rgba(59, 130, 246, 0.05);
  }

  .blue-tech-border {
    border: 1px solid #e2e8f0;
    box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1);
  }

  .blue-tech-gradient {
    background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 25%, #1e40af 50%, #1d4ed8 75%, #3b82f6 100%);
    position: relative;
    overflow: hidden;
  }

  .blue-tech-gradient::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(45deg, transparent 30%, rgba(255, 255, 255, 0.1) 50%, transparent 70%);
    animation: shimmer 3s infinite;
  }

  .blue-tech-text-glow {
    text-shadow: 
      0 0 10px rgba(59, 130, 246, 0.3),
      0 0 20px rgba(59, 130, 246, 0.1);
  }

  .blue-tech-button {
    background: linear-gradient(145deg, #3b82f6 0%, #1d4ed8 100%);
    border: 1px solid #3b82f6;
    color: white;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
    border-radius: 8px;
  }

  .blue-tech-button::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s ease;
  }

  .blue-tech-button:hover::before {
    left: 100%;
  }

  .blue-tech-button:hover {
    background: linear-gradient(145deg, #1d4ed8 0%, #1e40af 100%);
    box-shadow: 
      0 10px 15px -3px rgba(59, 130, 246, 0.3),
      0 4px 6px -2px rgba(59, 130, 246, 0.2);
    transform: translateY(-2px);
  }

  .blue-tech-button-yellow {
    background: linear-gradient(145deg, #fbbf24 0%, #f59e0b 100%);
    border: 1px solid #fbbf24;
    color: white;
  }

  .blue-tech-button-yellow:hover {
    background: linear-gradient(145deg, #f59e0b 0%, #d97706 100%);
    box-shadow: 
      0 10px 15px -3px rgba(251, 191, 36, 0.3),
      0 4px 6px -2px rgba(251, 191, 36, 0.2);
  }

  .blue-tech-sidebar {
    background: linear-gradient(180deg, #4e73df 0%, #3a5fcd 25%, #2d4a9e 50%, #1e3a8a 75%, #1e40af 100%);
    border-right: 2px solid #3a5fcd;
    box-shadow: 
      2px 0 15px rgba(78, 115, 223, 0.3),
      inset -1px 0 0 rgba(255, 255, 255, 0.1);
    position: relative;
  }

  .blue-tech-sidebar::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: 
      linear-gradient(90deg, transparent 95%, rgba(255, 255, 255, 0.1) 100%),
      linear-gradient(180deg, transparent 0%, rgba(255, 255, 255, 0.05) 50%, transparent 100%);
    pointer-events: none;
    z-index: 0;
  }

  .blue-tech-sidebar > * {
    position: relative;
    z-index: 1;
  }

  .blue-tech-nav-item {
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 8px;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
    margin-bottom: 6px;
    backdrop-filter: blur(10px);
  }

  .blue-tech-nav-item::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 3px;
    height: 100%;
    background: linear-gradient(180deg, #ffffff, #e0e7ff);
    transform: scaleY(0);
    transition: transform 0.3s ease;
  }

  .blue-tech-nav-item::after {
    content: '';
    position: absolute;
    top: 0;
    right: 0;
    width: 0;
    height: 0;
    border-style: solid;
    border-width: 0 6px 6px 0;
    border-color: transparent rgba(255, 255, 255, 0.3) transparent transparent;
    transition: all 0.3s ease;
  }

  .blue-tech-nav-item:hover::before,
  .blue-tech-nav-item.active::before {
    transform: scaleY(1);
  }

  .blue-tech-nav-item:hover::after {
    border-color: transparent rgba(255, 255, 255, 0.6) transparent transparent;
  }

  .blue-tech-nav-item:hover {
    background: rgba(255, 255, 255, 0.2);
    border-color: rgba(255, 255, 255, 0.4);
    box-shadow: 
      0 4px 12px rgba(255, 255, 255, 0.15),
      0 2px 6px rgba(255, 255, 255, 0.1);
    transform: translateX(5px);
  }

  .blue-tech-nav-item.active {
    background: linear-gradient(145deg, rgba(255, 255, 255, 0.25) 0%, rgba(224, 231, 255, 0.3) 100%);
    border-color: rgba(255, 255, 255, 0.6);
    box-shadow: 
      0 4px 12px rgba(255, 255, 255, 0.2),
      0 2px 6px rgba(255, 255, 255, 0.15);
  }

  .blue-tech-nav-item.active::after {
    border-color: transparent #ffffff transparent transparent;
  }

  /* 图标样式 */
  .blue-tech-nav-item svg {
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.2) 0%, rgba(224, 231, 255, 0.3) 100%);
    border-radius: 6px;
    padding: 3px;
    transition: all 0.3s ease;
    color: #ffffff;
  }

  .blue-tech-nav-item:hover svg {
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.3) 0%, rgba(224, 231, 255, 0.4) 100%);
    transform: scale(1.1);
  }

  .blue-tech-nav-item.active svg {
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.4) 0%, rgba(224, 231, 255, 0.5) 100%);
    color: #ffffff;
  }

  /* 动画效果 */
  @keyframes shimmer {
    0% { transform: translateX(-100%); }
    100% { transform: translateX(100%); }
  }

  @keyframes pulse-glow {
    0%, 100% { box-shadow: 0 0 20px rgba(59, 130, 246, 0.1); }
    50% { box-shadow: 0 0 30px rgba(59, 130, 246, 0.2); }
  }

  @keyframes float {
    0%, 100% { transform: translateY(0px); }
    50% { transform: translateY(-10px); }
  }

  .pulse-glow {
    animation: pulse-glow 2s infinite;
  }

  .float {
    animation: float 3s ease-in-out infinite;
  }

  /* 滚动条样式 */
  ::-webkit-scrollbar {
    width: 8px;
    height: 8px;
  }

  ::-webkit-scrollbar-track {
    background: #f1f5f9;
    border-radius: 4px;
  }

  ::-webkit-scrollbar-thumb {
    background: linear-gradient(180deg, #3b82f6, #1d4ed8);
    border-radius: 4px;
    border: 1px solid #e2e8f0;
  }

  ::-webkit-scrollbar-thumb:hover {
    background: linear-gradient(180deg, #60a5fa, #3b82f6);
  }
}
