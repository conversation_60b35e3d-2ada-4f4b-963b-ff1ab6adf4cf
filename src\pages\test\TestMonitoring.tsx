import { useState, useEffect } from "react";
import {
  <PERSON>,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Progress } from "@/components/ui/progress";
import { Tabs, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import {
  Monitor,
  Activity,
  Clock,
  CheckCircle,
  AlertCircle,
  Play,
  Package,
  ArrowUp,
  ArrowDown,
  Battery,
  RefreshCw,
  Eye,
  Network,
  Bot,
  Truck,
  TestTube2,
  Zap,
  Pause,
  StopCircle,
  RotateCcw,
} from "lucide-react";

interface TaskStep {
  stepNumber: number;
  stepName: string;
  description: string;
  status: "completed" | "running" | "pending" | "failed";
  startTime?: string;
  endTime?: string;
  duration?: number;
  details?: string;
}

interface Task {
  id: string;
  type: "outbound_test" | "inbound_storage" | "vehicle_charging";
  productId?: string;
  vehicleId?: string;
  startTime: string;
  currentStep: number;
  totalSteps: number;
  status: "running" | "completed" | "failed" | "paused";
  progress: number;
  estimatedCompletion: string;
  steps: TaskStep[];
}

interface TestBench {
  id: string;
  name: string;
  productId?: string;
  testType?: string;
  progress: number;
  status: "idle" | "running" | "completed" | "error" | "maintenance";
  startTime?: string;
  estimatedCompletion?: string;
  remainingTime?: string;
  temperature: number;
  humidity: number;
  voltage: number;
  current: number;
}

export default function TestMonitoring() {
  const [selectedTask, setSelectedTask] = useState<Task | null>(null);
  const [autoRefresh, setAutoRefresh] = useState(true);
  const [selectedTestBench, setSelectedTestBench] = useState<TestBench | null>(null);

  // 12个测试台的模拟数据
  const [testBenches] = useState<TestBench[]>([
    {
      id: "TB-01",
      name: "测试台-01",
      productId: "A-2024-001",
      testType: "月稳测试",
      progress: 65,
      status: "running",
      startTime: "2024-01-15 10:30:00",
      estimatedCompletion: "2024-01-15 16:30:00",
      remainingTime: "2小时35分",
      temperature: 25.5,
      humidity: 45.2,
      voltage: 220.1,
      current: 1.8,
    },
    {
      id: "TB-02",
      name: "测试台-02",
      productId: "A-2024-002",
      testType: "月稳测试",
      progress: 25,
      status: "running",
      startTime: "2024-01-15 11:15:00",
      estimatedCompletion: "2024-01-15 17:15:00",
      remainingTime: "6小时12分",
      temperature: 24.8,
      humidity: 46.1,
      voltage: 219.8,
      current: 1.6,
    },
    {
      id: "TB-03",
      name: "测试台-03",
      productId: "A-2024-003",
      testType: "临时测试",
      progress: 85,
      status: "running",
      startTime: "2024-01-15 09:45:00",
      estimatedCompletion: "2024-01-15 15:45:00",
      remainingTime: "1小时20分",
      temperature: 26.2,
      humidity: 44.8,
      voltage: 220.5,
      current: 2.1,
    },
    {
      id: "TB-04",
      name: "测试台-04",
      status: "idle",
      progress: 0,
      temperature: 23.5,
      humidity: 48.3,
      voltage: 220.0,
      current: 0.0,
    },
    {
      id: "TB-05",
      name: "测试台-05",
      productId: "A-2024-004",
      testType: "月稳测试",
      progress: 45,
      status: "running",
      startTime: "2024-01-15 12:00:00",
      estimatedCompletion: "2024-01-15 18:00:00",
      remainingTime: "4小时30分",
      temperature: 25.1,
      humidity: 45.9,
      voltage: 219.9,
      current: 1.9,
    },
    {
      id: "TB-06",
      name: "测试台-06",
      status: "maintenance",
      progress: 0,
      temperature: 22.0,
      humidity: 50.0,
      voltage: 0.0,
      current: 0.0,
    },
    {
      id: "TB-07",
      name: "测试台-07",
      productId: "A-2024-005",
      testType: "临时测试",
      progress: 90,
      status: "running",
      startTime: "2024-01-15 08:30:00",
      estimatedCompletion: "2024-01-15 14:30:00",
      remainingTime: "30分钟",
      temperature: 25.8,
      humidity: 45.5,
      voltage: 220.3,
      current: 2.0,
    },
    {
      id: "TB-08",
      name: "测试台-08",
      productId: "A-2024-006",
      testType: "月稳测试",
      progress: 15,
      status: "running",
      startTime: "2024-01-15 13:20:00",
      estimatedCompletion: "2024-01-15 19:20:00",
      remainingTime: "5小时45分",
      temperature: 24.9,
      humidity: 46.7,
      voltage: 219.7,
      current: 1.7,
    },
    {
      id: "TB-09",
      name: "测试台-09",
      status: "error",
      progress: 0,
      temperature: 28.5,
      humidity: 42.1,
      voltage: 225.0,
      current: 0.0,
    },
    {
      id: "TB-10",
      name: "测试台-10",
      productId: "A-2024-007",
      testType: "月稳测试",
      progress: 70,
      status: "running",
      startTime: "2024-01-15 10:00:00",
      estimatedCompletion: "2024-01-15 16:00:00",
      remainingTime: "2小时10分",
      temperature: 25.3,
      humidity: 45.8,
      voltage: 220.2,
      current: 1.9,
    },
    {
      id: "TB-11",
      name: "测试台-11",
      status: "idle",
      progress: 0,
      temperature: 23.8,
      humidity: 47.5,
      voltage: 220.0,
      current: 0.0,
    },
    {
      id: "TB-12",
      name: "测试台-12",
      productId: "A-2024-008",
      testType: "临时测试",
      progress: 55,
      status: "running",
      startTime: "2024-01-15 11:45:00",
      estimatedCompletion: "2024-01-15 17:45:00",
      remainingTime: "3小时25分",
      temperature: 25.0,
      humidity: 46.0,
      voltage: 219.8,
      current: 1.8,
    },
  ]);

  // 模拟当前执行的任务数据
  const [currentTasks] = useState<Task[]>([
    {
      id: "TASK-001",
      type: "outbound_test",
      productId: "P12345",
      startTime: "2024-01-10 14:25:30",
      currentStep: 3,
      totalSteps: 6,
      status: "running",
      progress: 50,
      estimatedCompletion: "2024-01-10 14:27:45",
      steps: [
        {
          stepNumber: 1,
          stepName: "通知MES产品出库",
          description: "ICS通知MES产品P12345开始出库测试",
          status: "completed",
          startTime: "14:25:30",
          endTime: "14:25:32",
          duration: 2,
          details: "MES响应成功，确认开始出库流程",
        },
        {
          stepNumber: 2,
          stepName: "桁架执行出库操作",
          description: "桁架系统将产品P12345从货位A-01搬运到接驳台-02",
          status: "completed",
          startTime: "14:25:32",
          endTime: "14:26:18",
          duration: 46,
          details: "桁架TRUSS-01执行完成，产品已到达接驳台-02",
        },
        {
          stepNumber: 3,
          stepName: "AMR小车执行出库操作",
          description: "AMR-03将产品P12345从接驳台-02搬运到测试台-01",
          status: "running",
          startTime: "14:26:18",
          details: "AMR-03正在执行搬运任务，当前位置：走廊B",
        },
        {
          stepNumber: 4,
          stepName: "测试台机械臂连接电缆",
          description: "测试台用机械臂连接测试电脑和产品之间的电缆线",
          status: "pending",
          details: "等待AMR搬运完成",
        },
        {
          stepNumber: 5,
          stepName: "ICS通知MES产品就绪",
          description: "ICS通知MES产品就绪",
          status: "pending",
          details: "待机械臂连接完成",
        },
        {
          stepNumber: 6,
          stepName: "MES通知产品开始测试",
          description: "MES通知产品开始测试",
          status: "pending",
          details: "待产品就绪确认",
        },
      ],
    },
    {
      id: "TASK-002",
      type: "inbound_storage",
      productId: "P67890",
      startTime: "2024-01-10 14:22:10",
      currentStep: 4,
      totalSteps: 6,
      status: "running",
      progress: 67,
      estimatedCompletion: "2024-01-10 14:25:30",
      steps: [
        {
          stepNumber: 1,
          stepName: "入库通知",
          description: "MES通知ICS产品P67890测试完毕",
          status: "completed",
          startTime: "14:22:10",
          endTime: "14:22:11",
          duration: 1,
          details: "测试结果：PASS，测试耗时：23分钟",
        },
        {
          stepNumber: 2,
          stepName: "告知MES入库",
          description: "ICS告知MES开始执行入库操作",
          status: "completed",
          startTime: "14:22:11",
          endTime: "14:22:12",
          duration: 1,
          details: "MES确认开始入库流程",
        },
        {
          stepNumber: 3,
          stepName: "测试系统断开电缆连接",
          description: "测试系统断开测试电脑与产品之间的电缆",
          status: "completed",
          startTime: "14:22:12",
          endTime: "14:22:18",
          duration: 6,
          details: "测试台-05机械臂已断开连接",
        },
        {
          stepNumber: 4,
          stepName: "AMR执行入库",
          description: "AMR车将产品P67890从测试台-05搬运到接驳台-03",
          status: "running",
          startTime: "14:22:18",
          details: "AMR-01正在执行搬运，预计2分钟到达",
        },
        {
          stepNumber: 5,
          stepName: "桁架入库",
          description: "桁架将产品P67890从接驳台-03搬运回货位B-15",
          status: "pending",
          details: "等待AMR搬运完成",
        },
        {
          stepNumber: 6,
          stepName: "ICS通知MES入库执行完毕",
          description: "ICS通知MES入库执行完毕",
          status: "pending",
          details: "待桁架入库完成",
        },
      ],
    },
    {
      id: "TASK-003",
      type: "vehicle_charging",
      vehicleId: "AMR-04",
      startTime: "2024-01-10 14:20:05",
      currentStep: 4,
      totalSteps: 5,
      status: "running",
      progress: 80,
      estimatedCompletion: "2024-01-10 14:32:00",
      steps: [
        {
          stepNumber: 1,
          stepName: "WCS设置车辆不可用",
          description: "WCS根据AMR-04电量将车状态改为不可用",
          status: "completed",
          startTime: "14:20:05",
          endTime: "14:20:05",
          duration: 0.5,
          details: "电量18%低于阈值20%，状态已更新",
        },
        {
          stepNumber: 2,
          stepName: "WCS调度AMR回待命点",
          description: "WCS调度AMR-04回到待命点",
          status: "completed",
          startTime: "14:20:05",
          endTime: "14:20:38",
          duration: 33,
          details: "从测试台-07到待命点-04，路径优化完成",
        },
        {
          stepNumber: 3,
          stepName: "WCS调度AMR到充电桩",
          description: "WCS调度AMR-04从待命点到充电桩-02进行充电",
          status: "completed",
          startTime: "14:20:38",
          endTime: "14:20:57",
          duration: 19,
          details: "已到达充电桩-02，开始充电",
        },
        {
          stepNumber: 4,
          stepName: "AMR充电完成自动返回",
          description: "AMR-04充电至100%后自动返回待命点",
          status: "running",
          startTime: "14:20:57",
          details: "当前电量85%，预计还需12分钟",
        },
        {
          stepNumber: 5,
          stepName: "AMR通知WCS充电完成",
          description: "AMR通知WCS充电完成，状态改为可用",
          status: "pending",
          details: "等待充电完成",
        },
      ],
    },
    {
      id: "TASK-004",
      type: "outbound_test",
      productId: "P11111",
      startTime: "2024-01-10 14:28:15",
      currentStep: 1,
      totalSteps: 6,
      status: "running",
      progress: 17,
      estimatedCompletion: "2024-01-10 14:30:45",
      steps: [
        {
          stepNumber: 1,
          stepName: "通知MES产品出库",
          description: "ICS通知MES产品P11111开始出库测试",
          status: "running",
          startTime: "14:28:15",
          details: "正在等待MES系统响应",
        },
        {
          stepNumber: 2,
          stepName: "桁架执行出库操作",
          description: "桁架系统将产品从货位搬运到接驳台",
          status: "pending",
          details: "等待MES确认",
        },
        {
          stepNumber: 3,
          stepName: "AMR小车执行出库操作",
          description: "AMR将产品从接驳台搬运到测试台",
          status: "pending",
          details: "待桁架操作完成",
        },
        {
          stepNumber: 4,
          stepName: "测试台机械臂连接电缆",
          description: "测试台用机械臂连接测试电脑和产品之间的电缆线",
          status: "pending",
          details: "待AMR搬运完成",
        },
        {
          stepNumber: 5,
          stepName: "ICS通知MES产品就绪",
          description: "ICS通知MES产品就绪",
          status: "pending",
          details: "待机械臂连接完成",
        },
        {
          stepNumber: 6,
          stepName: "MES通知产品开始测试",
          description: "MES通知产品开始测试",
          status: "pending",
          details: "待产品就绪确认",
        },
      ],
    },
  ]);

  // 静态MES调度记录数据
  const mesRecords = [
    { id: "MES-20240101-001", productId: "A-2024-001", orderNo: "WO-1001", time: "2024-01-15 08:30:00", content: "月稳测试排产", status: "已下发" },
    { id: "MES-20240101-002", productId: "A-2024-002", orderNo: "WO-1002", time: "2024-01-15 09:00:00", content: "性能测试排产", status: "已下发" },
    { id: "MES-20240101-003", productId: "A-2024-003", orderNo: "WO-1003", time: "2024-01-15 09:30:00", content: "老化测试排产", status: "已下发" },
  ];

  const getTaskTypeInfo = (type: string) => {
    const typeConfig = {
      outbound_test: {
        label: "产品出库测试",
        icon: ArrowUp,
        color: "text-blue-600",
        bgColor: "bg-blue-50",
        borderColor: "border-blue-200",
      },
      inbound_storage: {
        label: "产品测试完毕入库",
        icon: ArrowDown,
        color: "text-green-600",
        bgColor: "bg-green-50",
        borderColor: "border-green-200",
      },
      vehicle_charging: {
        label: "车辆充电",
        icon: Battery,
        color: "text-orange-600",
        bgColor: "bg-orange-50",
        borderColor: "border-orange-200",
      },
    };

    return typeConfig[type as keyof typeof typeConfig];
  };

  const getStatusBadge = (status: string) => {
    const statusConfig = {
      running: {
        label: "执行中",
        className: "bg-blue-100 text-blue-800 border-blue-200",
        icon: Play,
      },
      completed: {
        label: "已完成",
        className: "bg-green-100 text-green-800 border-green-200",
        icon: CheckCircle,
      },
      failed: {
        label: "执行失败",
        className: "bg-red-100 text-red-800 border-red-200",
        icon: AlertCircle,
      },
      paused: {
        label: "已暂停",
        className: "bg-yellow-100 text-yellow-800 border-yellow-200",
        icon: Pause,
      },
    };

    const config = statusConfig[status as keyof typeof statusConfig];
    const IconComponent = config.icon;
    return (
      <Badge className={`${config.className} border flex items-center gap-1`}>
        <IconComponent className="h-3 w-3" />
        {config.label}
      </Badge>
    );
  };

  const getStepStatusBadge = (status: string) => {
    const statusConfig = {
      completed: {
        label: "已完成",
        className: "bg-green-100 text-green-800 border-green-200",
        icon: CheckCircle,
      },
      running: {
        label: "执行中",
        className: "bg-blue-100 text-blue-800 border-blue-200",
        icon: Activity,
      },
      pending: {
        label: "等待中",
        className: "bg-gray-100 text-gray-600 border-gray-200",
        icon: Clock,
      },
      failed: {
        label: "失败",
        className: "bg-red-100 text-red-800 border-red-200",
        icon: AlertCircle,
      },
    };

    const config = statusConfig[status as keyof typeof statusConfig];
    const IconComponent = config.icon;
    return (
      <Badge className={`${config.className} border flex items-center gap-1`}>
        <IconComponent className="h-3 w-3" />
        {config.label}
      </Badge>
    );
  };

  const getStepIcon = (type: string, stepNumber: number) => {
    if (type === "outbound_test") {
      const icons = [Network, Bot, Truck, TestTube2, Network, Activity];
      return icons[stepNumber - 1] || Activity;
    } else if (type === "inbound_storage") {
      const icons = [Network, Network, TestTube2, Truck, Bot, Network];
      return icons[stepNumber - 1] || Activity;
    } else if (type === "vehicle_charging") {
      const icons = [Network, Truck, Zap, Battery, Network];
      return icons[stepNumber - 1] || Activity;
    }
    return Activity;
  };

  const getTestBenchStatusBadge = (status: string) => {
    const statusConfig = {
      idle: { label: "空闲", variant: "secondary" as const },
      running: { label: "运行中", variant: "default" as const },
      completed: { label: "已完成", variant: "secondary" as const },
      error: { label: "错误", variant: "destructive" as const },
      maintenance: { label: "维护中", variant: "outline" as const },
    };

    const config = statusConfig[status as keyof typeof statusConfig];
    return <Badge variant={config.variant}>{config.label}</Badge>;
  };

  // 统计数据
  const runningTasks = currentTasks.filter(
    (task) => task.status === "running",
  ).length;
  const completedTasks = currentTasks.filter(
    (task) => task.status === "completed",
  ).length;
  const failedTasks = currentTasks.filter(
    (task) => task.status === "failed",
  ).length;
  const pausedTasks = currentTasks.filter(
    (task) => task.status === "paused",
  ).length;

  // 处理一键还原功能
  const handleRestore = (taskId: string) => {
    console.log(`执行一键还原操作，任务ID: ${taskId}`);
    // 这里可以添加实际的还原逻辑
    alert(`已触发任务 ${taskId} 的一键还原操作`);
  };

  useEffect(() => {
    let interval: NodeJS.Timeout;
    if (autoRefresh) {
      interval = setInterval(() => {
        // 模拟数据更新
        console.log("Auto refreshing monitoring data...");
      }, 3000);
    }
    return () => clearInterval(interval);
  }, [autoRefresh]);

  return (
    <div className="min-h-screen bg-white text-gray-900 p-6">
      <div className="space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div>
            <h2 className="text-3xl font-bold tracking-tight bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
              测试监控
            </h2>
            <p className="text-gray-600 mt-2">
              实时监控正在执行的测试任务及其执行状态
            </p>
          </div>
          <div className="flex items-center space-x-3">
            <Button
              variant={autoRefresh ? "default" : "outline"}
              size="sm"
              onClick={() => setAutoRefresh(!autoRefresh)}
              className="flex items-center gap-2"
            >
              <RefreshCw
                className={`h-4 w-4 ${autoRefresh ? "animate-spin" : ""}`}
              />
              {autoRefresh ? "自动刷新" : "手动刷新"}
            </Button>
          </div>
        </div>

        {/* Statistics Cards */}
        <div className="grid gap-6 md:grid-cols-4">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium text-gray-700">
                执行中任务
              </CardTitle>
              <Play className="h-4 w-4 text-blue-600" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-blue-600">
                {runningTasks}
              </div>
              <p className="text-xs text-gray-500">正在执行的任务</p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium text-gray-700">
                已完成任务
              </CardTitle>
              <CheckCircle className="h-4 w-4 text-green-600" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-green-600">
                {completedTasks}
              </div>
              <p className="text-xs text-gray-500">今日完成数量</p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium text-gray-700">
                暂停任务
              </CardTitle>
              <Pause className="h-4 w-4 text-yellow-600" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-yellow-600">
                {pausedTasks}
              </div>
              <p className="text-xs text-gray-500">需要处理</p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium text-gray-700">
                失败任务
              </CardTitle>
              <AlertCircle className="h-4 w-4 text-red-600" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-red-600">
                {failedTasks}
              </div>
              <p className="text-xs text-gray-500">需要重试</p>
            </CardContent>
          </Card>
        </div>
        

        {/* Tabs for different monitoring views */}
        <Tabs defaultValue="tasks" className="space-y-4">
          <TabsList className="grid w-full grid-cols-2">
            <TabsTrigger value="tasks" className="flex items-center gap-2">
              <Monitor className="h-4 w-4" />
              任务监控
            </TabsTrigger>
            <TabsTrigger value="testbenches" className="flex items-center gap-2">
              <TestTube2 className="h-4 w-4" />
              测试台监控
            </TabsTrigger>
          </TabsList>

          <TabsContent value="tasks">
            {/* Current Tasks List */}
            <Card>
              <CardHeader>
                <CardTitle className="text-gray-800 flex items-center gap-2">
                  <Monitor className="h-5 w-5" />
                  当前执行任务列表
                </CardTitle>
                <CardDescription className="text-gray-600">
                  显示 {currentTasks.length} 个正在执行的任务
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {currentTasks.map((task) => {
                    const typeInfo = getTaskTypeInfo(task.type);
                    const TypeIcon = typeInfo.icon;

                    return (
                      <div
                        key={task.id}
                        className={`p-4 rounded-lg border-2 ${typeInfo.bgColor} ${typeInfo.borderColor} hover:shadow-md transition-all duration-200`}
                      >
                        <div className="flex items-center justify-between mb-3">
                          <div className="flex items-center space-x-3">
                            <div className="w-10 h-10 bg-white rounded-full flex items-center justify-center shadow-sm">
                              <TypeIcon className={`h-5 w-5 ${typeInfo.color}`} />
                            </div>
                            <div>
                              <h3 className={`font-medium ${typeInfo.color}`}>
                                {typeInfo.label}
                              </h3>
                              <p className="text-sm text-gray-600">
                                {task.productId || task.vehicleId} • 任务ID:{" "}
                                {task.id}
                              </p>
                            </div>
                          </div>
                          <div className="flex items-center space-x-3">
                            {getStatusBadge(task.status)}
                            <Button
                              variant="outline"
                              size="sm"
                              onClick={() => handleRestore(task.id)}
                              className="flex items-center gap-2 bg-amber-50 border-amber-200 text-amber-700 hover:bg-amber-100"
                            >
                              <RotateCcw className="h-4 w-4" />
                              一键还原
                            </Button>
                        <Dialog>
                          <DialogTrigger asChild>
                            <Button
                              variant="outline"
                              size="sm"
                              onClick={() => setSelectedTask(task)}
                              className="flex items-center gap-2"
                            >
                              <Eye className="h-4 w-4" />
                              查看详情
                            </Button>
                          </DialogTrigger>
                          <DialogContent className="max-w-4xl max-h-[80vh] overflow-y-auto">
                            <DialogHeader>
                              <DialogTitle className="flex items-center gap-2">
                                <TypeIcon
                                  className={`h-5 w-5 ${typeInfo.color}`}
                                />
                                {typeInfo.label} -{" "}
                                {task.productId || task.vehicleId}
                              </DialogTitle>
                              <DialogDescription>
                                任务开始时间: {task.startTime} • 预计完成:{" "}
                                {task.estimatedCompletion}
                              </DialogDescription>
                            </DialogHeader>

                            {selectedTask && (
                              <div className="mt-6 space-y-4">
                                {/* Progress Overview */}
                                <div className="bg-gray-50 p-4 rounded-lg">
                                  <div className="flex items-center justify-between mb-2">
                                    <span className="text-sm font-medium text-gray-700">
                                      执行进度: {selectedTask.currentStep}/
                                      {selectedTask.totalSteps}
                                    </span>
                                    <span className="text-sm text-gray-600">
                                      {selectedTask.progress}%
                                    </span>
                                  </div>
                                  <Progress
                                    value={selectedTask.progress}
                                    className="h-2"
                                  />
                                </div>

                                {/* Task Actions */}
                                <div className="flex justify-end space-x-3">
                                  <Button
                                    variant="outline"
                                    size="sm"
                                    onClick={() => handleRestore(selectedTask.id)}
                                    className="flex items-center gap-2 bg-amber-50 border-amber-200 text-amber-700 hover:bg-amber-100"
                                  >
                                    <RotateCcw className="h-4 w-4" />
                                    一键还原
                                  </Button>
                                  <Button
                                    variant="outline"
                                    size="sm"
                                    className="flex items-center gap-2"
                                  >
                                    <Pause className="h-4 w-4" />
                                    暂停任务
                                  </Button>
                                </div>

                                {/* Steps List */}
                                <div className="space-y-3">
                                  <h4 className="font-medium text-gray-800">
                                    执行步骤详情:
                                  </h4>
                                  {selectedTask.steps.map((step) => {
                                    const StepIcon = getStepIcon(
                                      selectedTask.type,
                                      step.stepNumber,
                                    );

                                    return (
                                      <div
                                        key={step.stepNumber}
                                        className={`p-4 rounded-lg border-2 ${
                                          step.status === "completed"
                                            ? "bg-green-50 border-green-200"
                                            : step.status === "running"
                                              ? "bg-blue-50 border-blue-200"
                                              : step.status === "failed"
                                                ? "bg-red-50 border-red-200"
                                                : "bg-gray-50 border-gray-200"
                                        }`}
                                      >
                                        <div className="flex items-start space-x-3">
                                          <div className="flex-shrink-0">
                                            <div
                                              className={`w-8 h-8 rounded-full flex items-center justify-center ${
                                                step.status === "completed"
                                                  ? "bg-green-100"
                                                  : step.status === "running"
                                                    ? "bg-blue-100"
                                                    : step.status === "failed"
                                                      ? "bg-red-100"
                                                      : "bg-gray-100"
                                              }`}
                                            >
                                              <StepIcon
                                                className={`h-4 w-4 ${
                                                  step.status === "completed"
                                                    ? "text-green-600"
                                                    : step.status === "running"
                                                      ? "text-blue-600"
                                                      : step.status === "failed"
                                                        ? "text-red-600"
                                                        : "text-gray-600"
                                                }`}
                                              />
                                            </div>
                                          </div>

                                          <div className="flex-1">
                                            <div className="flex items-center justify-between mb-2">
                                              <div>
                                                <h5 className="font-medium text-gray-800">
                                                  步骤 {step.stepNumber}:{" "}
                                                  {step.stepName}
                                                </h5>
                                                <p className="text-sm text-gray-600 mt-1">
                                                  {step.description}
                                                </p>
                                              </div>
                                              {getStepStatusBadge(step.status)}
                                            </div>

                                            <div className="grid grid-cols-2 gap-4 text-sm">
                                              <div>
                                                <span className="font-medium text-gray-700">
                                                  开始时间:
                                                </span>
                                                <span className="ml-1 text-gray-600">
                                                  {step.startTime || "待开始"}
                                                </span>
                                              </div>
                                              <div>
                                                <span className="font-medium text-gray-700">
                                                  执行时长:
                                                </span>
                                                <span className="ml-1 text-gray-600">
                                                  {step.duration
                                                    ? `${step.duration}s`
                                                    : "进行中"}
                                                </span>
                                              </div>
                                            </div>

                                            {step.details && (
                                              <div className="mt-2 p-2 bg-white rounded border">
                                                <span className="text-xs font-medium text-gray-700">
                                                  详细信息:
                                                </span>
                                                <p className="text-xs text-gray-600 mt-1">
                                                  {step.details}
                                                </p>
                                              </div>
                                            )}
                                          </div>
                                        </div>
                                      </div>
                                    );
                                  })}
                                </div>
                              </div>
                            )}
                          </DialogContent>
                        </Dialog>
                      </div>
                    </div>

                    <div className="space-y-2">
                      <div className="flex items-center justify-between text-sm">
                        <span className="text-gray-600">
                          当前步骤: {task.currentStep}/{task.totalSteps}
                        </span>
                        <span className="text-gray-600">
                          开始时间: {task.startTime}
                        </span>
                      </div>
                      <Progress value={task.progress} className="h-2" />
                      <div className="flex items-center justify-between text-xs text-gray-500">
                        <span>预计完成: {task.estimatedCompletion}</span>
                        <span>{task.progress}% 完成</span>
                      </div>
                    </div>
                  </div>
                );
              })}
            </div>
          </CardContent>
        </Card>
          </TabsContent>

          <TabsContent value="testbenches">
            {/* Test Bench Monitoring */}
            <div className="space-y-6">
              <Card>
                <CardHeader>
                  <CardTitle className="text-gray-800 flex items-center gap-2">
                    <TestTube2 className="h-5 w-5" />
                    测试台状态监控
                  </CardTitle>
                  <CardDescription className="text-gray-600">
                    实时监控12个测试台的运行状态和参数
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
                    {testBenches.map((bench) => (
                      <Card
                        key={bench.id}
                        className={`cursor-pointer transition-all duration-200 hover:shadow-md ${
                          bench.status === "running"
                            ? "border-blue-200 bg-blue-50"
                            : bench.status === "error"
                            ? "border-red-200 bg-red-50"
                            : bench.status === "maintenance"
                            ? "border-yellow-200 bg-yellow-50"
                            : "border-gray-200"
                        }`}
                        onClick={() => setSelectedTestBench(bench)}
                      >
                        <CardHeader className="pb-3">
                          <div className="flex items-center justify-between">
                            <CardTitle className="text-sm font-medium">
                              {bench.name}
                            </CardTitle>
                            {getTestBenchStatusBadge(bench.status)}
                          </div>
                        </CardHeader>
                        <CardContent className="space-y-3">
                          {bench.status === "running" && (
                            <div className="space-y-2">
                              <div className="flex justify-between text-xs">
                                <span>产品: {bench.productId}</span>
                                <span>类型: {bench.testType}</span>
                              </div>
                              <Progress value={bench.progress} className="h-2" />
                              <div className="flex justify-between text-xs text-gray-500">
                                <span>进度: {bench.progress}%</span>
                                <span>剩余: {bench.remainingTime}</span>
                              </div>
                            </div>
                          )}
                          
                          <div className="grid grid-cols-2 gap-2 text-xs">
                            <div className="flex justify-between">
                              <span>温度:</span>
                              <span className={bench.temperature > 27 ? "text-red-600" : "text-green-600"}>
                                {bench.temperature}°C
                              </span>
                            </div>
                            <div className="flex justify-between">
                              <span>湿度:</span>
                              <span className={bench.humidity > 50 ? "text-red-600" : "text-green-600"}>
                                {bench.humidity}%
                              </span>
                            </div>
                            <div className="flex justify-between">
                              <span>电压:</span>
                              <span className={bench.voltage > 225 || bench.voltage < 215 ? "text-red-600" : "text-green-600"}>
                                {bench.voltage}V
                              </span>
                            </div>
                            <div className="flex justify-between">
                              <span>电流:</span>
                              <span className={bench.current > 2.5 ? "text-red-600" : "text-green-600"}>
                                {bench.current}A
                              </span>
                            </div>
                          </div>
                        </CardContent>
                      </Card>
                    ))}
                  </div>
                </CardContent>
              </Card>
            </div>
          </TabsContent>
        </Tabs>

        {/* Quick Actions */}
        <div className="grid gap-4 md:grid-cols-3">
          <Card>
            <CardHeader>
              <CardTitle className="text-gray-800 text-sm">快捷操作</CardTitle>
            </CardHeader>
            <CardContent className="space-y-2">
              <Button
                variant="outline"
                className="w-full justify-start"
                size="sm"
              >
                <Pause className="h-4 w-4 mr-2" />
                暂停所有任务
              </Button>
              <Button
                variant="outline"
                className="w-full justify-start"
                size="sm"
              >
                <Play className="h-4 w-4 mr-2" />
                恢复执行
              </Button>
              <Button
                variant="outline"
                className="w-full justify-start"
                size="sm"
              >
                <StopCircle className="h-4 w-4 mr-2" />
                紧急停止
              </Button>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle className="text-gray-800 text-sm">系统状态</CardTitle>
            </CardHeader>
            <CardContent className="space-y-2">
              <div className="flex items-center justify-between text-sm">
                <span>MES连接</span>
                <Badge className="bg-green-100 text-green-800 border-green-200">
                  正常
                </Badge>
              </div>
              <div className="flex items-center justify-between text-sm">
                <span>WCS连接</span>
                <Badge className="bg-green-100 text-green-800 border-green-200">
                  正常
                </Badge>
              </div>
              <div className="flex items-center justify-between text-sm">
                <span>AMR系统</span>
                <Badge className="bg-green-100 text-green-800 border-green-200">
                  正常
                </Badge>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle className="text-gray-800 text-sm">设备状态</CardTitle>
            </CardHeader>
            <CardContent className="space-y-2">
              <div className="flex items-center justify-between text-sm">
                <span>桁架系统</span>
                <Badge className="bg-green-100 text-green-800 border-green-200">
                  运行中
                </Badge>
              </div>
              <div className="flex items-center justify-between text-sm">
                <span>AMR车队</span>
                <Badge className="bg-blue-100 text-blue-800 border-blue-200">
                  4/6运行
                </Badge>
              </div>
              <div className="flex items-center justify-between text-sm">
                <span>测试台</span>
                <Badge className="bg-green-100 text-green-800 border-green-200">
                  6/7运行
                </Badge>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
}
