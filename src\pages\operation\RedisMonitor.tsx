import { useState, useEffect } from "react";
import {
  <PERSON>,
  CardContent,
  CardDescription,
  Card<PERSON><PERSON>er,
  Card<PERSON><PERSON>le,
} from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, Ta<PERSON>Trigger } from "@/components/ui/tabs";
import { StatusChart } from "@/components/StatusChart";
import { Progress } from "@/components/ui/progress";
import {
  Database,
  Activity,
  MemoryStick,
  Network,
  Clock,
  RefreshCw,
  Download,
  AlertTriangle,
  CheckCircle,
  TrendingUp,
  TrendingDown,
  Zap,
  Users,
  Key,
  Hash,
  List,
  CircleDot,
  Circle,
} from "lucide-react";

interface RedisMetrics {
  timestamp: string;
  info: {
    version: string;
    uptime: number;
    connectedClients: number;
    usedMemory: number;
    usedMemoryPeak: number;
    totalSystemMemory: number;
    keyspaceHits: number;
    keyspaceMisses: number;
    totalCommandsProcessed: number;
    totalConnectionsReceived: number;
    totalNetInputBytes: number;
    totalNetOutputBytes: number;
    instantaneousOpsPerSec: number;
    instantaneousInputKbps: number;
    instantaneousOutputKbps: number;
    rejectedConnections: number;
    expiredKeys: number;
    evictedKeys: number;
    keyspace: {
      db0: number;
      db1: number;
      db2: number;
    };
  };
  commands: {
    get: number;
    set: number;
    del: number;
    hget: number;
    hset: number;
    lpush: number;
    rpush: number;
    lpop: number;
    rpop: number;
    sadd: number;
    srem: number;
    zadd: number;
    zrem: number;
  };
  memory: {
    used: number;
    peak: number;
    total: number;
    fragmentation: number;
  };
  performance: {
    opsPerSec: number;
    inputKbps: number;
    outputKbps: number;
    hitRate: number;
  };
}

interface RedisNode {
  id: string;
  role: "master" | "slave";
  host: string;
  port: number;
  status: "online" | "offline" | "syncing";
  lag: number;
  memory: number;
  keys: number;
}

export default function RedisMonitor() {
  const [metrics, setMetrics] = useState<RedisMetrics[]>([]);
  const [currentMetrics, setCurrentMetrics] = useState<RedisMetrics | null>(null);
  const [nodes, setNodes] = useState<RedisNode[]>([]);
  const [selectedNode, setSelectedNode] = useState<string>("all");
  const [isAutoRefresh, setIsAutoRefresh] = useState(true);

  // 模拟Redis数据
  useEffect(() => {
    const generateMetrics = (): RedisMetrics => ({
      timestamp: new Date().toLocaleString(),
      info: {
        version: "7.0.12",
        uptime: 86400 + Math.random() * 3600,
        connectedClients: 50 + Math.random() * 100,
        usedMemory: 512 + Math.random() * 256,
        usedMemoryPeak: 800 + Math.random() * 200,
        totalSystemMemory: 2048,
        keyspaceHits: 1000000 + Math.random() * 100000,
        keyspaceMisses: 10000 + Math.random() * 5000,
        totalCommandsProcessed: 5000000 + Math.random() * 1000000,
        totalConnectionsReceived: 10000 + Math.random() * 2000,
        totalNetInputBytes: 1000000000 + Math.random() * 100000000,
        totalNetOutputBytes: 500000000 + Math.random() * 50000000,
        instantaneousOpsPerSec: 1000 + Math.random() * 500,
        instantaneousInputKbps: 100 + Math.random() * 50,
        instantaneousOutputKbps: 50 + Math.random() * 25,
        rejectedConnections: Math.random() * 10,
        expiredKeys: 1000 + Math.random() * 500,
        evictedKeys: Math.random() * 100,
        keyspace: {
          db0: 10000 + Math.random() * 5000,
          db1: 5000 + Math.random() * 2000,
          db2: 2000 + Math.random() * 1000,
        },
      },
      commands: {
        get: 1000 + Math.random() * 500,
        set: 500 + Math.random() * 200,
        del: 100 + Math.random() * 50,
        hget: 300 + Math.random() * 150,
        hset: 200 + Math.random() * 100,
        lpush: 150 + Math.random() * 75,
        rpush: 150 + Math.random() * 75,
        lpop: 100 + Math.random() * 50,
        rpop: 100 + Math.random() * 50,
        sadd: 80 + Math.random() * 40,
        srem: 60 + Math.random() * 30,
        zadd: 50 + Math.random() * 25,
        zrem: 40 + Math.random() * 20,
      },
      memory: {
        used: 512 + Math.random() * 256,
        peak: 800 + Math.random() * 200,
        total: 2048,
        fragmentation: Math.random() * 10,
      },
      performance: {
        opsPerSec: 1000 + Math.random() * 500,
        inputKbps: 100 + Math.random() * 50,
        outputKbps: 50 + Math.random() * 25,
        hitRate: 90 + Math.random() * 10,
      },
    });

    const updateMetrics = () => {
      const newMetrics = generateMetrics();
      setCurrentMetrics(newMetrics);
      setMetrics(prev => [...prev.slice(-59), newMetrics]);
    };

    updateMetrics();
    const interval = setInterval(updateMetrics, 5000);

    return () => clearInterval(interval);
  }, []);

  // 模拟Redis集群节点
  useEffect(() => {
    const mockNodes: RedisNode[] = [
      {
        id: "redis-master-1",
        role: "master",
        host: "*************",
        port: 6379,
        status: "online",
        lag: 0,
        memory: 512,
        keys: 15000,
      },
      {
        id: "redis-slave-1",
        role: "slave",
        host: "*************",
        port: 6379,
        status: "online",
        lag: 5,
        memory: 512,
        keys: 15000,
      },
      {
        id: "redis-slave-2",
        role: "slave",
        host: "*************",
        port: 6379,
        status: "syncing",
        lag: 120,
        memory: 480,
        keys: 14800,
      },
    ];
    setNodes(mockNodes);
  }, []);

  const getStatusBadge = (status: string) => {
    const statusConfig = {
      online: { label: "在线", className: "bg-green-100 text-green-800 border-green-200" },
      offline: { label: "离线", className: "bg-red-100 text-red-800 border-red-200" },
      syncing: { label: "同步中", className: "bg-yellow-100 text-yellow-800 border-yellow-200" },
    };
    const config = statusConfig[status as keyof typeof statusConfig];
    return <Badge className={`${config.className} border`}>{config.label}</Badge>;
  };

  const getRoleBadge = (role: string) => {
    const roleConfig = {
      master: { label: "主节点", className: "bg-blue-100 text-blue-800 border-blue-200" },
      slave: { label: "从节点", className: "bg-gray-100 text-gray-800 border-gray-200" },
    };
    const config = roleConfig[role as keyof typeof roleConfig];
    return <Badge className={`${config.className} border`}>{config.label}</Badge>;
  };

  if (!currentMetrics) return <div>加载中...</div>;

  const memoryUsage = (currentMetrics.memory.used / currentMetrics.memory.total) * 100;
  const hitRate = currentMetrics.performance.hitRate;

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <div>
          <h2 className="text-3xl font-bold tracking-tight">Redis性能监控</h2>
          <p className="text-muted-foreground">
            监控Redis数据库的性能指标、内存使用、连接统计和命令执行情况
          </p>
        </div>
        <div className="flex items-center space-x-3">
          <Button
            variant="outline"
            size="sm"
            onClick={() => setIsAutoRefresh(!isAutoRefresh)}
          >
            <RefreshCw className={`mr-2 h-4 w-4 ${isAutoRefresh ? 'animate-spin' : ''}`} />
            {isAutoRefresh ? '自动刷新' : '手动刷新'}
          </Button>
          <Button variant="outline" size="sm">
            <Download className="mr-2 h-4 w-4" />
            导出报告
          </Button>
        </div>
      </div>

      {/* 关键指标卡片 */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">内存使用率</CardTitle>
            <MemoryStick className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className={`text-2xl font-bold ${memoryUsage > 80 ? 'text-red-600' : memoryUsage > 60 ? 'text-yellow-600' : 'text-green-600'}`}>
              {memoryUsage.toFixed(1)}%
            </div>
            <div className="flex items-center justify-between mt-2">
              <Progress value={memoryUsage} className="flex-1 mr-2" />
              <Badge className={memoryUsage > 80 ? 'bg-red-100 text-red-800 border-red-200' : memoryUsage > 60 ? 'bg-yellow-100 text-yellow-800 border-yellow-200' : 'bg-green-100 text-green-800 border-green-200'}>
                {memoryUsage > 80 ? '高' : memoryUsage > 60 ? '中' : '正常'}
              </Badge>
            </div>
            <p className="text-xs text-muted-foreground mt-2">
              已用: {currentMetrics.memory.used.toFixed(0)}MB / {currentMetrics.memory.total.toFixed(0)}MB
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">操作/秒</CardTitle>
            <Activity className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-blue-600">
              {currentMetrics.performance.opsPerSec.toFixed(0)}
            </div>
            <div className="flex items-center justify-between mt-2">
              <Progress value={Math.min(currentMetrics.performance.opsPerSec / 2000 * 100, 100)} className="flex-1 mr-2" />
              <Badge className="bg-blue-100 text-blue-800 border-blue-200">正常</Badge>
            </div>
            <p className="text-xs text-muted-foreground mt-2">
              峰值: {currentMetrics.info.usedMemoryPeak.toFixed(0)}MB
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">缓存命中率</CardTitle>
            <CheckCircle className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className={`text-2xl font-bold ${hitRate > 95 ? 'text-green-600' : hitRate > 80 ? 'text-yellow-600' : 'text-red-600'}`}>
              {hitRate.toFixed(1)}%
            </div>
            <div className="flex items-center justify-between mt-2">
              <Progress value={hitRate} className="flex-1 mr-2" />
              <Badge className={hitRate > 95 ? 'bg-green-100 text-green-800 border-green-200' : hitRate > 80 ? 'bg-yellow-100 text-yellow-800 border-yellow-200' : 'bg-red-100 text-red-800 border-red-200'}>
                {hitRate > 95 ? '优秀' : hitRate > 80 ? '良好' : '需优化'}
              </Badge>
            </div>
            <p className="text-xs text-muted-foreground mt-2">
              命中: {currentMetrics.info.keyspaceHits.toLocaleString()} / 未命中: {currentMetrics.info.keyspaceMisses.toLocaleString()}
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">连接数</CardTitle>
            <Users className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-purple-600">
              {currentMetrics.info.connectedClients}
            </div>
            <div className="flex items-center justify-between mt-2">
              <Progress value={Math.min(currentMetrics.info.connectedClients / 200 * 100, 100)} className="flex-1 mr-2" />
              <Badge className="bg-purple-100 text-purple-800 border-purple-200">活跃</Badge>
            </div>
            <p className="text-xs text-muted-foreground mt-2">
              总连接: {currentMetrics.info.totalConnectionsReceived.toLocaleString()}
            </p>
          </CardContent>
        </Card>
      </div>

      {/* 主要内容区域 */}
      <Tabs defaultValue="overview" className="space-y-4">
        <TabsList>
          <TabsTrigger value="overview">概览</TabsTrigger>
          <TabsTrigger value="memory">内存监控</TabsTrigger>
          <TabsTrigger value="commands">命令统计</TabsTrigger>
          <TabsTrigger value="performance">性能监控</TabsTrigger>
          <TabsTrigger value="clusters">集群状态</TabsTrigger>
          <TabsTrigger value="keyspace">键空间</TabsTrigger>
        </TabsList>

        {/* 概览 */}
        <TabsContent value="overview" className="space-y-4">
          <div className="grid gap-4 md:grid-cols-2">
            <Card>
              <CardHeader>
                <CardTitle>Redis性能趋势</CardTitle>
                <CardDescription>内存使用和操作频率变化</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="h-80">
                  <StatusChart
                    type="line"
                    data={metrics.map(m => ({
                      time: m.timestamp,
                      memory: (m.memory.used / m.memory.total) * 100,
                      opsPerSec: m.performance.opsPerSec,
                      hitRate: m.performance.hitRate,
                    }))}
                    dataKey="memory"
                    nameKey="time"
                    colors={["#ef4444", "#3b82f6", "#10b981"]}
                  />
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Redis基本信息</CardTitle>
                <CardDescription>Redis服务器状态和配置信息</CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <label className="text-sm font-medium">Redis版本</label>
                    <p className="text-lg font-semibold">{currentMetrics.info.version}</p>
                  </div>
                  <div>
                    <label className="text-sm font-medium">运行时间</label>
                    <p className="text-lg font-semibold">{Math.floor(currentMetrics.info.uptime / 3600)}小时</p>
                  </div>
                  <div>
                    <label className="text-sm font-medium">网络输入</label>
                    <p className="text-lg font-semibold">{(currentMetrics.info.totalNetInputBytes / 1024 / 1024).toFixed(1)}MB</p>
                  </div>
                  <div>
                    <label className="text-sm font-medium">网络输出</label>
                    <p className="text-lg font-semibold">{(currentMetrics.info.totalNetOutputBytes / 1024 / 1024).toFixed(1)}MB</p>
                  </div>
                </div>
                <div className="space-y-3">
                  <div className="flex justify-between">
                    <span>总命令处理数</span>
                    <span>{currentMetrics.info.totalCommandsProcessed.toLocaleString()}</span>
                  </div>
                  <div className="flex justify-between">
                    <span>拒绝连接数</span>
                    <span>{currentMetrics.info.rejectedConnections}</span>
                  </div>
                  <div className="flex justify-between">
                    <span>过期键数</span>
                    <span>{currentMetrics.info.expiredKeys.toLocaleString()}</span>
                  </div>
                  <div className="flex justify-between">
                    <span>驱逐键数</span>
                    <span>{currentMetrics.info.evictedKeys}</span>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        {/* 内存监控 */}
        <TabsContent value="memory" className="space-y-4">
          <div className="grid gap-4 md:grid-cols-2">
            <Card>
              <CardHeader>
                <CardTitle>内存使用趋势</CardTitle>
                <CardDescription>内存使用量实时变化</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="h-80">
                  <StatusChart
                    type="line"
                    data={metrics.map(m => ({
                      time: m.timestamp,
                      used: m.memory.used,
                      peak: m.memory.peak,
                    }))}
                    dataKey="used"
                    nameKey="time"
                    colors={["#3b82f6", "#ef4444"]}
                  />
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>内存详细信息</CardTitle>
                <CardDescription>内存使用情况详细分析</CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-3">
                  <div className="flex justify-between">
                    <span>已用内存</span>
                    <span>{currentMetrics.memory.used.toFixed(0)}MB</span>
                  </div>
                  <Progress value={(currentMetrics.memory.used / currentMetrics.memory.total) * 100} />
                  
                  <div className="flex justify-between">
                    <span>峰值内存</span>
                    <span>{currentMetrics.memory.peak.toFixed(0)}MB</span>
                  </div>
                  <Progress value={(currentMetrics.memory.peak / currentMetrics.memory.total) * 100} />
                  
                  <div className="flex justify-between">
                    <span>可用内存</span>
                    <span>{(currentMetrics.memory.total - currentMetrics.memory.used).toFixed(0)}MB</span>
                  </div>
                  <Progress value={((currentMetrics.memory.total - currentMetrics.memory.used) / currentMetrics.memory.total) * 100} />
                </div>
                
                <div className="pt-4 border-t">
                  <div className="flex justify-between">
                    <span>内存碎片率</span>
                    <span>{currentMetrics.memory.fragmentation.toFixed(2)}%</span>
                  </div>
                  <Progress value={currentMetrics.memory.fragmentation} />
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        {/* 命令统计 */}
        <TabsContent value="commands" className="space-y-4">
          <div className="grid gap-4 md:grid-cols-2">
            <Card>
              <CardHeader>
                <CardTitle>命令执行统计</CardTitle>
                <CardDescription>各类Redis命令的执行频率</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="h-80">
                  <StatusChart
                    type="bar"
                    data={[
                      { command: "GET", count: currentMetrics.commands.get },
                      { command: "SET", count: currentMetrics.commands.set },
                      { command: "DEL", count: currentMetrics.commands.del },
                      { command: "HGET", count: currentMetrics.commands.hget },
                      { command: "HSET", count: currentMetrics.commands.hset },
                      { command: "LPUSH", count: currentMetrics.commands.lpush },
                      { command: "RPUSH", count: currentMetrics.commands.rpush },
                    ]}
                    dataKey="count"
                    nameKey="command"
                    colors={["#3b82f6"]}
                  />
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>命令类型分布</CardTitle>
                <CardDescription>按数据类型统计的命令分布</CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-3">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-2">
                      <Key className="h-4 w-4 text-blue-600" />
                      <span>字符串操作</span>
                    </div>
                    <span>{currentMetrics.commands.get + currentMetrics.commands.set} ops/s</span>
                  </div>
                  <Progress value={((currentMetrics.commands.get + currentMetrics.commands.set) / currentMetrics.performance.opsPerSec) * 100} />
                  
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-2">
                      <Hash className="h-4 w-4 text-green-600" />
                      <span>哈希操作</span>
                    </div>
                    <span>{currentMetrics.commands.hget + currentMetrics.commands.hset} ops/s</span>
                  </div>
                  <Progress value={((currentMetrics.commands.hget + currentMetrics.commands.hset) / currentMetrics.performance.opsPerSec) * 100} />
                  
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-2">
                      <List className="h-4 w-4 text-purple-600" />
                      <span>列表操作</span>
                    </div>
                    <span>{currentMetrics.commands.lpush + currentMetrics.commands.rpush + currentMetrics.commands.lpop + currentMetrics.commands.rpop} ops/s</span>
                  </div>
                  <Progress value={((currentMetrics.commands.lpush + currentMetrics.commands.rpush + currentMetrics.commands.lpop + currentMetrics.commands.rpop) / currentMetrics.performance.opsPerSec) * 100} />
                  
                                     <div className="flex items-center justify-between">
                     <div className="flex items-center space-x-2">
                       <CircleDot className="h-4 w-4 text-orange-600" />
                       <span>集合操作</span>
                     </div>
                     <span>{currentMetrics.commands.sadd + currentMetrics.commands.srem} ops/s</span>
                   </div>
                   <Progress value={((currentMetrics.commands.sadd + currentMetrics.commands.srem) / currentMetrics.performance.opsPerSec) * 100} />
                   
                   <div className="flex items-center justify-between">
                     <div className="flex items-center space-x-2">
                       <Circle className="h-4 w-4 text-red-600" />
                       <span>有序集合操作</span>
                     </div>
                     <span>{currentMetrics.commands.zadd + currentMetrics.commands.zrem} ops/s</span>
                   </div>
                   <Progress value={((currentMetrics.commands.zadd + currentMetrics.commands.zrem) / currentMetrics.performance.opsPerSec) * 100} />
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        {/* 性能监控 */}
        <TabsContent value="performance" className="space-y-4">
          <div className="grid gap-4 md:grid-cols-2">
            <Card>
              <CardHeader>
                <CardTitle>网络性能</CardTitle>
                <CardDescription>网络输入输出流量</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="h-80">
                  <StatusChart
                    type="line"
                    data={metrics.map(m => ({
                      time: m.timestamp,
                      input: m.performance.inputKbps,
                      output: m.performance.outputKbps,
                    }))}
                    dataKey="input"
                    nameKey="time"
                    colors={["#3b82f6", "#ef4444"]}
                  />
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>性能指标</CardTitle>
                <CardDescription>关键性能指标统计</CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <label className="text-sm font-medium">操作/秒</label>
                    <p className="text-2xl font-bold">{currentMetrics.performance.opsPerSec.toFixed(0)}</p>
                  </div>
                  <div>
                    <label className="text-sm font-medium">输入带宽</label>
                    <p className="text-2xl font-bold">{currentMetrics.performance.inputKbps.toFixed(1)} KB/s</p>
                  </div>
                  <div>
                    <label className="text-sm font-medium">输出带宽</label>
                    <p className="text-2xl font-bold">{currentMetrics.performance.outputKbps.toFixed(1)} KB/s</p>
                  </div>
                  <div>
                    <label className="text-sm font-medium">命中率</label>
                    <p className="text-2xl font-bold">{currentMetrics.performance.hitRate.toFixed(1)}%</p>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        {/* 集群状态 */}
        <TabsContent value="clusters" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Redis集群节点</CardTitle>
              <CardDescription>集群节点状态和同步情况</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {nodes.map((node) => (
                  <div
                    key={node.id}
                    className="flex items-center justify-between p-4 border rounded-lg"
                  >
                    <div className="flex items-center space-x-4">
                      <div>
                        <p className="font-medium">{node.id}</p>
                        <p className="text-sm text-muted-foreground">
                          {node.host}:{node.port}
                        </p>
                      </div>
                      {getRoleBadge(node.role)}
                      {getStatusBadge(node.status)}
                    </div>
                    <div className="text-right">
                      <p className="text-sm font-medium">{node.memory}MB</p>
                      <p className="text-xs text-muted-foreground">{node.keys.toLocaleString()} keys</p>
                      {node.role === "slave" && (
                        <p className="text-xs text-muted-foreground">延迟: {node.lag}ms</p>
                      )}
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* 键空间 */}
        <TabsContent value="keyspace" className="space-y-4">
          <div className="grid gap-4 md:grid-cols-3">
            <Card>
              <CardHeader>
                <CardTitle>数据库 0</CardTitle>
                <CardDescription>默认数据库</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{currentMetrics.info.keyspace.db0.toLocaleString()}</div>
                <p className="text-xs text-muted-foreground">键数量</p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>数据库 1</CardTitle>
                <CardDescription>用户数据</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{currentMetrics.info.keyspace.db1.toLocaleString()}</div>
                <p className="text-xs text-muted-foreground">键数量</p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>数据库 2</CardTitle>
                <CardDescription>缓存数据</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{currentMetrics.info.keyspace.db2.toLocaleString()}</div>
                <p className="text-xs text-muted-foreground">键数量</p>
              </CardContent>
            </Card>
          </div>
        </TabsContent>
      </Tabs>
    </div>
  );
} 