import { useRef, useMemo, useState, useEffect } from "react";
import { <PERSON><PERSON>, use<PERSON>rame, useThree } from "@react-three/fiber";
import { OrbitControls, Text, Html, useGLTF } from "@react-three/drei";
import * as THREE from "three";

interface StorageItem {
  id: string;
  row: number; // 1-6排
  column: number; // 列号
  level: number; // 1-3层
  status: "empty" | "stored" | "testing";
  productId?: string;
}

interface EnhancedWarehouseVisualizationProps {
  storageData?: StorageItem[];
  className?: string;
}

// 货架配置：每排的列数
const ROW_CONFIG = {
  1: 28, // 第一排28列
  2: 34, // 第二排34列
  3: 34, // 第三排34列
  4: 34, // 第四排34列
  5: 34, // 第五排34列
  6: 36, // 第六排36列
};

// 过道位置：第22和23列之间
const AISLE_POSITION = 22.5;

// 生成模拟数据
const generateMockStorageData = (): StorageItem[] => {
  const data: StorageItem[] = [];
  
  for (let row = 1; row <= 6; row++) {
    const columns = ROW_CONFIG[row as keyof typeof ROW_CONFIG];
    
    for (let col = 1; col <= columns; col++) {
      for (let level = 1; level <= 3; level++) {
        const status = Math.random() > 0.3 ? "empty" : 
                      Math.random() > 0.5 ? "stored" : "testing";
        
        data.push({
          id: `${row}-${col}-${level}`,
          row,
          column: col,
          level,
          status,
          productId: status !== "empty" ? `PROD${Math.floor(Math.random() * 100000).toString().padStart(5, '0')}` : undefined
        });
      }
    }
  }
  
  return data;
};

// GLB模型组件
function ShelfModel({ position, scale = 1 }: { position: [number, number, number]; scale?: number }) {
  const { scene } = useGLTF('/src/asserts/货架.glb');
  const modelRef = useRef<THREE.Group>(null);

  useEffect(() => {
    if (modelRef.current) {
      modelRef.current.scale.setScalar(scale);
    }
  }, [scale]);

  return (
    <primitive 
      ref={modelRef}
      object={scene.clone()} 
      position={position}
    />
  );
}

function ProductModel({ position, scale = 0.5 }: { position: [number, number, number]; scale?: number }) {
  const { scene } = useGLTF('/src/asserts/惯组.glb');
  const modelRef = useRef<THREE.Group>(null);

  useEffect(() => {
    if (modelRef.current) {
      modelRef.current.scale.setScalar(scale);
    }
  }, [scale]);

  return (
    <primitive 
      ref={modelRef}
      object={scene.clone()} 
      position={position}
    />
  );
}

// 单个货位组件
function StorageSlot({
  position,
  status,
  productId,
  slotInfo,
  isSelected,
  onSlotSelect,
  onSlotClick,
  onContextMenu
}: {
  position: [number, number, number];
  status: "empty" | "stored" | "testing";
  productId?: string;
  slotInfo?: { row: number; column: number; level: number };
  isSelected?: boolean;
  onSlotSelect?: () => void;
  onSlotClick?: () => void;
  onContextMenu?: (event: any, item: any) => void;
}) {
  const meshRef = useRef<THREE.Mesh>(null);
  const [hovered, setHovered] = useState(false);

  // 根据状态设置颜色
  const getColor = () => {
    switch (status) {
      case "empty":
        return "#e5e7eb"; // 灰色
      case "stored":
        return "#10b981"; // 绿色
      case "testing":
        return "#f59e0b"; // 橙色
      default:
        return "#e5e7eb";
    }
  };

  // 悬停效果
  useFrame(() => {
    if (meshRef.current) {
      if (hovered) {
        meshRef.current.scale.setScalar(1.1);
      } else {
        meshRef.current.scale.setScalar(1.0);
      }
    }
  });

  return (
    <group position={position}>
      {/* 选中高亮边框 */}
      {isSelected && (
        <mesh position={[0, 0, 0]}>
          <boxGeometry args={[0.9, 0.9, 1.3]} />
          <meshStandardMaterial 
            color="#3B82F6" 
            transparent
            opacity={0.6}
            wireframe={true}
          />
        </mesh>
      )}
      
      <mesh
        ref={meshRef}
        onClick={() => {
          onSlotSelect?.();
          onSlotClick?.();
        }}
        onPointerOver={() => setHovered(true)}
        onPointerOut={() => setHovered(false)}
        onPointerDown={(event) => {
          if (event.button === 2) { // 右键
            event.stopPropagation();
            if (slotInfo) {
              const item = {
                ...slotInfo,
                status,
                productId
              };
              onContextMenu?.(event, item);
            }
          }
        }}
      >
        <boxGeometry args={[0.8, 0.8, 1.2]} />
        <meshStandardMaterial 
          color={getColor()} 
          transparent
          opacity={status === "empty" ? 0.3 : 0.8}
        />
      </mesh>

      {/* 如果有产品，显示产品模型 */}
      {productId && status !== "empty" && (
        <ProductModel position={[0, 0, 0]} scale={0.3} />
      )}

      {productId && (
        <Text
          position={[0, 0, 0.7]}
          fontSize={0.08}
          color="#000000"
          anchorX="center"
          anchorY="middle"
        >
          {productId.substring(0, 4)}
        </Text>
      )}

      {/* 气泡提示 */}
      {hovered && slotInfo && (
        <Html position={[0, 1.2, 0]} center>
          <div className="bg-black text-white text-xs px-2 py-1 rounded shadow-lg whitespace-nowrap">
            <div>第{slotInfo.row}排-{slotInfo.column}列-{slotInfo.level}层</div>
            {productId && <div>产品: {productId}</div>}
            <div>状态: {status === "empty" ? "空货位" : status === "stored" ? "已存储" : "测试中"}</div>
          </div>
        </Html>
      )}
    </group>
  );
}

// 单排货架组件
function ShelfRow({
  rowNumber,
  position,
  storageData,
  onSlotClick,
  selectedSlot,
  onSlotSelect,
  onContextMenu
}: {
  rowNumber: number;
  position: [number, number, number];
  storageData: StorageItem[];
  onSlotClick?: (item: StorageItem) => void;
  selectedSlot?: { row: number; column: number; level: number };
  onSlotSelect?: (slot: { row: number; column: number; level: number }) => void;
  onContextMenu?: (event: any, item: any) => void;
}) {
  const columns = ROW_CONFIG[rowNumber as keyof typeof ROW_CONFIG];
  
  // 获取当前排的数据
  const rowData = storageData.filter(item => item.row === rowNumber);

  return (
    <group position={position}>
      {/* 货架主体结构 */}
      {/* 立柱 */}
      {[-1.5, 1.5].map((xPos, pillarIndex) => (
        <mesh key={`front-pillar-${pillarIndex}`} position={[xPos, 2.1, 1.2]}>
          <boxGeometry args={[0.1, 4, 0.1]} />
          <meshStandardMaterial 
            color="#87CEEB" 
            metalness={0.5}
            roughness={0.1}
            emissive="#4A90E2"
            emissiveIntensity={0.2}
          />
        </mesh>
      ))}
      
      {[-1.5, 1.5].map((xPos, pillarIndex) => (
        <mesh key={`back-pillar-${pillarIndex}`} position={[xPos, 2.1, -1.2]}>
          <boxGeometry args={[0.1, 4, 0.1]} />
          <meshStandardMaterial 
            color="#87CEEB" 
            metalness={0.5}
            roughness={0.1}
            emissive="#4A90E2"
            emissiveIntensity={0.2}
          />
        </mesh>
      ))}

      {/* 三层货位 */}
      {[1, 2, 3].map((level) => (
        <group key={level} position={[0, (level-0.7) * 1.3, 0]}>
          {/* 层板 */}
          <mesh position={[0, 0, 0]}>
            <boxGeometry args={[columns * 1.0 + 1, 0.1, 3]} />
            <meshStandardMaterial 
              color="#a0abc0" 
              metalness={0.85}
              roughness={0.15}
              emissive="#a0abcF"
              emissiveIntensity={0.2}
            />
          </mesh>
          
          {/* 左右挡板 */}
          <mesh position={[-(columns * 1.0 + 1) / 2 - 0.05, 0, 0]}>
            <boxGeometry args={[0.1, 0.1, 3]} />
            <meshStandardMaterial 
              color="#a0abc0" 
              metalness={0.85}
              roughness={0.15}
              emissive="#a0abcF"
              emissiveIntensity={0.2}
            />
          </mesh>
          
          <mesh position={[(columns * 1.0 + 1) / 2 + 0.05, 0, 0]}>
            <boxGeometry args={[0.1, 0.1, 3]} />
            <meshStandardMaterial 
              color="#a0abc0" 
              metalness={0.85}
              roughness={0.15}
              emissive="#a0abcF"
              emissiveIntensity={0.2}
            />
          </mesh>
          
          {/* 货位 */}
          {Array.from({ length: columns }, (_, colIndex) => {
            const column = colIndex + 1;
            const slotPosition: [number, number, number] = [
              -(columns * 1.0) / 2 + colIndex * 1.0 + 0.5, // 水平分布
              0.5, // 在层板上方
              0, // 居中
            ];

            const storageItem = rowData.find(
              item => item.level === level && item.column === column
            );

            const currentSlot = { row: rowNumber, column, level };
            const isSelected = selectedSlot && 
              selectedSlot.row === currentSlot.row && 
              selectedSlot.column === currentSlot.column && 
              selectedSlot.level === currentSlot.level;
            
            return (
              <StorageSlot
                key={`${rowNumber}-${column}-${level}`}
                position={slotPosition}
                status={storageItem?.status || "empty"}
                productId={storageItem?.productId}
                onSlotClick={() => storageItem && onSlotClick?.(storageItem)}
                slotInfo={currentSlot}
                isSelected={isSelected}
                onSlotSelect={() => onSlotSelect?.(currentSlot)}
                onContextMenu={onContextMenu}
              />
            );
          })}
        </group>
      ))}

      {/* 货架编号 */}
      <Text
        position={[0, 4.2, 0]}
        fontSize={0.3}
        color="#1e40af"
        anchorX="center"
        anchorY="middle"
      >
        第{rowNumber}排
      </Text>
    </group>
  );
}

// 3D场景组件
function WarehouseScene({ 
  storageData, 
  onSlotClick,
  selectedSlot,
  onSlotSelect,
  onContextMenu
}: { 
  storageData: StorageItem[];
  onSlotClick?: (item: StorageItem) => void;
  selectedSlot?: { row: number; column: number; level: number };
  onSlotSelect?: (slot: { row: number; column: number; level: number }) => void;
  onContextMenu?: (event: any, item: any) => void;
}) {
  const sceneRef = useRef<THREE.Group>(null);

  // 计算货架位置
  const shelfPositions: [number, number, number][] = [
    // 第一排 - 最前面
    [0, 0, 0],
    // 第二排和第三排背对背
    [0, 0, -8], // 第二排
    [0, 0, -16], // 第三排
    // 第四排和第五排背对背
    [0, 0, -24], // 第四排
    [0, 0, -32], // 第五排
    // 第六排 - 最后面
    [0, 0, -40],
  ];

  return (
    <group ref={sceneRef}>
      {/* 地面 */}
      <mesh position={[0, -0.5, -20]} rotation={[-Math.PI / 2, 0, 0]}>
        <planeGeometry args={[100, 100]} />
        <meshStandardMaterial color="#f3f4f6" />
      </mesh>

      {/* 过道标记 */}
      <mesh position={[AISLE_POSITION, 0.1, -20]} rotation={[-Math.PI / 2, 0, 0]}>
        <planeGeometry args={[2, 100]} />
        <meshStandardMaterial color="#fbbf24" transparent opacity={0.3} />
      </mesh>

      {/* 6排货架 */}
      {shelfPositions.map((position, index) => (
        <ShelfRow
          key={index + 1}
          rowNumber={index + 1}
          position={position}
          storageData={storageData}
          onSlotClick={onSlotClick}
          selectedSlot={selectedSlot}
          onSlotSelect={onSlotSelect}
          onContextMenu={onContextMenu}
        />
      ))}

      {/* 环境光 */}
      <ambientLight intensity={0.4} />
      
      {/* 主方向光 */}
      <directionalLight
        position={[10, 15, 10]}
        intensity={1.2}
        castShadow
        shadow-mapSize-width={2048}
        shadow-mapSize-height={2048}
      />
      
      {/* 补光 */}
      <directionalLight
        position={[-10, 10, -5]}
        intensity={0.7}
        castShadow
      />
      
      {/* 顶部光 */}
      <directionalLight
        position={[0, 20, 0]}
        intensity={0.8}
        castShadow
      />
    </group>
  );
}

// 主组件
export function EnhancedWarehouseVisualization({
  storageData = [],
  className = "",
}: EnhancedWarehouseVisualizationProps) {
  const [data, setData] = useState<StorageItem[]>(() => 
    storageData.length > 0 ? storageData : generateMockStorageData()
  );
  const [selectedSlot, setSelectedSlot] = useState<{ row: number; column: number; level: number } | null>(null);
  const [contextMenu, setContextMenu] = useState<{ visible: boolean; x: number; y: number; item: any } | null>(null);
  const [cameraPosition, setCameraPosition] = useState<[number, number, number]>([0, 15, 20]);
  const [cameraTarget, setCameraTarget] = useState<[number, number, number]>([0, 2, -20]);

  const handleSlotClick = (item: StorageItem) => {
    console.log("点击货位:", item);
  };

  const handleSlotSelect = (slot: { row: number; column: number; level: number }) => {
    setSelectedSlot(slot);
  };

  // 获取选中货位的产品信息
  const getSelectedItem = () => {
    if (!selectedSlot) return null;
    return data.find(item => 
      item.row === selectedSlot.row && 
      item.column === selectedSlot.column && 
      item.level === selectedSlot.level
    );
  };

  const handleContextMenu = (event: any, item: any) => {
    event.preventDefault();
    setContextMenu({
      visible: true,
      x: event.clientX || 100,
      y: event.clientY || 100,
      item
    });
  };

  const handleContextMenuAction = (action: string) => {
    if (!contextMenu) return;
    
    const { item } = contextMenu;
    let message = "";
    
    switch (action) {
      case "outbound_test":
        message = `已经安排产品 ${item.productId} 进行出库测试`;
        break;
      case "outbound":
        message = `开始出厂 - 产品 ${item.productId}`;
        break;
      case "end_test":
        message = `产品 ${item.productId} 正在结束测试，请稍后`;
        break;
      default:
        message = "操作完成";
    }
    
    alert(message);
    setContextMenu(null);
  };

  const handleClickOutside = () => {
    setContextMenu(null);
  };

  const stats = useMemo(() => {
    const total = data.length;
    const empty = data.filter(item => item.status === "empty").length;
    const stored = data.filter(item => item.status === "stored").length;
    const testing = data.filter(item => item.status === "testing").length;
    
    return { total, empty, stored, testing };
  }, [data]);

  // 相机位置控制函数
  const handleCameraToFirstRow = () => {
    setCameraPosition([0, 15, 20]);
    setCameraTarget([0, 2, 0]);
  };

  const handleCameraToMiddleRows = () => {
    setCameraPosition([0, 15, 0]);
    setCameraTarget([0, 2, -20]);
  };

  const handleCameraToLastRow = () => {
    setCameraPosition([0, 15, -20]);
    setCameraTarget([0, 2, -40]);
  };

  const handleCameraToOverview = () => {
    setCameraPosition([0, 25, 30]);
    setCameraTarget([0, 2, -20]);
  };

  return (
    <div 
      className={`w-full h-full ${className}`} 
      onClick={handleClickOutside}
      onContextMenu={(e) => e.preventDefault()}
    >
      <div className="space-y-4">
        {/* 控制面板 */}
        <div className="flex items-center justify-between p-4 bg-white rounded-lg shadow-sm">
          <div className="flex items-center space-x-4">
            <h3 className="text-lg font-semibold text-gray-800">仓储控制系统</h3>
            <div className="text-sm text-gray-600">
              共 {stats.total} 个货位
            </div>
            {selectedSlot && (
              <div className="text-sm text-blue-600 font-medium">
                已选中: 第{selectedSlot.row}排-{selectedSlot.column}列-{selectedSlot.level}层
                {(() => {
                  const selectedItem = getSelectedItem();
                  if (selectedItem && selectedItem.productId) {
                    return ` - 产品: ${selectedItem.productId}`;
                  } else {
                    return ' - 空货位';
                  }
                })()}
              </div>
            )}
          </div>
          
          <div className="flex items-center space-x-6 text-sm">
            <div className="flex items-center space-x-2">
              <div className="w-4 h-4 bg-gray-300 rounded"></div>
              <span>空货位 ({stats.empty})</span>
            </div>
            <div className="flex items-center space-x-2">
              <div className="w-4 h-4 bg-green-500 rounded"></div>
              <span>已存储 ({stats.stored})</span>
            </div>
            <div className="flex items-center space-x-2">
              <div className="w-4 h-4 bg-orange-500 rounded"></div>
              <span>测试中 ({stats.testing})</span>
            </div>
          </div>
        </div>

        {/* 相机位置控制 */}
        <div className="flex items-center justify-center space-x-3 p-3 bg-white rounded-lg shadow-sm">
          <span className="text-sm font-medium text-gray-700">视角控制:</span>
          <button
            onClick={handleCameraToFirstRow}
            className="px-3 py-1 bg-blue-500 hover:bg-blue-600 text-white rounded text-sm font-medium transition-all duration-200"
          >
            第一排货架
          </button>
          <button
            onClick={handleCameraToMiddleRows}
            className="px-3 py-1 bg-green-500 hover:bg-green-600 text-white rounded text-sm font-medium transition-all duration-200"
          >
            中间货架
          </button>
          <button
            onClick={handleCameraToLastRow}
            className="px-3 py-1 bg-purple-500 hover:bg-purple-600 text-white rounded text-sm font-medium transition-all duration-200"
          >
            最后一排
          </button>
          <button
            onClick={handleCameraToOverview}
            className="px-3 py-1 bg-orange-500 hover:bg-orange-600 text-white rounded text-sm font-medium transition-all duration-200"
          >
            全景视角
          </button>
        </div>

        {/* 3D画布 */}
        <div 
          className="w-full h-96 bg-gradient-to-br from-blue-50 to-indigo-100 rounded-lg overflow-hidden relative"
          onContextMenu={(e) => e.preventDefault()}
        >
          <Canvas
            camera={{ 
              position: cameraPosition, 
              fov: 45,
              near: 0.1,
              far: 1000
            }}
            shadows
            onContextMenu={(e) => {
              e.preventDefault();
            }}
          >
            <WarehouseScene 
              storageData={data} 
              onSlotClick={handleSlotClick}
              selectedSlot={selectedSlot}
              onSlotSelect={handleSlotSelect}
              onContextMenu={handleContextMenu}
            />
            
            <OrbitControls
              enablePan={true}
              enableZoom={true}
              enableRotate={true}
              maxPolarAngle={Math.PI / 2}
              minPolarAngle={0}
              maxDistance={100}
              minDistance={5}
              target={cameraTarget}
              defaultPolarAngle={Math.PI / 2 - Math.PI / 9}
              enableDamping={true}
              dampingFactor={0.05}
            />
          </Canvas>

          {/* 右键菜单 */}
          {contextMenu && (
            <div 
              className="absolute bg-white border border-gray-200 rounded-lg shadow-lg py-2 z-50"
              style={{ 
                left: contextMenu.x, 
                top: contextMenu.y,
                minWidth: '150px'
              }}
              onClick={(e) => e.stopPropagation()}
              onContextMenu={(e) => e.preventDefault()}
            >
              <div className="text-xs text-gray-500 px-2 py-1 border-b">
                第{contextMenu.item.row}排-{contextMenu.item.column}列-{contextMenu.item.level}层
              </div>
              {contextMenu.item.status === "stored" && (
                <>
                  <button
                    className="w-full text-left px-4 py-2 hover:bg-gray-100 text-sm"
                    onClick={() => handleContextMenuAction("outbound_test")}
                  >
                    出库测试
                  </button>
                  <button
                    className="w-full text-left px-4 py-2 hover:bg-gray-100 text-sm"
                    onClick={() => handleContextMenuAction("outbound")}
                  >
                    出厂
                  </button>
                </>
              )}
              {contextMenu.item.status === "testing" && (
                <button
                  className="w-full text-left px-4 py-2 hover:bg-gray-100 text-sm"
                  onClick={() => handleContextMenuAction("end_test")}
                >
                  结束测试
                </button>
              )}
            </div>
          )}
        </div>

        {/* 说明文字 */}
        <div className="text-sm text-gray-600 bg-white p-3 rounded-lg">
          <p>• 仓储系统共600个货位，分6排显示</p>
          <p>• 第一排28列，第二/三/四/五排34列，第六排36列</p>
          <p>• 第二排与第三排背对背，第四排与第五排背对背</p>
          <p>• 第二三四五排中间有过道，位于第22与第23列之间</p>
          <p>• 灰色立方体：空货位 | 绿色立方体：已存储惯组 | 橙色立方体：测试中惯组</p>
          <p>• 点击货位可选中并查看详细信息</p>
          <p>• 右键点击有产品的货位可进行出库测试、出厂等操作</p>
          <p>• <strong>视角控制：</strong>使用上方按钮快速定位到不同排的货架或全景视角</p>
          <p>• <strong>漫游控制：</strong>鼠标拖拽旋转视角 | 滚轮缩放 | 右键拖拽平移</p>
        </div>
      </div>
    </div>
  );
}
