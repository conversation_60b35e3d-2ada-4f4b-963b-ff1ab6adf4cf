import { useState } from "react";
import { DashboardCard } from "@/components/DashboardCard";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Calendar } from "@/components/ui/calendar";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Badge } from "@/components/ui/badge";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import {
  Plus,
  Calendar as CalendarIcon,
  Users,
  Eye,
  Pause,
  Search,
  Trash2,
  Play,
  CheckSquare,
  Square,
  Factory,
  UserPlus,
  Clock,
} from "lucide-react";

interface TestPlan {
  id: string;
  productId: string;
  testType: string;
  scheduledDate: string;
  assignedOperator: string;
  status: "pending" | "running" | "completed" | "paused";
  priority: "high" | "medium" | "low";
}

interface Shift {
  id: string;
  name: string;
  startTime: string;
  endTime: string;
  operators: string[];
}

interface ProductionTask {
  id: string;
  taskType: "排产" | "取消排产";
  productId: string;
  batchNumber: string;
  productionTime: string;
  status: "pending" | "processing" | "completed" | "failed";
  priority: "high" | "medium" | "low";
  operator?: string;
  notes?: string;
}

interface MESProductionNotification {
  id: string;
  productId: string;
  productName: string;
  batchNumber: string;
  quantity: number;
  testType: string;
  priority: "high" | "medium" | "low";
  estimatedDuration: string;
  dueDate: string;
  status: "pending" | "approved" | "rejected";
}

interface Operator {
  id: string;
  name: string;
  department: string;
  skills: string[];
  status: "available" | "busy" | "off";
  currentWorkload: number;
  maxWorkload: number;
}

interface ShiftSchedule {
  id: string;
  operatorId: string;
  operatorName: string;
  date: string;
  shiftType: "早班" | "中班" | "夜班";
  startTime: string;
  endTime: string;
  status: "scheduled" | "completed" | "cancelled";
}

const mockTestPlans: TestPlan[] = [
  {
    id: "TP001",
    productId: "A-2024-001",
    testType: "月稳测试",
    scheduledDate: "2025-08-15",
    assignedOperator: "张三",
    status: "pending",
    priority: "high",
  },
  {
    id: "TP002",
    productId: "A-2024-002",
    testType: "月稳测试",
    scheduledDate: "2025-08-15",
    assignedOperator: "李四",
    status: "running",
    priority: "medium",
  },
  {
    id: "TP003",
    productId: "A-2024-003",
    testType: "月稳测试",
    scheduledDate: "2025-08-16",
    assignedOperator: "王五",
    status: "completed",
    priority: "low",
  },
  {
    id: "TP004",
    productId: "A-2024-004",
    testType: "临时测试",
    scheduledDate: "2025-08-17",
    assignedOperator: "赵六",
    status: "pending",
    priority: "high",
  },
  {
    id: "TP005",
    productId: "A-2024-005",
    testType: "月稳测试",
    scheduledDate: "2025-08-18",
    assignedOperator: "孙七",
    status: "paused",
    priority: "medium",
  },
  {
    id: "TP006",
    productId: "A-2024-006",
    testType: "临时测试",
    scheduledDate: "2025-08-19",
    assignedOperator: "周八",
    status: "pending",
    priority: "low",
  },
];

const mockShifts: Shift[] = [
  {
    id: "S1",
    name: "早班",
    startTime: "08:00",
    endTime: "16:00",
    operators: ["张三", "李四"],
  },
  {
    id: "S2",
    name: "中班",
    startTime: "16:00",
    endTime: "00:00",
    operators: ["王五", "赵六"],
  },
  {
    id: "S3",
    name: "夜班",
    startTime: "00:00",
    endTime: "08:00",
    operators: ["孙七", "周八"],
  },
];

const mockProductionTasks: ProductionTask[] = [
  {
    id: "PT001",
    taskType: "排产",
    productId: "A-2024-001",
    batchNumber: "BATCH-2024-001",
    productionTime: "2025-08-15 14:30:00",
    status: "completed",
    priority: "high",
    operator: "张三",
    notes: "MES系统自动下发"
  },
  {
    id: "PT002",
    taskType: "排产",
    productId: "A-2024-002",
    batchNumber: "BATCH-2024-002",
    productionTime: "2025-08-15 16:45:00",
    status: "processing",
    priority: "medium",
    operator: "李四",
    notes: "紧急排产任务"
  },
  {
    id: "PT003",
    taskType: "取消排产",
    productId: "A-2024-003",
    batchNumber: "BATCH-2024-003",
    productionTime: "2025-08-15 10:20:00",
    status: "completed",
    priority: "high",
    operator: "王五",
    notes: "客户取消订单"
  },
  {
    id: "PT004",
    taskType: "排产",
    productId: "A-2024-004",
    batchNumber: "BATCH-2024-004",
    productionTime: "2025-08-15 09:15:00",
    status: "pending",
    priority: "low",
    notes: "常规排产任务"
  },
  {
    id: "PT005",
    taskType: "排产",
    productId: "A-2024-005",
    batchNumber: "BATCH-2024-005",
    productionTime: "2025-08-15 11:30:00",
    status: "failed",
    priority: "high",
    operator: "赵六",
    notes: "设备故障导致失败"
  },
  {
    id: "PT006",
    taskType: "排产",
    productId: "A-2024-006",
    batchNumber: "BATCH-2024-006",
    productionTime: "2025-08-15 13:45:00",
    status: "pending",
    priority: "medium",
    notes: "MES系统自动下发"
  },
  {
    id: "PT007",
    taskType: "取消排产",
    productId: "A-2024-007",
    batchNumber: "BATCH-2024-007",
    productionTime: "2025-08-15 15:20:00",
    status: "completed",
    priority: "medium",
    operator: "孙七",
    notes: "质量检查不通过"
  },
  {
    id: "PT008",
    taskType: "排产",
    productId: "A-2024-008",
    batchNumber: "BATCH-2024-008",
    productionTime: "2025-08-15 17:10:00",
    status: "processing",
    priority: "high",
    operator: "周八",
    notes: "紧急订单"
  }
];

// MES排产通知模拟数据
const mockMESNotifications: MESProductionNotification[] = [
  {
    id: "MES001",
    productId: "A-2024-006",
    productName: "光纤惯导组件A型",
    batchNumber: "B202401006",
    quantity: 1,
    testType: "月稳测试",
    priority: "high",
    estimatedDuration: "6小时",
    dueDate: "2025-08-16",
    status: "pending",
  },
  {
    id: "MES002",
    productId: "A-2024-007",
    productName: "光纤惯导组件B型",
    batchNumber: "B202401007",
    quantity: 1,
    testType: "月稳测试",
    priority: "medium",
    estimatedDuration: "6小时",
    dueDate: "2025-08-16",
    status: "pending",
  },
  {
    id: "MES003",
    productId: "A-2024-008",
    productName: "光纤惯导组件C型",
    batchNumber: "B202401008",
    quantity: 1,
    testType: "临时测试",
    priority: "low",
    estimatedDuration: "4小时",
    dueDate: "2025-08-17",
    status: "pending",
  },
  {
    id: "MES004",
    productId: "A-2024-009",
    productName: "光纤惯导组件D型",
    batchNumber: "B202401009",
    quantity: 1,
    testType: "月稳测试",
    priority: "high",
    estimatedDuration: "6小时",
    dueDate: "2025-08-16",
    status: "pending",
  },
  {
    id: "MES005",
    productId: "A-2025-080",
    productName: "光纤惯导组件E型",
    batchNumber: "B202401010",
    quantity: 1,
    testType: "临时测试",
    priority: "medium",
    estimatedDuration: "4小时",
    dueDate: "2025-08-17",
    status: "pending",
  },
];

// 操作员模拟数据
const mockOperators: Operator[] = [
  {
    id: "OP001",
    name: "张三",
    department: "测试部",
    skills: ["月稳测试", "临时测试"],
    status: "available",
    currentWorkload: 3,
    maxWorkload: 4,
  },
  {
    id: "OP002",
    name: "李四",
    department: "测试部",
    skills: ["月稳测试"],
    status: "busy",
    currentWorkload: 4,
    maxWorkload: 4,
  },
  {
    id: "OP003",
    name: "王五",
    department: "测试部",
    skills: ["临时测试"],
    status: "available",
    currentWorkload: 2,
    maxWorkload: 4,
  },
  {
    id: "OP004",
    name: "赵六",
    department: "测试部",
    skills: ["月稳测试", "临时测试"],
    status: "available",
    currentWorkload: 1,
    maxWorkload: 4,
  },
  {
    id: "OP005",
    name: "孙七",
    department: "测试部",
    skills: ["月稳测试"],
    status: "off",
    currentWorkload: 0,
    maxWorkload: 4,
  },
  {
    id: "OP006",
    name: "周八",
    department: "测试部",
    skills: ["临时测试"],
    status: "available",
    currentWorkload: 2,
    maxWorkload: 4,
  },
];

// 排班记录模拟数据
const mockShiftSchedules: ShiftSchedule[] = [
  {
    id: "SS001",
    operatorId: "OP001",
    operatorName: "张三",
    date: "2025-08-15",
    shiftType: "早班",
    startTime: "08:00",
    endTime: "16:00",
    status: "scheduled",
  },
  {
    id: "SS002",
    operatorId: "OP002",
    operatorName: "李四",
    date: "2025-08-15",
    shiftType: "中班",
    startTime: "16:00",
    endTime: "00:00",
    status: "scheduled",
  },
  {
    id: "SS003",
    operatorId: "OP003",
    operatorName: "王五",
    date: "2025-08-16",
    shiftType: "早班",
    startTime: "08:00",
    endTime: "16:00",
    status: "scheduled",
  },
];

export default function TestPlanning() {
  const [selectedDate, setSelectedDate] = useState<Date | undefined>(
    new Date(),
  );
  const [testPlans, setTestPlans] = useState(mockTestPlans);
  const [productionTasks, setProductionTasks] = useState(mockProductionTasks);
  
  // 排产相关状态
  const [isProductionDialogOpen, setIsProductionDialogOpen] = useState(false);
  const [selectedNotifications, setSelectedNotifications] = useState<string[]>([]);
  const [mesNotifications] = useState(mockMESNotifications);
  
  // 排班相关状态
  const [isShiftDialogOpen, setIsShiftDialogOpen] = useState(false);
  const [operators] = useState(mockOperators);
  const [shiftSchedules, setShiftSchedules] = useState(mockShiftSchedules);
  const [selectedOperators, setSelectedOperators] = useState<string[]>([]);
  const [dateRange, setDateRange] = useState<{
    from: Date | undefined;
    to: Date | undefined;
  } | undefined>(undefined);
  const [selectedShiftType, setSelectedShiftType] = useState<"早班" | "中班" | "夜班">("早班");
  const [shiftMode, setShiftMode] = useState<"create" | "replace">("create");
  const [selectedScheduleToReplace, setSelectedScheduleToReplace] = useState<string | null>(null);
  
  // 排产任务分页和搜索状态
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize] = useState(5);
  const [searchTerm, setSearchTerm] = useState("");
  const [selectedTaskType, setSelectedTaskType] = useState<string>("all");
  const [selectedStatus, setSelectedStatus] = useState<string>("all");
  
  // 测试排产搜索状态
  const [testPlanSearchTerm, setTestPlanSearchTerm] = useState("");
  const [selectedTestType, setSelectedTestType] = useState<string>("all");
  const [selectedTestStatus, setSelectedTestStatus] = useState<string>("all");
  const [selectedTestPriority, setSelectedTestPriority] = useState<string>("all");

  const getStatusBadge = (status: string) => {
    const statusConfig = {
      pending: { label: "待开始", variant: "secondary" as const },
      running: { label: "进行中", variant: "default" as const },
      completed: { label: "已完成", variant: "secondary" as const },
      paused: { label: "已暂停", variant: "destructive" as const },
    };

    const config = statusConfig[status as keyof typeof statusConfig];
    return <Badge variant={config.variant}>{config.label}</Badge>;
  };

  const getPriorityBadge = (priority: string) => {
    const priorityConfig = {
      high: { label: "高", variant: "destructive" as const },
      medium: { label: "中", variant: "default" as const },
      low: { label: "低", variant: "secondary" as const },
    };

    const config = priorityConfig[priority as keyof typeof priorityConfig];
    return <Badge variant={config.variant}>{config.label}</Badge>;
  };

  const getTaskStatusBadge = (status: string) => {
    const statusConfig = {
      pending: { label: "待处理", variant: "secondary" as const },
      processing: { label: "处理中", variant: "default" as const },
      completed: { label: "已完成", variant: "secondary" as const },
      failed: { label: "失败", variant: "destructive" as const },
    };

    const config = statusConfig[status as keyof typeof statusConfig];
    return <Badge variant={config.variant}>{config.label}</Badge>;
  };

  const getTaskTypeBadge = (taskType: string) => {
    const typeConfig = {
      "排产": { label: "排产", variant: "default" as const },
      "取消排产": { label: "取消排产", variant: "destructive" as const },
    };

    const config = typeConfig[taskType as keyof typeof typeConfig];
    return <Badge variant={config.variant}>{config.label}</Badge>;
  };

  // 过滤和分页逻辑
  const filteredTasks = productionTasks.filter((task) => {
    const searchMatch = searchTerm === "" || 
      task.productId.toLowerCase().includes(searchTerm.toLowerCase()) ||
      task.batchNumber.toLowerCase().includes(searchTerm.toLowerCase());
    
    const typeMatch = selectedTaskType === "all" || task.taskType === selectedTaskType;
    const statusMatch = selectedStatus === "all" || task.status === selectedStatus;
    
    return searchMatch && typeMatch && statusMatch;
  });

  const totalPages = Math.ceil(filteredTasks.length / pageSize);
  const startIndex = (currentPage - 1) * pageSize;
  const endIndex = startIndex + pageSize;
  const currentTasks = filteredTasks.slice(startIndex, endIndex);

  const handlePageChange = (page: number) => {
    setCurrentPage(page);
  };

  const handleDeleteTestPlan = (planId: string) => {
    if (window.confirm("确定要删除这个测试计划吗？")) {
      setTestPlans(prev => prev.filter(plan => plan.id !== planId));
    }
  };

  // 排产相关处理函数
  const handleOpenProductionDialog = () => {
    // 默认全选所有通知
    setSelectedNotifications(mesNotifications.map(notification => notification.id));
    setIsProductionDialogOpen(true);
  };

  const handleToggleNotification = (notificationId: string) => {
    setSelectedNotifications(prev => 
      prev.includes(notificationId)
        ? prev.filter(id => id !== notificationId)
        : [...prev, notificationId]
    );
  };

  const handleSelectAll = () => {
    setSelectedNotifications(mesNotifications.map(notification => notification.id));
  };

  const handleDeselectAll = () => {
    setSelectedNotifications([]);
  };

  const handleStartProduction = () => {
    if (selectedNotifications.length === 0) {
      alert("请至少选择一个产品进行排产");
      return;
    }

    // 生成排产记录
    const newProductionTasks: ProductionTask[] = selectedNotifications.map((notificationId, index) => {
      const notification = mesNotifications.find(n => n.id === notificationId);
      if (!notification) return null;

      return {
        id: `PT${Date.now()}-${index}`,
        taskType: "排产",
        productId: notification.productId,
        batchNumber: notification.batchNumber,
        productionTime: new Date().toISOString().replace('T', ' ').substring(0, 19),
        status: "pending",
        priority: notification.priority,
        notes: `MES排产通知: ${notification.productName}`,
      };
    }).filter(Boolean) as ProductionTask[];

    // 添加到排产任务列表
    setProductionTasks(prev => [...newProductionTasks, ...prev]);
    
    // 关闭对话框并重置选择
    setIsProductionDialogOpen(false);
    setSelectedNotifications([]);
    
    alert(`成功为 ${selectedNotifications.length} 个产品创建排产任务`);
  };

  // 排班相关处理函数
  const handleOpenShiftDialog = () => {
    setIsShiftDialogOpen(true);
    setSelectedOperators([]);
    setDateRange({ from: undefined, to: undefined });
    setShiftMode("create");
    setSelectedScheduleToReplace(null);
  };

  const handleToggleOperator = (operatorId: string) => {
    setSelectedOperators(prev => 
      prev.includes(operatorId)
        ? prev.filter(id => id !== operatorId)
        : [...prev, operatorId]
    );
  };

  const handleSelectAllOperators = () => {
    setSelectedOperators(operators.map(op => op.id));
  };

  const handleDeselectAllOperators = () => {
    setSelectedOperators([]);
  };

  const handleCreateShifts = () => {
    if (selectedOperators.length === 0) {
      alert("请至少选择一个操作员");
      return;
    }

    if (!dateRange?.from || !dateRange?.to) {
      alert("请选择日期范围");
      return;
    }

    const newSchedules: ShiftSchedule[] = [];
    const currentDate = new Date(dateRange.from);
    const endDate = new Date(dateRange.to);

    while (currentDate <= endDate) {
      const dateStr = currentDate.toISOString().split('T')[0];
      
      selectedOperators.forEach((operatorId, index) => {
        const operator = operators.find(op => op.id === operatorId);
        if (!operator) return;

        const shiftTimes = {
          "早班": { start: "08:00", end: "16:00" },
          "中班": { start: "16:00", end: "00:00" },
          "夜班": { start: "00:00", end: "08:00" },
        };

        newSchedules.push({
          id: `SS${Date.now()}-${index}`,
          operatorId: operator.id,
          operatorName: operator.name,
          date: dateStr,
          shiftType: selectedShiftType,
          startTime: shiftTimes[selectedShiftType].start,
          endTime: shiftTimes[selectedShiftType].end,
          status: "scheduled",
        });
      });

      currentDate.setDate(currentDate.getDate() + 1);
    }

    setShiftSchedules(prev => [...newSchedules, ...prev]);
    alert(`成功为 ${selectedOperators.length} 个操作员创建排班`);
  };

  const handleReplaceOperator = () => {
    if (selectedOperators.length !== 1) {
      alert("请选择一个操作员进行替换");
      return;
    }

    if (!selectedScheduleToReplace) {
      alert("请选择要替换的排班记录");
      return;
    }

    const newOperator = operators.find(op => op.id === selectedOperators[0]);
    if (!newOperator) return;

    setShiftSchedules(prev => prev.map(schedule => 
      schedule.id === selectedScheduleToReplace
        ? { ...schedule, operatorId: newOperator.id, operatorName: newOperator.name }
        : schedule
    ));

    alert(`成功将排班替换为 ${newOperator.name}`);
  };

  const getOperatorStatusBadge = (status: string) => {
    const statusConfig = {
      available: { label: "可用", variant: "default" as const },
      busy: { label: "忙碌", variant: "destructive" as const },
      off: { label: "休息", variant: "secondary" as const },
    };

    const config = statusConfig[status as keyof typeof statusConfig];
    return <Badge variant={config.variant}>{config.label}</Badge>;
  };

  // 获取有测试计划的日期
  const getDatesWithPlans = () => {
    const dates = new Set<string>();
    testPlans.forEach(plan => {
      dates.add(plan.scheduledDate);
    });
    return Array.from(dates).map(dateStr => new Date(dateStr));
  };

  // 测试计划过滤逻辑
  const filteredTestPlans = testPlans.filter((plan) => {
    const searchMatch = testPlanSearchTerm === "" || 
      plan.id.toLowerCase().includes(testPlanSearchTerm.toLowerCase()) ||
      plan.productId.toLowerCase().includes(testPlanSearchTerm.toLowerCase()) ||
      plan.assignedOperator.toLowerCase().includes(testPlanSearchTerm.toLowerCase());
    
    const typeMatch = selectedTestType === "all" || plan.testType === selectedTestType;
    const statusMatch = selectedTestStatus === "all" || plan.status === selectedTestStatus;
    const priorityMatch = selectedTestPriority === "all" || plan.priority === selectedTestPriority;
    
    // 日期过滤逻辑
    const dateMatch = !selectedDate || plan.scheduledDate === selectedDate.toISOString().split('T')[0];
    
    return searchMatch && typeMatch && statusMatch && priorityMatch && dateMatch;
  });

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h2 className="text-3xl font-bold tracking-tight">测试计划管理</h2>
         <div className="flex space-x-2">
           <Button 
             onClick={handleOpenProductionDialog}
             className="bg-green-600 hover:bg-green-700"
           >
             <Factory className="mr-2 h-4 w-4" />
             排产
            </Button>
           <Button 
             onClick={handleOpenShiftDialog}
             className="bg-blue-600 hover:bg-blue-700"
           >
             <UserPlus className="mr-2 h-4 w-4" />
             人员排班
           </Button>
         </div>
       </div>

      <Tabs defaultValue="schedule" className="space-y-4">
        <TabsList>
          <TabsTrigger value="schedule">测试排产</TabsTrigger>
          <TabsTrigger value="shifts">人员排班</TabsTrigger>
          <TabsTrigger value="production">MES排产通知</TabsTrigger>
        </TabsList>

        <TabsContent value="schedule">
          <div className="space-y-6">
            {/* 搜索和筛选 */}
            <Card>
              <CardHeader>
                <CardTitle>测试计划查询</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid gap-4 md:grid-cols-5">
                <div className="space-y-2">
                    <Label htmlFor="searchTestPlan">计划编号/产品编号/操作员</Label>
                    <Input 
                      id="searchTestPlan" 
                      placeholder="搜索计划编号、产品编号或操作员"
                      value={testPlanSearchTerm}
                      onChange={(e) => setTestPlanSearchTerm(e.target.value)}
                    />
                </div>
                <div className="space-y-2">
                    <Label htmlFor="filterTestType">测试类型</Label>
                    <Select value={selectedTestType} onValueChange={setSelectedTestType}>
                    <SelectTrigger>
                      <SelectValue placeholder="选择测试类型" />
                    </SelectTrigger>
                    <SelectContent>
                        <SelectItem value="all">全部</SelectItem>
                        <SelectItem value="月稳测试">月稳测试</SelectItem>
                        <SelectItem value="临时测试">临时测试</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <div className="space-y-2">
                    <Label htmlFor="filterTestStatus">状态</Label>
                    <Select value={selectedTestStatus} onValueChange={setSelectedTestStatus}>
                    <SelectTrigger>
                        <SelectValue placeholder="选择状态" />
                    </SelectTrigger>
                    <SelectContent>
                        <SelectItem value="all">全部</SelectItem>
                        <SelectItem value="pending">待开始</SelectItem>
                        <SelectItem value="running">进行中</SelectItem>
                        <SelectItem value="completed">已完成</SelectItem>
                        <SelectItem value="paused">已暂停</SelectItem>
                    </SelectContent>
                  </Select>
              </div>
              <div className="space-y-2">
                    <Label htmlFor="filterTestPriority">优先级</Label>
                    <Select value={selectedTestPriority} onValueChange={setSelectedTestPriority}>
                  <SelectTrigger>
                    <SelectValue placeholder="选择优先级" />
                  </SelectTrigger>
                  <SelectContent>
                        <SelectItem value="all">全部</SelectItem>
                    <SelectItem value="high">高</SelectItem>
                    <SelectItem value="medium">中</SelectItem>
                    <SelectItem value="low">低</SelectItem>
                  </SelectContent>
                </Select>
              </div>
                  <div className="flex items-end">
                    <Button className="w-full bg-blue-600 hover:bg-blue-700">
                      <Search className="mr-2 h-4 w-4" />
                      查询
                    </Button>
            </div>
            </div>
              </CardContent>
            </Card>

          <div className="grid gap-6 md:grid-cols-3">
            <Card className="md:col-span-2">
              <CardHeader>
                  <CardTitle>
                    测试计划列表
                    {selectedDate && (
                      <span className="text-sm font-normal text-gray-500 ml-2">
                        - {selectedDate.toLocaleDateString('zh-CN')}
                      </span>
                    )}
                  </CardTitle>
              </CardHeader>
              <CardContent>
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>计划编号</TableHead>
                      <TableHead>产品编号</TableHead>
                      <TableHead>测试类型</TableHead>
                      <TableHead>计划日期</TableHead>
                      <TableHead>优先级</TableHead>
                      <TableHead>状态</TableHead>
                      <TableHead>操作</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                      {filteredTestPlans.map((plan) => (
                      <TableRow key={plan.id}>
                        <TableCell className="font-medium">{plan.id}</TableCell>
                        <TableCell>{plan.productId}</TableCell>
                        <TableCell>{plan.testType}</TableCell>
                        <TableCell>{plan.scheduledDate}</TableCell>
                        <TableCell>{getPriorityBadge(plan.priority)}</TableCell>
                        <TableCell>{getStatusBadge(plan.status)}</TableCell>
                        <TableCell>
                          <div className="flex space-x-2">
                            <Button size="sm" variant="outline">
                              <Eye className="h-3 w-3" />
                            </Button>
                            {plan.status === "running" && (
                              <Button size="sm" variant="outline">
                                <Pause className="h-3 w-3" />
                              </Button>
                            )}
                              <Button 
                                size="sm" 
                                variant="outline" 
                                className="border-red-200 hover:bg-red-50 hover:text-red-700"
                                onClick={() => handleDeleteTestPlan(plan.id)}
                              >
                                <Trash2 className="h-3 w-3" />
                              </Button>
                          </div>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <CalendarIcon className="mr-2 h-4 w-4" />
                  日历视图
                </CardTitle>
              </CardHeader>
              <CardContent>
                <Calendar
                  mode="single"
                  selected={selectedDate}
                  onSelect={setSelectedDate}
                  className="rounded-md border"
                    modifiers={{
                      hasPlans: getDatesWithPlans(),
                    }}
                    modifiersStyles={{
                      hasPlans: {
                        backgroundColor: "#3b82f6",
                        color: "white",
                        fontWeight: "bold",
                      },
                    }}
                  />
                  <div className="mt-4 text-sm text-gray-600">
                    <div className="flex items-center space-x-2 mb-2">
                      <div className="w-3 h-3 bg-blue-500 rounded-full"></div>
                      <span>有测试计划的日期</span>
                    </div>
                    {selectedDate && (
                      <div className="text-xs">
                        已选择: {selectedDate.toLocaleDateString('zh-CN')}
                        <Button 
                          variant="link" 
                          size="sm" 
                          className="ml-2 p-0 h-auto text-xs"
                          onClick={() => setSelectedDate(undefined)}
                        >
                          清除选择
                        </Button>
                      </div>
                    )}
                  </div>
              </CardContent>
            </Card>
            </div>
          </div>
        </TabsContent>

        <TabsContent value="shifts">
          <div className="grid gap-6 md:grid-cols-2">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <Users className="mr-2 h-4 w-4" />
                  班次安排
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {mockShifts.map((shift) => (
                    <div key={shift.id} className="border rounded-lg p-4">
                      <div className="flex items-center justify-between mb-2">
                        <h4 className="font-semibold">{shift.name}</h4>
                        <span className="text-sm text-gray-500">
                          {shift.startTime} - {shift.endTime}
                        </span>
                      </div>
                      <div className="flex flex-wrap gap-2">
                        {shift.operators.map((operator) => (
                          <Badge key={operator} variant="outline">
                            {operator}
                          </Badge>
                        ))}
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>操作员工作负荷</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="flex items-center justify-between">
                    <span>张三</span>
                    <div className="flex items-center space-x-2">
                      <div className="w-32 bg-gray-200 rounded-full h-2">
                        <div
                          className="bg-blue-600 h-2 rounded-full"
                          style={{ width: "75%" }}
                        ></div>
                      </div>
                      <span className="text-sm">3/4</span>
                    </div>
                  </div>
                  <div className="flex items-center justify-between">
                    <span>李四</span>
                    <div className="flex items-center space-x-2">
                      <div className="w-32 bg-gray-200 rounded-full h-2">
                        <div
                          className="bg-blue-600 h-2 rounded-full"
                          style={{ width: "50%" }}
                        ></div>
                      </div>
                      <span className="text-sm">2/4</span>
                    </div>
                  </div>
                  <div className="flex items-center justify-between">
                    <span>王五</span>
                    <div className="flex items-center space-x-2">
                      <div className="w-32 bg-gray-200 rounded-full h-2">
                        <div
                          className="bg-blue-600 h-2 rounded-full"
                          style={{ width: "100%" }}
                        ></div>
                      </div>
                      <span className="text-sm">4/4</span>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

 
        <TabsContent value="production">
          <div className="space-y-6">
            {/* 搜索和筛选 */}
              <Card>
                <CardHeader>
                <CardTitle>MES排产通知查询</CardTitle>
                </CardHeader>
                <CardContent>
                <div className="grid gap-4 md:grid-cols-4">
                  <div className="space-y-2">
                    <Label htmlFor="searchTask">产品编号/批次号</Label>
                    <Input 
                      id="searchTask" 
                      placeholder="搜索产品编号或批次号"
                      value={searchTerm}
                      onChange={(e) => setSearchTerm(e.target.value)}
                    />
                    </div>
                  <div className="space-y-2">
                    <Label htmlFor="filterTaskType">任务类型</Label>
                    <Select value={selectedTaskType} onValueChange={setSelectedTaskType}>
                      <SelectTrigger>
                        <SelectValue placeholder="选择任务类型" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="all">全部</SelectItem>
                        <SelectItem value="排产">排产</SelectItem>
                        <SelectItem value="取消排产">取消排产</SelectItem>
                      </SelectContent>
                    </Select>
                    </div>
                  <div className="space-y-2">
                    <Label htmlFor="filterStatus">状态</Label>
                    <Select value={selectedStatus} onValueChange={setSelectedStatus}>
                      <SelectTrigger>
                        <SelectValue placeholder="选择状态" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="all">全部</SelectItem>
                        <SelectItem value="pending">待处理</SelectItem>
                        <SelectItem value="completed">已处理</SelectItem>
                      </SelectContent>
                    </Select>
                    </div>
                  <div className="flex items-end">
                    <Button className="w-full bg-blue-600 hover:bg-blue-700">
                      <Search className="mr-2 h-4 w-4" />
                      查询
                    </Button>
                    </div>
                  </div>
                </CardContent>
              </Card>

            {/* 排产任务列表 */}
              <Card>
                <CardHeader>
                <CardTitle>排产任务列表</CardTitle>
                </CardHeader>
                <CardContent>
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>任务编号</TableHead>
                      <TableHead>任务类型</TableHead>
                      <TableHead>产品编号</TableHead>
                      <TableHead>批次号</TableHead>
                      <TableHead>排产时间</TableHead>
                      <TableHead>优先级</TableHead>
                      <TableHead>状态</TableHead>
                      <TableHead>操作员</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {currentTasks.map((task) => (
                      <TableRow key={task.id}>
                        <TableCell className="font-medium">{task.id}</TableCell>
                        <TableCell>{getTaskTypeBadge(task.taskType)}</TableCell>
                        <TableCell>{task.productId}</TableCell>
                        <TableCell>{task.batchNumber}</TableCell>
                        <TableCell>{task.productionTime}</TableCell>
                        <TableCell>{getPriorityBadge(task.priority)}</TableCell>
                        <TableCell>{getTaskStatusBadge(task.status)}</TableCell>
                        <TableCell>{task.operator || "-"}</TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>

                {/* 分页控件 */}
                {totalPages > 1 && (
                  <div className="flex items-center justify-between mt-4">
                    <div className="text-sm text-gray-500">
                      显示 {startIndex + 1} - {Math.min(endIndex, filteredTasks.length)} 条，共 {filteredTasks.length} 条
                    </div>
                    <div className="flex items-center space-x-2">
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => handlePageChange(currentPage - 1)}
                        disabled={currentPage === 1}
                      >
                        上一页
                      </Button>
                      <div className="flex items-center space-x-1">
                        {Array.from({ length: totalPages }, (_, i) => i + 1).map((page) => (
                          <Button
                            key={page}
                            variant={currentPage === page ? "default" : "outline"}
                            size="sm"
                            onClick={() => handlePageChange(page)}
                            className="w-8 h-8 p-0"
                          >
                            {page}
                          </Button>
                        ))}
                    </div>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => handlePageChange(currentPage + 1)}
                        disabled={currentPage === totalPages}
                      >
                        下一页
                      </Button>
                    </div>
                    </div>
                )}
                </CardContent>
              </Card>
            </div>
        </TabsContent>
      </Tabs>

      {/* MES排产通知对话框 */}
      <Dialog open={isProductionDialogOpen} onOpenChange={setIsProductionDialogOpen}>
        <DialogContent className="max-w-6xl max-h-[80vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle className="flex items-center gap-2">
              <Play className="h-5 w-5" />
              MES排产通知列表
            </DialogTitle>
            <DialogDescription>
              选择需要排产的产品，系统将根据选择生成对应的排产记录
            </DialogDescription>
          </DialogHeader>
          
          <div className="space-y-4">
            {/* 操作按钮 */}
            <div className="flex justify-between items-center">
              <div className="flex space-x-2">
                <Button variant="outline" size="sm" onClick={handleSelectAll}>
                  <CheckSquare className="mr-2 h-4 w-4" />
                  全选
                </Button>
                <Button variant="outline" size="sm" onClick={handleDeselectAll}>
                  <Square className="mr-2 h-4 w-4" />
                  取消全选
                </Button>
              </div>
              <div className="text-sm text-gray-600">
                已选择 {selectedNotifications.length} / {mesNotifications.length} 项
              </div>
            </div>

            {/* 通知列表 */}
            <div className="border rounded-lg">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead className="w-12">
                      <input
                        type="checkbox"
                        checked={selectedNotifications.length === mesNotifications.length}
                        onChange={(e) => e.target.checked ? handleSelectAll() : handleDeselectAll()}
                        className="rounded"
                      />
                    </TableHead>
                    <TableHead>产品编号</TableHead>
                    <TableHead>产品名称</TableHead>
                    <TableHead>批次号</TableHead>
                    <TableHead>测试类型</TableHead>
                    <TableHead>优先级</TableHead>
                    <TableHead>预计时长</TableHead>
                    <TableHead>交期</TableHead>
                    <TableHead>状态</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {mesNotifications.map((notification) => (
                    <TableRow key={notification.id}>
                    <TableCell>
                        <input
                          type="checkbox"
                          checked={selectedNotifications.includes(notification.id)}
                          onChange={() => handleToggleNotification(notification.id)}
                          className="rounded"
                        />
                    </TableCell>
                      <TableCell className="font-medium">{notification.productId}</TableCell>
                      <TableCell>{notification.productName}</TableCell>
                      <TableCell>{notification.batchNumber}</TableCell>
                      <TableCell>{notification.testType}</TableCell>
                      <TableCell>{getPriorityBadge(notification.priority)}</TableCell>
                      <TableCell>{notification.estimatedDuration}</TableCell>
                      <TableCell>{notification.dueDate}</TableCell>
                    <TableCell>
                        <Badge variant="secondary">
                          {notification.status === "pending" ? "待处理" : 
                           notification.status === "approved" ? "已批准" : "已拒绝"}
                        </Badge>
                    </TableCell>
                  </TableRow>
                  ))}
                </TableBody>
              </Table>
            </div>

            {/* 操作按钮 */}
            <div className="flex justify-end space-x-2 pt-4 border-t">
              <Button variant="outline" onClick={() => setIsProductionDialogOpen(false)}>
                取消
              </Button>
              <Button 
                onClick={handleStartProduction}
                disabled={selectedNotifications.length === 0}
                className="bg-green-600 hover:bg-green-700"
              >
                <Play className="mr-2 h-4 w-4" />
                立即排产
              </Button>
            </div>
          </div>
                 </DialogContent>
       </Dialog>

       {/* 人员排班对话框 */}
       <Dialog open={isShiftDialogOpen} onOpenChange={setIsShiftDialogOpen}>
         <DialogContent className="max-w-7xl max-h-[90vh] overflow-y-auto">
           <DialogHeader>
             <DialogTitle className="flex items-center gap-2">
               <UserPlus className="h-5 w-5" />
               人员排班管理
             </DialogTitle>
             <DialogDescription>
               选择操作员和日期范围进行排班，或替换现有排班记录
             </DialogDescription>
           </DialogHeader>
           
           <div className="space-y-6">
             {/* 排班模式选择 */}
             <div className="flex space-x-4">
               <Button
                 variant={shiftMode === "create" ? "default" : "outline"}
                 onClick={() => setShiftMode("create")}
               >
                 <CalendarIcon className="mr-2 h-4 w-4" />
                 创建排班
               </Button>
               <Button
                 variant={shiftMode === "replace" ? "default" : "outline"}
                 onClick={() => setShiftMode("replace")}
               >
                 <Clock className="mr-2 h-4 w-4" />
                 替换人员
               </Button>
             </div>

             <div className="grid gap-6 md:grid-cols-2">
               {/* 左侧：人员列表 */}
               <Card>
                 <CardHeader>
                   <CardTitle className="flex items-center justify-between">
                     <span>操作员列表</span>
                     <div className="flex space-x-2">
                       <Button variant="outline" size="sm" onClick={handleSelectAllOperators}>
                         <CheckSquare className="mr-2 h-4 w-4" />
                         全选
                       </Button>
                       <Button variant="outline" size="sm" onClick={handleDeselectAllOperators}>
                         <Square className="mr-2 h-4 w-4" />
                         取消全选
                       </Button>
                     </div>
                   </CardTitle>
                 </CardHeader>
                 <CardContent>
                   <div className="space-y-3">
                     {operators.map((operator) => (
                       <div key={operator.id} className="flex items-center space-x-3 p-3 border rounded-lg">
                         <input
                           type="checkbox"
                           checked={selectedOperators.includes(operator.id)}
                           onChange={() => handleToggleOperator(operator.id)}
                           className="rounded"
                         />
                         <div className="flex-1">
                           <div className="flex items-center justify-between">
                             <span className="font-medium">{operator.name}</span>
                             {getOperatorStatusBadge(operator.status)}
                           </div>
                           <div className="text-sm text-gray-500">
                             <div>部门: {operator.department}</div>
                             <div>技能: {operator.skills.join(", ")}</div>
                             <div>工作负荷: {operator.currentWorkload}/{operator.maxWorkload}</div>
                           </div>
                         </div>
                       </div>
                     ))}
                   </div>
            </CardContent>
          </Card>

               {/* 右侧：日历和排班设置 */}
               <Card>
                 <CardHeader>
                   <CardTitle>排班设置</CardTitle>
                 </CardHeader>
                 <CardContent>
                   {shiftMode === "create" ? (
                     <div className="space-y-4">
                       {/* 日期范围选择 */}
                       <div className="space-y-2">
                         <Label>选择日期范围</Label>
                         <Calendar
                           mode="range"
                           selected={dateRange}
                           onSelect={(range) => {
                             if (range?.from && range?.to) {
                               setDateRange({ from: range.from, to: range.to });
                             } else {
                               setDateRange(undefined);
                             }
                           }}
                           className="rounded-md border"
                         />
                       </div>

                       {/* 班次类型选择 */}
                       <div className="space-y-2">
                         <Label>班次类型</Label>
                         <Select value={selectedShiftType} onValueChange={(value: "早班" | "中班" | "夜班") => setSelectedShiftType(value)}>
                           <SelectTrigger>
                             <SelectValue />
                           </SelectTrigger>
                           <SelectContent>
                             <SelectItem value="早班">早班 (08:00-16:00)</SelectItem>
                             <SelectItem value="中班">中班 (16:00-00:00)</SelectItem>
                             <SelectItem value="夜班">夜班 (00:00-08:00)</SelectItem>
                           </SelectContent>
                         </Select>
                       </div>

                       {/* 创建排班按钮 */}
                       <Button 
                         onClick={handleCreateShifts}
                         disabled={selectedOperators.length === 0 || !dateRange.from || !dateRange.to}
                         className="w-full bg-green-600 hover:bg-green-700"
                       >
                         <CalendarIcon className="mr-2 h-4 w-4" />
                         创建排班
                       </Button>
                     </div>
                   ) : (
                     <div className="space-y-4">
                       {/* 现有排班列表 */}
                       <div className="space-y-2">
                         <Label>选择要替换的排班记录</Label>
                         <div className="max-h-60 overflow-y-auto space-y-2">
                           {shiftSchedules.map((schedule) => (
                             <div 
                               key={schedule.id} 
                               className={`p-3 border rounded-lg cursor-pointer ${
                                 selectedScheduleToReplace === schedule.id ? 'border-blue-500 bg-blue-50' : ''
                               }`}
                               onClick={() => setSelectedScheduleToReplace(schedule.id)}
                             >
                               <div className="flex justify-between items-center">
                                 <span className="font-medium">{schedule.operatorName}</span>
                                 <Badge variant="outline">{schedule.shiftType}</Badge>
                               </div>
                               <div className="text-sm text-gray-500">
                                 {schedule.date} {schedule.startTime}-{schedule.endTime}
                               </div>
                             </div>
                           ))}
                         </div>
                       </div>

                       {/* 替换人员按钮 */}
                       <Button 
                         onClick={handleReplaceOperator}
                         disabled={selectedOperators.length !== 1 || !selectedScheduleToReplace}
                         className="w-full bg-blue-600 hover:bg-blue-700"
                       >
                         <UserPlus className="mr-2 h-4 w-4" />
                         替换人员
                       </Button>
                     </div>
                   )}
                 </CardContent>
               </Card>
             </div>

             {/* 操作按钮 */}
             <div className="flex justify-end space-x-2 pt-4 border-t">
               <Button variant="outline" onClick={() => setIsShiftDialogOpen(false)}>
                 关闭
               </Button>
             </div>
           </div>
         </DialogContent>
       </Dialog>
    </div>
  );
}
