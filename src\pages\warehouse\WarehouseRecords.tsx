import { useState, useEffect } from "react";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import { Progress } from "@/components/ui/progress";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { StatusChart } from "@/components/StatusChart";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import {
  Package,
  ArrowUpCircle,
  ArrowDownCircle,
  Clock,
  User,
  MapPin,
  Barcode,
  Search,
  Filter,
  Download,
  RefreshCw,
  TrendingUp,
  Calendar,
  CheckCircle,
  AlertTriangle,
  Eye,
} from "lucide-react";

interface WarehouseRecord {
  id: string;
  type: "inbound" | "outbound";
  productId: string;
  productName: string;
  batchNumber: string;
  quantity: number;
  location: string;
  operator: string;
  timestamp: string;
  status: "completed" | "pending" | "error";
  amrId?: string;
  reason: string;
  notes?: string;
}

interface DailyStats {
  date: string;
  inbound: number;
  outbound: number;
  efficiency: number;
  inboundEfficiency: number;
  outboundEfficiency: number;
}

export default function WarehouseRecords() {
  const [selectedTimeRange, setSelectedTimeRange] = useState("7d");
  const [selectedType, setSelectedType] = useState("all");
  const [searchTerm, setSearchTerm] = useState("");
  const [selectedRecord, setSelectedRecord] = useState<WarehouseRecord | null>(
    null,
  );

  // 模拟出入库记录数据
  const [records] = useState<WarehouseRecord[]>([
    {
      id: "WR-2024-001",
      type: "inbound",
      productId: "A-2024-001",
      productName: "产品A-型号X1",
      batchNumber: "BATCH-240110-001",
      quantity: 50,
      location: "A区-01-02",
      operator: "张三",
      timestamp: "2024-01-10 14:23:15",
      status: "completed",
      amrId: "AMR-01",
      reason: "生产入库",
      notes: "质检通过，正常入库",
    },
    {
      id: "WR-2024-002",
      type: "outbound",
      productId: "A-2024-005",
      productName: "产品A-型号X2",
      batchNumber: "BATCH-240108-003",
      quantity: 30,
      location: "B区-01-01",
      operator: "李四",
      timestamp: "2024-01-10 13:45:22",
      status: "completed",
      amrId: "AMR-02",
      reason: "测试出库",
      notes: "送往测试台-03",
    },
    {
      id: "WR-2024-003",
      type: "inbound",
      productId: "A-2024-012",
      productName: "产品A-型号Y1",
      batchNumber: "BATCH-240109-005",
      quantity: 75,
      location: "C区-01-01",
      operator: "王五",
      timestamp: "2024-01-10 11:30:08",
      status: "completed",
      amrId: "AMR-03",
      reason: "生产入库",
      notes: "",
    },
    {
      id: "WR-2024-004",
      type: "outbound",
      productId: "A-2024-018",
      productName: "产品A-型号Z1",
      batchNumber: "BATCH-240107-002",
      quantity: 20,
      location: "A区-04-01",
      operator: "赵六",
      timestamp: "2024-01-10 09:15:33",
      status: "pending",
      amrId: "AMR-04",
      reason: "客户发货",
      notes: "等待质检确认",
    },
    {
      id: "WR-2024-005",
      type: "inbound",
      productId: "A-2024-025",
      productName: "产品A-型号X3",
      batchNumber: "BATCH-240110-008",
      quantity: 100,
      location: "A区-05-01",
      operator: "孙七",
      timestamp: "2024-01-10 08:22:41",
      status: "error",
      amrId: "AMR-01",
      reason: "生产入库",
      notes: "位置冲突，需要重新分配",
    },
    {
      id: "WR-2024-006",
      type: "outbound",
      productId: "A-2024-009",
      productName: "产品A-型号Y2",
      batchNumber: "BATCH-240106-004",
      quantity: 45,
      location: "B区-03-01",
      operator: "周八",
      timestamp: "2024-01-09 16:45:18",
      status: "completed",
      amrId: "AMR-05",
      reason: "测试出库",
      notes: "月稳测试用",
    },
  ]);

  // 每日统计数据
  const [dailyStats] = useState<DailyStats[]>([
    { date: "01-04", inbound: 245, outbound: 180, efficiency: 92, inboundEfficiency: 94, outboundEfficiency: 89 },
    { date: "01-05", inbound: 280, outbound: 220, efficiency: 88, inboundEfficiency: 91, outboundEfficiency: 85 },
    { date: "01-06", inbound: 195, outbound: 165, efficiency: 94, inboundEfficiency: 96, outboundEfficiency: 92 },
    { date: "01-07", inbound: 320, outbound: 285, efficiency: 86, inboundEfficiency: 88, outboundEfficiency: 84 },
    { date: "01-08", inbound: 275, outbound: 190, efficiency: 90, inboundEfficiency: 93, outboundEfficiency: 87 },
    { date: "01-09", inbound: 210, outbound: 155, efficiency: 91, inboundEfficiency: 94, outboundEfficiency: 88 },
    { date: "01-10", inbound: 195, outbound: 115, efficiency: 89, inboundEfficiency: 92, outboundEfficiency: 86 },
  ]);

  const filteredRecords = records.filter((record) => {
    const typeMatch = selectedType === "all" || record.type === selectedType;
    const searchMatch =
      searchTerm === "" ||
      record.productId.toLowerCase().includes(searchTerm.toLowerCase()) ||
      record.productName.toLowerCase().includes(searchTerm.toLowerCase()) ||
      record.batchNumber.toLowerCase().includes(searchTerm.toLowerCase()) ||
      record.operator.toLowerCase().includes(searchTerm.toLowerCase());
    return typeMatch && searchMatch;
  });

  const getStatusBadge = (status: string) => {
    const statusConfig = {
      completed: {
        label: "已完成",
        className: "bg-green-100 text-green-800 border-green-200",
      },
      pending: {
        label: "进行中",
        className: "bg-yellow-100 text-yellow-800 border-yellow-200",
      },
      error: {
        label: "异常",
        className: "bg-red-100 text-red-800 border-red-200",
      },
    };

    const config = statusConfig[status as keyof typeof statusConfig];
    return (
      <Badge className={`${config.className} border`}>{config.label}</Badge>
    );
  };

  const getTypeIcon = (type: string) => {
    return type === "inbound" ? (
      <ArrowDownCircle className="h-4 w-4 text-green-600" />
    ) : (
      <ArrowUpCircle className="h-4 w-4 text-blue-600" />
    );
  };

  const totalInbound = records.filter((r) => r.type === "inbound").length;
  const totalOutbound = records.filter((r) => r.type === "outbound").length;
  const pendingRecords = records.filter((r) => r.status === "pending").length;
  const errorRecords = records.filter((r) => r.status === "error").length;

  return (
    <div className="min-h-screen bg-gray-50 text-gray-900 p-6">
      <div className="space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div>
            <h2 className="text-3xl font-bold tracking-tight bg-gradient-to-r from-blue-600 to-cyan-600 bg-clip-text text-transparent">
              出入库记录管理
            </h2>
            <p className="text-gray-600 mt-2">
              智能仓储系统出入库操作记录与数据分析
            </p>
          </div>
          <div className="flex items-center space-x-3">
            <Select
              value={selectedTimeRange}
              onValueChange={setSelectedTimeRange}
            >
              <SelectTrigger className="w-32 bg-white border-gray-300 text-gray-900">
                <SelectValue />
              </SelectTrigger>
              <SelectContent className="bg-white border-gray-300">
                <SelectItem value="24h">24小时</SelectItem>
                <SelectItem value="7d">7天</SelectItem>
                <SelectItem value="30d">30天</SelectItem>
                <SelectItem value="90d">90天</SelectItem>
              </SelectContent>
            </Select>

            <Button
              variant="outline"
              size="sm"
              className="border-gray-300 bg-white text-gray-700 hover:bg-gray-50"
            >
              <RefreshCw className="h-4 w-4 mr-2" />
              刷新
            </Button>

            <Button
              variant="outline"
              size="sm"
              className="border-gray-300 bg-white text-gray-700 hover:bg-gray-50"
            >
              <Download className="h-4 w-4 mr-2" />
              导出
            </Button>
          </div>
        </div>

        {/* Statistics Cards */}
        <div className="grid gap-6 md:grid-cols-4">
          <Card className="bg-white border-gray-200 shadow-sm">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium text-gray-600">
                今日入库
              </CardTitle>
              <ArrowDownCircle className="h-4 w-4 text-green-600" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-green-600">
                {totalInbound}
              </div>
              <p className="text-xs text-gray-500">+12% 较昨日</p>
            </CardContent>
          </Card>

          <Card className="bg-white border-gray-200 shadow-sm">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium text-gray-600">
                今日出库
              </CardTitle>
              <ArrowUpCircle className="h-4 w-4 text-blue-600" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-blue-600">
                {totalOutbound}
              </div>
              <p className="text-xs text-gray-500">-5% 较昨日</p>
            </CardContent>
          </Card>

          <Card className="bg-white border-gray-200 shadow-sm">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium text-gray-600">
                待处理
              </CardTitle>
              <Clock className="h-4 w-4 text-yellow-600" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-yellow-600">
                {pendingRecords}
              </div>
              <p className="text-xs text-gray-500">需要关注</p>
            </CardContent>
          </Card>

          <Card className="bg-white border-gray-200 shadow-sm">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium text-gray-600">
                异常记录
              </CardTitle>
              <AlertTriangle className="h-4 w-4 text-red-600" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-red-600">
                {errorRecords}
              </div>
              <p className="text-xs text-gray-500">需要处理</p>
            </CardContent>
          </Card>
        </div>

        {/* Main Content */}
        <Tabs defaultValue="records" className="space-y-4">
          <TabsList className="bg-white border-gray-200">
            <TabsTrigger
              value="records"
              className="data-[state=active]:bg-blue-50 data-[state=active]:text-blue-700 text-gray-600"
            >
              操作记录
            </TabsTrigger>
            <TabsTrigger
              value="statistics"
              className="data-[state=active]:bg-blue-50 data-[state=active]:text-blue-700 text-gray-600"
            >
              数据统计
            </TabsTrigger>
            <TabsTrigger
              value="analysis"
              className="data-[state=active]:bg-blue-50 data-[state=active]:text-blue-700 text-gray-600"
            >
              趋势分析
            </TabsTrigger>
          </TabsList>

          {/* Records Tab */}
          <TabsContent value="records" className="space-y-4">
            {/* Search and Filter */}
            <Card className="bg-white border-gray-200 shadow-sm">
              <CardHeader>
                <CardTitle className="text-gray-900">查询条件</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid gap-4 md:grid-cols-4">
                  <div className="space-y-2">
                    <label className="text-sm font-medium text-gray-700">
                      搜索关键词
                    </label>
                    <div className="relative">
                      <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                      <Input
                        placeholder="产品ID、批次号、操作员..."
                        value={searchTerm}
                        onChange={(e) => setSearchTerm(e.target.value)}
                        className="pl-10 bg-white border-gray-300 text-gray-900 placeholder-gray-400"
                      />
                    </div>
                  </div>

                  <div className="space-y-2">
                    <label className="text-sm font-medium text-gray-700">
                      操作类型
                    </label>
                    <Select
                      value={selectedType}
                      onValueChange={setSelectedType}
                    >
                      <SelectTrigger className="bg-white border-gray-300 text-gray-900">
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent className="bg-white border-gray-300">
                        <SelectItem value="all">全部操作</SelectItem>
                        <SelectItem value="inbound">入库操作</SelectItem>
                        <SelectItem value="outbound">出库操作</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>

                  <div className="space-y-2">
                    <label className="text-sm font-medium text-gray-700">
                      状态筛选
                    </label>
                    <Select>
                      <SelectTrigger className="bg-white border-gray-300 text-gray-900">
                        <SelectValue placeholder="选择状态" />
                      </SelectTrigger>
                      <SelectContent className="bg-white border-gray-300">
                        <SelectItem value="all">全部状态</SelectItem>
                        <SelectItem value="completed">已完成</SelectItem>
                        <SelectItem value="pending">进行中</SelectItem>
                        <SelectItem value="error">异常</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>

                  <div className="flex items-end">
                    <Button className="w-full bg-blue-600 hover:bg-blue-700">
                      <Filter className="mr-2 h-4 w-4" />
                      筛选
                    </Button>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Records Table */}
            <Card className="bg-white border-gray-200 shadow-sm">
              <CardHeader>
                <CardTitle className="text-gray-900">出入库记录</CardTitle>
                <CardDescription className="text-gray-600">
                  显示 {filteredRecords.length} 条记录
                </CardDescription>
              </CardHeader>
              <CardContent>
                <Table>
                  <TableHeader>
                    <TableRow className="border-gray-200">
                      <TableHead className="text-gray-700">操作类型</TableHead>
                      <TableHead className="text-gray-700">产品信息</TableHead>
                      <TableHead className="text-gray-700">批次号</TableHead>
                      <TableHead className="text-gray-700">数量</TableHead>
                      <TableHead className="text-gray-700">位置</TableHead>
                      <TableHead className="text-gray-700">操作员</TableHead>
                      <TableHead className="text-gray-700">时间</TableHead>
                      <TableHead className="text-gray-700">状态</TableHead>
                      <TableHead className="text-gray-700">操作</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {filteredRecords.map((record) => (
                      <TableRow
                        key={record.id}
                        className="border-gray-200 hover:bg-gray-50"
                      >
                        <TableCell>
                          <div className="flex items-center space-x-2">
                            {getTypeIcon(record.type)}
                            <span className="text-gray-900">
                              {record.type === "inbound" ? "入库" : "出库"}
                            </span>
                          </div>
                        </TableCell>
                        <TableCell>
                          <div className="space-y-1">
                            <div className="font-medium text-gray-900">
                              {record.productId}
                            </div>
                            <div className="text-xs text-gray-500">
                              {record.productName}
                            </div>
                          </div>
                        </TableCell>
                        <TableCell>
                          <code className="text-xs bg-gray-100 px-2 py-1 rounded text-gray-700">
                            {record.batchNumber}
                          </code>
                        </TableCell>
                        <TableCell className="text-gray-900">
                          {record.quantity}
                        </TableCell>
                        <TableCell>
                          <div className="flex items-center space-x-1">
                            <MapPin className="h-3 w-3 text-gray-400" />
                            <span className="text-gray-700 text-sm">
                              {record.location}
                            </span>
                          </div>
                        </TableCell>
                        <TableCell>
                          <div className="flex items-center space-x-1">
                            <User className="h-3 w-3 text-gray-400" />
                            <span className="text-gray-700">
                              {record.operator}
                            </span>
                          </div>
                        </TableCell>
                        <TableCell className="text-gray-700 text-sm">
                          {record.timestamp}
                        </TableCell>
                        <TableCell>{getStatusBadge(record.status)}</TableCell>
                        <TableCell>
                          <Dialog>
                            <DialogTrigger asChild>
                              <Button
                                variant="outline"
                                size="sm"
                                className="border-gray-300 bg-white text-gray-700 hover:bg-gray-50"
                                onClick={() => setSelectedRecord(record)}
                              >
                                <Eye className="h-3 w-3" />
                              </Button>
                            </DialogTrigger>
                            <DialogContent className="bg-white border-gray-200 text-gray-900">
                              <DialogHeader>
                                <DialogTitle className="text-gray-900">
                                  记录详情
                                </DialogTitle>
                                <DialogDescription className="text-gray-600">
                                  查看出入库操作的详细信息
                                </DialogDescription>
                              </DialogHeader>
                              {selectedRecord && (
                                <div className="space-y-4">
                                  <div className="grid grid-cols-2 gap-4">
                                    <div>
                                      <label className="text-sm font-medium text-gray-700">
                                        操作类型
                                      </label>
                                      <div className="flex items-center space-x-2 mt-1">
                                        {getTypeIcon(selectedRecord.type)}
                                        <span>
                                          {selectedRecord.type === "inbound"
                                            ? "入库操作"
                                            : "出库操作"}
                                        </span>
                                      </div>
                                    </div>
                                    <div>
                                      <label className="text-sm font-medium text-gray-700">
                                        状态
                                      </label>
                                      <div className="mt-1">
                                        {getStatusBadge(selectedRecord.status)}
                                      </div>
                                    </div>
                                    <div>
                                      <label className="text-sm font-medium text-gray-700">
                                        产品ID
                                      </label>
                                      <p className="text-gray-900">
                                        {selectedRecord.productId}
                                      </p>
                                    </div>
                                    <div>
                                      <label className="text-sm font-medium text-gray-700">
                                        产品名称
                                      </label>
                                      <p className="text-gray-900">
                                        {selectedRecord.productName}
                                      </p>
                                    </div>
                                    <div>
                                      <label className="text-sm font-medium text-gray-700">
                                        批次号
                                      </label>
                                      <p className="text-gray-900">
                                        {selectedRecord.batchNumber}
                                      </p>
                                    </div>
                                    <div>
                                      <label className="text-sm font-medium text-gray-700">
                                        数量
                                      </label>
                                      <p className="text-gray-900">
                                        {selectedRecord.quantity}
                                      </p>
                                    </div>
                                    <div>
                                      <label className="text-sm font-medium text-gray-700">
                                        存储位置
                                      </label>
                                      <p className="text-gray-900">
                                        {selectedRecord.location}
                                      </p>
                                    </div>
                                    <div>
                                      <label className="text-sm font-medium text-gray-700">
                                        操作员
                                      </label>
                                      <p className="text-gray-900">
                                        {selectedRecord.operator}
                                      </p>
                                    </div>
                                    <div>
                                      <label className="text-sm font-medium text-gray-700">
                                        AMR设备
                                      </label>
                                      <p className="text-gray-900">
                                        {selectedRecord.amrId}
                                      </p>
                                    </div>
                                    <div>
                                      <label className="text-sm font-medium text-gray-700">
                                        操作时间
                                      </label>
                                      <p className="text-gray-900">
                                        {selectedRecord.timestamp}
                                      </p>
                                    </div>
                                  </div>
                                  <div>
                                    <label className="text-sm font-medium text-gray-700">
                                      操作原因
                                    </label>
                                    <p className="text-gray-900 mt-1">
                                      {selectedRecord.reason}
                                    </p>
                                  </div>
                                  {selectedRecord.notes && (
                                    <div>
                                      <label className="text-sm font-medium text-gray-700">
                                        备注
                                      </label>
                                      <p className="text-gray-900 mt-1">
                                        {selectedRecord.notes}
                                      </p>
                                    </div>
                                  )}
                                </div>
                              )}
                            </DialogContent>
                          </Dialog>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </CardContent>
            </Card>
          </TabsContent>

          {/* Statistics Tab */}
          <TabsContent value="statistics" className="space-y-4">
            <div className="grid gap-6 md:grid-cols-2">
              <Card className="bg-white border-gray-200 shadow-sm">
                <CardHeader>
                  <CardTitle className="text-gray-900">
                    每日出入库统计
                  </CardTitle>
                  <CardDescription className="text-gray-600">
                    过去7天的出入库数量对比
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="h-80">
                    <StatusChart
                      type="bar"
                      data={dailyStats.map((stat) => ({
                        name: stat.date,
                        inbound: stat.inbound,
                        outbound: stat.outbound,
                      }))}
                      nameKey="name"
                      colors={["#22d3ee", "#3b82f6"]}
                      multipleBars={true}
                    />
                  </div>
                </CardContent>
              </Card>

              <Card className="bg-white border-gray-200 shadow-sm">
                <CardHeader>
                  <CardTitle className="text-gray-900">操作效率趋势</CardTitle>
                  <CardDescription className="text-gray-600">
                    仓储操作效率变化趋势
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="h-80">
                    <StatusChart
                      type="line"
                      data={dailyStats}
                      nameKey="date"
                      colors={["#10b981", "#3b82f6"]}
                      multipleLines={true}
                    />
                  </div>
                </CardContent>
              </Card>
            </div>
          </TabsContent>

          {/* Analysis Tab */}
          <TabsContent value="analysis" className="space-y-4">
            <div className="grid gap-6 md:grid-cols-3">
              <Card className="bg-white border-gray-200 shadow-sm">
                <CardHeader>
                  <CardTitle className="text-gray-900 flex items-center">
                    <TrendingUp className="mr-2 h-5 w-5 text-green-600" />
                    周度分析
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="space-y-2">
                    <div className="flex justify-between">
                      <span className="text-gray-700">入库总量</span>
                      <span className="text-green-600 font-semibold">
                        1,720
                      </span>
                    </div>
                    <Progress value={85} className="h-2" />
                  </div>
                  <div className="space-y-2">
                    <div className="flex justify-between">
                      <span className="text-gray-700">出库总量</span>
                      <span className="text-blue-600 font-semibold">1,310</span>
                    </div>
                    <Progress value={65} className="h-2" />
                  </div>
                  <div className="space-y-2">
                    <div className="flex justify-between">
                      <span className="text-gray-700">周转率</span>
                      <span className="text-cyan-600 font-semibold">76%</span>
                    </div>
                    <Progress value={76} className="h-2" />
                  </div>
                </CardContent>
              </Card>

              <Card className="bg-white border-gray-200 shadow-sm">
                <CardHeader>
                  <CardTitle className="text-gray-900">热门货位</CardTitle>
                </CardHeader>
                <CardContent className="space-y-3">
                                      {["A区-01", "B区-01", "C区-01"].map(
                    (location, index) => (
                      <div
                        key={location}
                        className="flex items-center justify-between p-2 bg-gray-50 rounded"
                      >
                        <span className="text-gray-700">{location}</span>
                        <Badge
                          variant="outline"
                          className="border-blue-500 text-blue-600"
                        >
                          {95 - index * 5}次
                        </Badge>
                      </div>
                    ),
                  )}
                </CardContent>
              </Card>

              <Card className="bg-white border-gray-200 shadow-sm">
                <CardHeader>
                  <CardTitle className="text-gray-900">操作员绩效</CardTitle>
                </CardHeader>
                <CardContent className="space-y-3">
                  {["张三", "李四", "王五", "赵六"].map((operator, index) => (
                    <div
                      key={operator}
                      className="flex items-center justify-between p-2 bg-gray-50 rounded"
                    >
                      <span className="text-gray-700">{operator}</span>
                      <div className="flex items-center space-x-2">
                        <span className="text-sm text-gray-500">
                          {180 - index * 20}次
                        </span>
                        <CheckCircle className="h-4 w-4 text-green-600" />
                      </div>
                    </div>
                  ))}
                </CardContent>
              </Card>
            </div>
          </TabsContent>
        </Tabs>
      </div>
    </div>
  );
}
