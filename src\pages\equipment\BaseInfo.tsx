import { DashboardCard } from "@/components/DashboardCard";
import { StatusChart } from "@/components/StatusChart";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Badge } from "@/components/ui/badge";
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import { Textarea } from "@/components/ui/textarea";
import {
  Plus,
  Search,
  Settings,
  AlertTriangle,
  CheckCircle,
  Clock,
  Wrench,
  Target,
  Shield,
  AlertCircle,
} from "lucide-react";

interface Equipment {
  id: string;
  name: string;
  type: string;
  model: string;
  manufacturer: string;
  installDate: string;
  status: "running" | "maintenance" | "fault" | "standby";
  location: string;
  mtbf: number;
  mttr: number;
  lastMaintenance: string;
  nextMaintenance: string;
}

const mockEquipment: Equipment[] = [
  {
    id: "EQ001",
    name: "桁架系统-01",
    type: "桁架",
    model: "TRS-2000",
    manufacturer: "智能装备有限公司",
    installDate: "2023-06-15",
    status: "running",
    location: "仓储区A",
    mtbf: 720,
    mttr: 2.5,
    lastMaintenance: "2024-01-01",
    nextMaintenance: "2024-04-01",
  },
  {
    id: "EQ002",
    name: "AGV-01",
    type: "AGV",
    model: "AGV-500",
    manufacturer: "自动化科技公司",
    installDate: "2023-07-20",
    status: "running",
    location: "运输通道",
    mtbf: 480,
    mttr: 1.8,
    lastMaintenance: "2024-01-10",
    nextMaintenance: "2024-03-10",
  },
  {
    id: "EQ003",
    name: "测试台-01",
    type: "测试设备",
    model: "TEST-3000",
    manufacturer: "测试设备制造商",
    installDate: "2023-08-01",
    status: "maintenance",
    location: "测试区",
    mtbf: 600,
    mttr: 3.2,
    lastMaintenance: "2024-01-15",
    nextMaintenance: "2024-04-15",
  },
];

const equipmentTypes = [
  { name: "桁架系统", count: 1, running: 1 },
  { name: "AGV设备", count: 6, running: 5 },
  { name: "测试台", count: 12, running: 11 },
  { name: "接驳台", count: 4, running: 3 },
  { name: "充电桩", count: 3, running: 3 },
  { name: "光电开关", count: 600, running: 600 },
  { name: "三色灯", count: 600, running: 600 },

];

const maintenanceData = [
  { name: "预防性保养", value: 15 },
  { name: "计划性维修", value: 8 },
  { name: "故障维修", value: 3 },
  { name: "改造升级", value: 2 },
];

export default function BaseInfo() {
  const getStatusBadge = (status: string) => {
    const statusConfig = {
      running: {
        label: "运行中",
        variant: "default" as const,
        icon: CheckCircle,
      },
      maintenance: {
        label: "维护中",
        variant: "secondary" as const,
        icon: Wrench,
      },
      fault: {
        label: "故障",
        variant: "destructive" as const,
        icon: AlertTriangle,
      },
      standby: { label: "待机", variant: "secondary" as const, icon: Clock },
    };

    const config = statusConfig[status as keyof typeof statusConfig];
    const Icon = config.icon;
    return (
      <Badge variant={config.variant} className="flex items-center gap-1">
        <Icon className="h-3 w-3" />
        {config.label}
      </Badge>
    );
  };

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h2 className="text-3xl font-bold tracking-tight">设备基准信息</h2>
        <div className="flex space-x-2">
          <Dialog>
            <DialogTrigger asChild>
              <Button variant="outline">
                <Shield className="mr-2 h-4 w-4" />
                创建保养策略
              </Button>
            </DialogTrigger>
            <DialogContent className="max-w-2xl">
              <DialogHeader>
                <DialogTitle>创建保养策略</DialogTitle>
                <DialogDescription>为设备制定保养计划和策略</DialogDescription>
              </DialogHeader>
              <div className="grid gap-4 py-4">
                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="strategyName">策略名称</Label>
                    <Input id="strategyName" placeholder="保养策略名称" />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="equipmentType">适用设备类型</Label>
                    <Select>
                      <SelectTrigger>
                        <SelectValue placeholder="选择设备类型" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="truss">桁架系统</SelectItem>
                        <SelectItem value="agv">AGV设备</SelectItem>
                        <SelectItem value="test">测试设备</SelectItem>
                        <SelectItem value="bridge">接驳台</SelectItem>
                        <SelectItem value="charger">充电桩</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </div>
                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="maintenanceCycle">保养周期(天)</Label>
                    <Input id="maintenanceCycle" placeholder="保养周期天数" />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="maintenanceType">保养类型</Label>
                    <Select>
                      <SelectTrigger>
                        <SelectValue placeholder="选择保养类型" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="preventive">预防性保养</SelectItem>
                        <SelectItem value="corrective">纠正性保养</SelectItem>
                        <SelectItem value="predictive">预测性保养</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </div>
                <div className="space-y-2">
                  <Label htmlFor="strategyDescription">策略描述</Label>
                  <Textarea id="strategyDescription" placeholder="保养策略详细描述..." />
                </div>
              </div>
              <div className="flex justify-end space-x-2">
                <Button variant="outline">取消</Button>
                <Button>保存策略</Button>
              </div>
            </DialogContent>
          </Dialog>

          <Dialog>
            <DialogTrigger asChild>
              <Button variant="outline">
                <Target className="mr-2 h-4 w-4" />
                目标设定
              </Button>
            </DialogTrigger>
            <DialogContent className="max-w-2xl">
              <DialogHeader>
                <DialogTitle>设备目标设定</DialogTitle>
                <DialogDescription>设定设备运行目标和性能指标</DialogDescription>
              </DialogHeader>
              <div className="grid gap-4 py-4">
                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="targetName">目标名称</Label>
                    <Input id="targetName" placeholder="目标名称" />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="targetType">目标类型</Label>
                    <Select>
                      <SelectTrigger>
                        <SelectValue placeholder="选择目标类型" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="efficiency">效率目标</SelectItem>
                        <SelectItem value="quality">质量目标</SelectItem>
                        <SelectItem value="safety">安全目标</SelectItem>
                        <SelectItem value="cost">成本目标</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </div>
                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="targetValue">目标值</Label>
                    <Input id="targetValue" placeholder="目标数值" />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="targetUnit">单位</Label>
                    <Input id="targetUnit" placeholder="目标单位" />
                  </div>
                </div>
                <div className="space-y-2">
                  <Label htmlFor="targetDescription">目标描述</Label>
                  <Textarea id="targetDescription" placeholder="目标详细描述..." />
                </div>
              </div>
              <div className="flex justify-end space-x-2">
                <Button variant="outline">取消</Button>
                <Button>保存目标</Button>
              </div>
            </DialogContent>
          </Dialog>

          <Dialog>
            <DialogTrigger asChild>
              <Button variant="outline">
                <AlertCircle className="mr-2 h-4 w-4" />
                新增故障原因
              </Button>
            </DialogTrigger>
            <DialogContent className="max-w-2xl">
              <DialogHeader>
                <DialogTitle>新增故障原因</DialogTitle>
                <DialogDescription>添加新的设备故障原因分类</DialogDescription>
              </DialogHeader>
              <div className="grid gap-4 py-4">
                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="faultName">故障名称</Label>
                    <Input id="faultName" placeholder="故障原因名称" />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="faultCategory">故障类别</Label>
                    <Select>
                      <SelectTrigger>
                        <SelectValue placeholder="选择故障类别" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="mechanical">机械故障</SelectItem>
                        <SelectItem value="electrical">电气故障</SelectItem>
                        <SelectItem value="software">软件故障</SelectItem>
                        <SelectItem value="human">人为因素</SelectItem>
                        <SelectItem value="environmental">环境因素</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </div>
                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="severity">严重程度</Label>
                    <Select>
                      <SelectTrigger>
                        <SelectValue placeholder="选择严重程度" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="low">轻微</SelectItem>
                        <SelectItem value="medium">中等</SelectItem>
                        <SelectItem value="high">严重</SelectItem>
                        <SelectItem value="critical">致命</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="priority">优先级</Label>
                    <Select>
                      <SelectTrigger>
                        <SelectValue placeholder="选择优先级" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="low">低</SelectItem>
                        <SelectItem value="medium">中</SelectItem>
                        <SelectItem value="high">高</SelectItem>
                        <SelectItem value="urgent">紧急</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </div>
                <div className="space-y-2">
                  <Label htmlFor="faultDescription">故障描述</Label>
                  <Textarea id="faultDescription" placeholder="故障原因详细描述..." />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="solution">解决方案</Label>
                  <Textarea id="solution" placeholder="故障解决方案..." />
                </div>
              </div>
              <div className="flex justify-end space-x-2">
                <Button variant="outline">取消</Button>
                <Button>保存故障原因</Button>
              </div>
            </DialogContent>
          </Dialog>

          <Dialog>
            <DialogTrigger asChild>
              <Button>
                <Plus className="mr-2 h-4 w-4" />
                新增设备
              </Button>
            </DialogTrigger>
            <DialogContent className="max-w-2xl">
              <DialogHeader>
                <DialogTitle>新增设备信息</DialogTitle>
                <DialogDescription>录入新设备的基础信息</DialogDescription>
              </DialogHeader>
              <div className="grid gap-4 py-4">
                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="equipmentName">设备名称</Label>
                    <Input id="equipmentName" placeholder="设备名称" />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="equipmentType">设备类型</Label>
                    <Select>
                      <SelectTrigger>
                        <SelectValue placeholder="选择设备类型" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="truss">桁架系统</SelectItem>
                        <SelectItem value="agv">AGV设备</SelectItem>
                        <SelectItem value="test">测试设备</SelectItem>
                        <SelectItem value="bridge">接驳台</SelectItem>
                        <SelectItem value="charger">充电桩</SelectItem>
                        <SelectItem value="light">光电开关</SelectItem>
                        <SelectItem value="light">三色灯</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </div>
                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="model">设备型号</Label>
                    <Input id="model" placeholder="设备型号" />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="manufacturer">制造商</Label>
                    <Input id="manufacturer" placeholder="制造商" />
                  </div>
                </div>
                <div className="space-y-2">
                  <Label htmlFor="location">安装位置</Label>
                  <Input id="location" placeholder="安装位置" />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="description">设备描述</Label>
                  <Textarea id="description" placeholder="设备详细描述..." />
                </div>
              </div>
              <div className="flex justify-end space-x-2">
                <Button variant="outline">取消</Button>
                <Button>保存</Button>
              </div>
                        </DialogContent>
          </Dialog>
        </div>
      </div>

      <Tabs defaultValue="overview" className="space-y-4">
        <TabsList>
          <TabsTrigger value="overview">设备概览</TabsTrigger>
          <TabsTrigger value="maintenance">保养策略</TabsTrigger>
          <TabsTrigger value="targets">目标设定</TabsTrigger>
          <TabsTrigger value="faults">故障原因</TabsTrigger>
        </TabsList>

        <TabsContent value="overview">
          <div className="space-y-6">
            {/* Equipment Summary */}
            <div className="grid gap-6 md:grid-cols-2">
              <Card>
                <CardHeader>
                  <CardTitle>设备类型分布</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    {equipmentTypes.map((type) => (
                      <div
                        key={type.name}
                        className="flex items-center justify-between"
                      >
                        <span className="text-sm font-medium">{type.name}</span>
                        <div className="flex items-center space-x-2">
                          <Badge variant="outline">
                            {type.running}/{type.count}
                          </Badge>
                          <div className="w-20 bg-gray-200 rounded-full h-2">
                            <div
                              className="bg-green-600 h-2 rounded-full"
                              style={{
                                width: `${(type.running / type.count) * 100}%`,
                              }}
                            ></div>
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>

              <DashboardCard
                title="维护作业分布"
                description="各类维护作业的数量分布"
              >
                <StatusChart type="pie" data={maintenanceData} />
              </DashboardCard>
            </div>

            {/* Equipment Table */}
            <Card>
              <CardHeader>
                <CardTitle>设备清单</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {/* Search and Filter */}
                  <div className="grid gap-4 md:grid-cols-4">
                    <div className="space-y-2">
                      <Label htmlFor="searchName">设备名称</Label>
                      <Input id="searchName" placeholder="搜索设备名称" />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="filterType">设备类型</Label>
                      <Select>
                        <SelectTrigger>
                          <SelectValue placeholder="选择类型" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="all">全部</SelectItem>
                          <SelectItem value="truss">桁架系统</SelectItem>
                          <SelectItem value="agv">AGV设备</SelectItem>
                          <SelectItem value="test">测试设备</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="filterStatus">运行状态</Label>
                      <Select>
                        <SelectTrigger>
                          <SelectValue placeholder="选择状态" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="all">全部</SelectItem>
                          <SelectItem value="running">运行中</SelectItem>
                          <SelectItem value="maintenance">维护中</SelectItem>
                          <SelectItem value="fault">故障</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                    <div className="flex items-end">
                      <Button className="w-full">
                        <Search className="mr-2 h-4 w-4" />
                        查询
                      </Button>
                    </div>
                  </div>

                  <Table>
                    <TableHeader>
                      <TableRow>
                        <TableHead>设备编号</TableHead>
                        <TableHead>设备名称</TableHead>
                        <TableHead>类型</TableHead>
                        <TableHead>型号</TableHead>
                        <TableHead>制造商</TableHead>
                        <TableHead>安装位置</TableHead>
                        <TableHead>运行状态</TableHead>
                        <TableHead>MTBF(小时)</TableHead>
                        <TableHead>MTTR(小时)</TableHead>
                        <TableHead>操作</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {mockEquipment.map((equipment) => (
                        <TableRow key={equipment.id}>
                          <TableCell className="font-medium">
                            {equipment.id}
                          </TableCell>
                          <TableCell>{equipment.name}</TableCell>
                          <TableCell>{equipment.type}</TableCell>
                          <TableCell>{equipment.model}</TableCell>
                          <TableCell>{equipment.manufacturer}</TableCell>
                          <TableCell>{equipment.location}</TableCell>
                          <TableCell>
                            {getStatusBadge(equipment.status)}
                          </TableCell>
                          <TableCell>{equipment.mtbf}</TableCell>
                          <TableCell>{equipment.mttr}</TableCell>
                          <TableCell>
                            <div className="flex space-x-2">
                              <Button size="sm" variant="outline">
                                <Settings className="h-3 w-3" />
                              </Button>
                              <Button size="sm" variant="outline">
                                编辑
                              </Button>
                            </div>
                          </TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="maintenance">
          <Card>
            <CardHeader>
              <CardTitle>日常点检/保养策略维护</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>策略编号</TableHead>
                      <TableHead>设备类型</TableHead>
                      <TableHead>保养类型</TableHead>
                      <TableHead>频率</TableHead>
                      <TableHead>检查项目</TableHead>
                      <TableHead>责任人</TableHead>
                      <TableHead>状态</TableHead>
                      <TableHead>操作</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    <TableRow>
                      <TableCell>MS001</TableCell>
                      <TableCell>AGV设备</TableCell>
                      <TableCell>日常点检</TableCell>
                      <TableCell>每日</TableCell>
                      <TableCell>电池电量、轮胎磨损、传感器状态</TableCell>
                      <TableCell>张三</TableCell>
                      <TableCell>
                        <Badge variant="default">启用</Badge>
                      </TableCell>
                      <TableCell>
                        <Button size="sm" variant="outline">
                          编辑
                        </Button>
                      </TableCell>
                    </TableRow>
                    <TableRow>
                      <TableCell>MS002</TableCell>
                      <TableCell>测试设备</TableCell>
                      <TableCell>周保养</TableCell>
                      <TableCell>每周</TableCell>
                      <TableCell>机械臂校准、夹具检查、软件更新</TableCell>
                      <TableCell>李四</TableCell>
                      <TableCell>
                        <Badge variant="default">启用</Badge>
                      </TableCell>
                      <TableCell>
                        <Button size="sm" variant="outline">
                          编辑
                        </Button>
                      </TableCell>
                    </TableRow>
                  </TableBody>
                </Table>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="targets">
          <div className="grid gap-6 md:grid-cols-2">
            <Card>
              <CardHeader>
                <CardTitle>MTBF目标设定</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {mockEquipment.map((equipment) => (
                    <div key={equipment.id} className="border rounded-lg p-4">
                      <div className="flex items-center justify-between mb-2">
                        <span className="font-medium">{equipment.name}</span>
                        <Badge variant="outline">{equipment.type}</Badge>
                      </div>
                      <div className="grid grid-cols-2 gap-4 text-sm">
                        <div>
                          <span className="text-gray-500">当前MTBF:</span>
                          <div className="font-semibold">
                            {equipment.mtbf}小时
                          </div>
                        </div>
                        <div>
                          <span className="text-gray-500">目标MTBF:</span>
                          <div className="font-semibold text-green-600">
                            800小时
                          </div>
                        </div>
                      </div>
                      <div className="mt-2">
                        <div className="w-full bg-gray-200 rounded-full h-2">
                          <div
                            className="bg-blue-600 h-2 rounded-full"
                            style={{
                              width: `${(equipment.mtbf / 800) * 100}%`,
                            }}
                          ></div>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>MTTR目标设定</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {mockEquipment.map((equipment) => (
                    <div key={equipment.id} className="border rounded-lg p-4">
                      <div className="flex items-center justify-between mb-2">
                        <span className="font-medium">{equipment.name}</span>
                        <Badge variant="outline">{equipment.type}</Badge>
                      </div>
                      <div className="grid grid-cols-2 gap-4 text-sm">
                        <div>
                          <span className="text-gray-500">当前MTTR:</span>
                          <div className="font-semibold">
                            {equipment.mttr}小时
                          </div>
                        </div>
                        <div>
                          <span className="text-gray-500">目标MTTR:</span>
                          <div className="font-semibold text-green-600">
                            2.0小时
                          </div>
                        </div>
                      </div>
                      <div className="mt-2">
                        <div className="w-full bg-gray-200 rounded-full h-2">
                          <div
                            className={`h-2 rounded-full ${
                              equipment.mttr <= 2.0
                                ? "bg-green-600"
                                : "bg-red-600"
                            }`}
                            style={{
                              width: `${Math.min((equipment.mttr / 4.0) * 100, 100)}%`,
                            }}
                          ></div>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="faults">
          <Card>
            <CardHeader>
              <CardTitle>设备故障原因维护</CardTitle>
            </CardHeader>
            <CardContent>
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>故障编号</TableHead>
                    <TableHead>设备名称</TableHead>
                    <TableHead>故障现象</TableHead>
                    <TableHead>故障原因</TableHead>
                    <TableHead>解决方案</TableHead>
                    <TableHead>发生频率</TableHead>
                    <TableHead>严重等级</TableHead>
                    <TableHead>操作</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  <TableRow>
                    <TableCell>FT001</TableCell>
                    <TableCell>AGV-01</TableCell>
                    <TableCell>导航偏差</TableCell>
                    <TableCell>激光雷达校准偏移</TableCell>
                    <TableCell>重新校准激光雷达定位系统</TableCell>
                    <TableCell>2次/月</TableCell>
                    <TableCell>
                      <Badge variant="secondary">中等</Badge>
                    </TableCell>
                    <TableCell>
                      <Button size="sm" variant="outline">
                        编辑
                      </Button>
                    </TableCell>
                  </TableRow>
                  <TableRow>
                    <TableCell>FT002</TableCell>
                    <TableCell>测试台-01</TableCell>
                    <TableCell>机械臂动作异常</TableCell>
                    <TableCell>伺服电机过热</TableCell>
                    <TableCell>更换伺服电机散热器</TableCell>
                    <TableCell>1次/3月</TableCell>
                    <TableCell>
                      <Badge variant="destructive">高</Badge>
                    </TableCell>
                    <TableCell>
                      <Button size="sm" variant="outline">
                        编辑
                      </Button>
                    </TableCell>
                  </TableRow>
                </TableBody>
              </Table>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}
