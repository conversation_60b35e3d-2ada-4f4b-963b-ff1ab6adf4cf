import React, { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { Badge } from "@/components/ui/badge";
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog";
import { Textarea } from "@/components/ui/textarea";
import { Search, Download, Eye, Edit, Plus, Save, X, ChevronLeft, ChevronRight } from "lucide-react";

const ProductLedger = () => {
  const [isRegisterDialogOpen, setIsRegisterDialogOpen] = useState(false);
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize, setPageSize] = useState(10);
  const [productLedger, setProductLedger] = useState([
    {
      id: "IG001",
      productName: "光纤惯导组件A型",
      productType: "光纤惯导",
      model: "FOG-001A",
      unit: "套",
      location: "A区-01-01",
      status: "待入库",
      testCount: 1,
      rfidTag: "RFID-IG001-2024-001",
      lastUpdate: "2024-01-15 14:30:00"
    },
    {
      id: "IG002",
      productName: "激光惯导组件B型",
      productType: "激光惯导",
      model: "RLG-002B",
      unit: "套",
      location: "A区-01-02",
      status: "测试中",
      testCount: 3,
      rfidTag: "RFID-IG002-2024-002",
      lastUpdate: "2024-01-15 16:20:00"
    },
    {
      id: "IG003",
      productName: "MEMS惯导组件C型",
      productType: "MEMS惯导",
      model: "MEMS-003C",
      unit: "套",
      location: "A区-02-01",
      status: "已入库",
      testCount: 4,
      rfidTag: "RFID-IG003-2024-003",
      lastUpdate: "2024-01-15 10:15:00"
    },
    {
      id: "IG004",
      productName: "高精度惯导组件D型",
      productType: "高精度惯导",
      model: "HP-004D",
      unit: "套",
      location: "A区-02-02",
      status: "待入库",
      testCount: 2,
      rfidTag: "RFID-IG004-2024-004",
      lastUpdate: "2024-01-15 09:45:00"
    },
    {
      id: "IG005",
      productName: "捷联惯导组件E型",
      productType: "捷联惯导",
      model: "SINS-005E",
      unit: "套",
      location: "A区-03-01",
      status: "已入库",
      testCount: 1,
      rfidTag: "RFID-IG005-2024-005",
      lastUpdate: "2024-01-15 08:30:00"
    },
    {
      id: "IG006",
      productName: "光纤惯导组件F型",
      productType: "光纤惯导",
      model: "FOG-006F",
      unit: "套",
      location: "A区-03-02",
      status: "测试中",
      testCount: 4,
      rfidTag: "RFID-IG006-2024-006",
      lastUpdate: "2024-01-15 07:15:00"
    },
    {
      id: "IG007",
      productName: "激光惯导组件G型",
      productType: "激光惯导",
      model: "RLG-007G",
      unit: "套",
      location: "G区-07-01",
      status: "待入库",
      testCount: 3,
      rfidTag: "RFID-IG007-2024-007",
      lastUpdate: "2024-01-15 06:45:00"
    },
    {
      id: "IG008",
      productName: "MEMS惯导组件H型",
      productType: "MEMS惯导",
      model: "MEMS-008H",
      unit: "套",
      location: "H区-08-01",
      status: "已入库",
      testCount: 2,
      rfidTag: "RFID-IG008-2024-008",
      lastUpdate: "2024-01-15 05:20:00"
    },
    {
      id: "IG009",
      productName: "高精度惯导组件I型",
      productType: "高精度惯导",
      model: "HP-009I",
      unit: "套",
      location: "I区-09-01",
      status: "测试中",
      testCount: 1,
      rfidTag: "RFID-IG009-2024-009",
      lastUpdate: "2024-01-15 04:10:00"
    },
    {
      id: "IG010",
      productName: "捷联惯导组件J型",
      productType: "捷联惯导",
      model: "SINS-010J",
      unit: "套",
      location: "J区-10-01",
      status: "待入库",
      testCount: 4,
      rfidTag: "RFID-IG010-2024-010",
      lastUpdate: "2024-01-15 03:30:00"
    }
  ]);

  // 处理入库操作
  const handleInbound = (productId: string) => {
    setProductLedger(prev => 
      prev.map(product => 
        product.id === productId 
          ? { ...product, status: "入库中" }
          : product
      )
    );

    // 20秒后状态变为已入库
    setTimeout(() => {
      setProductLedger(prev => 
        prev.map(product => 
          product.id === productId 
            ? { ...product, status: "已入库", lastUpdate: new Date().toLocaleString() }
            : product
        )
      );
    }, 20000);
  };

  // 处理产品登记
  const handleProductRegister = () => {
    // 生成新的产品ID
    const newId = `IG${String(productLedger.length + 1).padStart(3, '0')}`;
    
    // 生成RFID标签
    const rfidTag = `RFID-${newId}-${new Date().getFullYear()}-${String(productLedger.length + 1).padStart(3, '0')}`;
    
    // 创建新产品（这里简化处理，实际应该从表单获取数据）
    const newProduct = {
      id: newId,
      productName: "新惯组产品",
      productType: "光纤惯导",
      model: "NEW-001",
      unit: "套",
      location: "待分配",
      status: "待入库",
      testCount: 1,
      rfidTag: rfidTag,
      lastUpdate: new Date().toLocaleString()
    };

    // 添加到产品列表
    setProductLedger(prev => [...prev, newProduct]);
    
    // 关闭弹窗
    setIsRegisterDialogOpen(false);
  };

  // 分页计算
  const totalItems = productLedger.length;
  const totalPages = Math.ceil(totalItems / pageSize);
  const startIndex = (currentPage - 1) * pageSize;
  const endIndex = startIndex + pageSize;
  const currentProducts = productLedger.slice(startIndex, endIndex);

  // 处理页码变化
  const handlePageChange = (page: number) => {
    setCurrentPage(page);
  };

  // 处理每页显示数量变化
  const handlePageSizeChange = (size: number) => {
    setPageSize(size);
    setCurrentPage(1); // 重置到第一页
  };

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">产品台账</h1>
          <p className="text-muted-foreground">
            管理所有产品的库存信息和基本信息
          </p>
        </div>
        <div className="flex gap-2">
          <Button variant="outline" className="flex items-center gap-2">
            <Download className="h-4 w-4" />
            导出台账
          </Button>
          <Dialog open={isRegisterDialogOpen} onOpenChange={setIsRegisterDialogOpen}>
            <DialogTrigger asChild>
              <Button className="flex items-center gap-2">
                <Plus className="h-4 w-4" />
                产品登记
              </Button>
            </DialogTrigger>
            <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
              <DialogHeader>
                <DialogTitle className="flex items-center gap-2">
                  <Edit className="h-5 w-5" />
                  惯组产品登记
                </DialogTitle>
                <DialogDescription>
                  登记新的惯组产品信息，包括基本信息、技术参数等
                </DialogDescription>
              </DialogHeader>
              <div className="space-y-6">
                                 <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                   <div className="space-y-2">
                     <Label htmlFor="productCode">产品编码</Label>
                     <Input id="productCode" placeholder="请输入惯组产品编码" />
                   </div>
                   <div className="space-y-2">
                     <Label htmlFor="productName">产品名称</Label>
                     <Input id="productName" placeholder="请输入惯组产品名称" />
                   </div>
                   <div className="space-y-2">
                     <Label htmlFor="productType">惯组类型</Label>
                     <Select>
                       <SelectTrigger>
                         <SelectValue placeholder="请选择惯组类型" />
                       </SelectTrigger>
                       <SelectContent>
                         <SelectItem value="fiber">光纤惯导</SelectItem>
                         <SelectItem value="laser">激光惯导</SelectItem>
                         <SelectItem value="mems">MEMS惯导</SelectItem>
                         <SelectItem value="high-precision">高精度惯导</SelectItem>
                         <SelectItem value="strapdown">捷联惯导</SelectItem>
                       </SelectContent>
                     </Select>
                   </div>
                   <div className="space-y-2">
                     <Label htmlFor="model">型号规格</Label>
                     <Input id="model" placeholder="请输入型号规格" />
                   </div>
                   <div className="space-y-2">
                     <Label htmlFor="unit">计量单位</Label>
                     <Select>
                       <SelectTrigger>
                         <SelectValue placeholder="请选择计量单位" />
                       </SelectTrigger>
                       <SelectContent>
                         <SelectItem value="set">套</SelectItem>
                         <SelectItem value="piece">件</SelectItem>
                       </SelectContent>
                     </Select>
                   </div>

                 </div>
                
                <div className="space-y-2">
                  <Label htmlFor="description">产品描述</Label>
                  <Textarea 
                    id="description" 
                    placeholder="请输入惯组产品详细描述"
                    rows={4}
                  />
                </div>

                <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                  <div className="space-y-2">
                    <Label htmlFor="length">长度 (mm)</Label>
                    <Input id="length" type="number" placeholder="0" />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="width">宽度 (mm)</Label>
                    <Input id="width" type="number" placeholder="0" />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="height">高度 (mm)</Label>
                    <Input id="height" type="number" placeholder="0" />
                  </div>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div className="space-y-2">
                    <Label htmlFor="weight">重量 (kg)</Label>
                    <Input id="weight" type="number" step="0.01" placeholder="0.00" />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="accuracy">精度等级</Label>
                    <Select>
                      <SelectTrigger>
                        <SelectValue placeholder="请选择精度等级" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="high">高精度</SelectItem>
                        <SelectItem value="medium">中精度</SelectItem>
                        <SelectItem value="low">低精度</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="location">存储位置</Label>
                  <Input id="location" placeholder="请输入存储位置" />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="rfidTag">RFID标签</Label>
                  <Input id="rfidTag" placeholder="系统自动生成RFID标签" disabled />
                </div>

                <div className="flex justify-end space-x-4 pt-6">
                  <Button variant="outline" onClick={() => setIsRegisterDialogOpen(false)} className="flex items-center gap-2">
                    <X className="h-4 w-4" />
                    取消
                  </Button>
                                   <Button className="flex items-center gap-2" onClick={handleProductRegister}>
                   <Save className="h-4 w-4" />
                   保存产品
                 </Button>
                </div>
              </div>
            </DialogContent>
          </Dialog>
        </div>
      </div>

      {/* 统计卡片 */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">总惯组数</p>
                <p className="text-2xl font-bold">553</p>
              </div>
              <div className="h-8 w-8 bg-blue-100 rounded-lg flex items-center justify-center">
                <Eye className="h-4 w-4 text-blue-600" />
              </div>
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">待入库</p>
                <p className="text-2xl font-bold text-orange-600">12</p>
              </div>
              <div className="h-8 w-8 bg-orange-100 rounded-lg flex items-center justify-center">
                <Eye className="h-4 w-4 text-orange-600" />
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      <div className="grid gap-6">
        {/* 搜索筛选 */}
        <Card>
          <CardHeader>
            <CardTitle>台账筛选</CardTitle>
            <CardDescription>
              根据条件筛选产品台账信息
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-5 gap-4">
              <div className="space-y-2">
                <Label htmlFor="productCode">产品编码</Label>
                <Input id="productCode" placeholder="请输入产品编码" />
              </div>
              <div className="space-y-2">
                <Label htmlFor="productName">产品名称</Label>
                <Input id="productName" placeholder="请输入产品名称" />
              </div>
                             <div className="space-y-2">
                 <Label htmlFor="productType">惯组类型</Label>
                 <Select>
                   <SelectTrigger>
                     <SelectValue placeholder="请选择类型" />
                   </SelectTrigger>
                   <SelectContent>
                     <SelectItem value="all">全部</SelectItem>
                     <SelectItem value="fiber">光纤惯导</SelectItem>
                     <SelectItem value="laser">激光惯导</SelectItem>
                     <SelectItem value="mems">MEMS惯导</SelectItem>
                     <SelectItem value="high-precision">高精度惯导</SelectItem>
                     <SelectItem value="strapdown">捷联惯导</SelectItem>
                   </SelectContent>
                 </Select>
               </div>
                             <div className="space-y-2">
                 <Label htmlFor="status">状态</Label>
                 <Select>
                   <SelectTrigger>
                     <SelectValue placeholder="请选择状态" />
                   </SelectTrigger>
                   <SelectContent>
                     <SelectItem value="all">全部</SelectItem>
                     <SelectItem value="pending">待入库</SelectItem>
                     <SelectItem value="inbounding">入库中</SelectItem>
                     <SelectItem value="completed">已入库</SelectItem>
                     <SelectItem value="testing">测试中</SelectItem>
                   </SelectContent>
                 </Select>
               </div>
              <div className="space-y-2">
                <Label>&nbsp;</Label>
                <Button className="w-full flex items-center gap-2">
                  <Search className="h-4 w-4" />
                  搜索
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* 产品台账表格 */}
        <Card>
          <CardHeader>
            <CardTitle>产品台账</CardTitle>
            <CardDescription>
              显示所有产品的库存和基本信息
            </CardDescription>
          </CardHeader>
          <CardContent>
            <Table>
                             <TableHeader>
                                 <TableRow>
                  <TableHead>产品编码</TableHead>
                  <TableHead>产品名称</TableHead>
                  <TableHead>惯组类型</TableHead>
                  <TableHead>型号规格</TableHead>
                  <TableHead>RFID标签</TableHead>
                  <TableHead>仓位</TableHead>
                  <TableHead>测试次数</TableHead>
                  <TableHead>状态</TableHead>
                  <TableHead>最后更新</TableHead>
                  <TableHead>操作</TableHead>
                </TableRow>
               </TableHeader>
                                 <TableBody>
                     {currentProducts.map((product) => (
                       <TableRow key={product.id}>
                         <TableCell className="font-medium">{product.id}</TableCell>
                         <TableCell>{product.productName}</TableCell>
                         <TableCell>{product.productType}</TableCell>
                         <TableCell>{product.model}</TableCell>
                         <TableCell className="font-mono text-sm">{product.rfidTag}</TableCell>
                         <TableCell>{product.location}</TableCell>
                         <TableCell>
                           <Badge variant="outline" className="font-medium">
                             {product.testCount}/4
                           </Badge>
                         </TableCell>
                         <TableCell>
                           <Badge variant={
                             product.status === '入库中' ? 'default' : 
                             product.status === '已入库' ? 'secondary' : 
                             product.status === '测试中' ? 'destructive' : 'outline'
                           }>
                             {product.status}
                           </Badge>
                         </TableCell>
                         <TableCell>{product.lastUpdate}</TableCell>
                         <TableCell>
                           <div className="flex gap-2">
                             <Button variant="outline" size="sm">
                               <Eye className="h-3 w-3" />
                             </Button>
                             <Button variant="outline" size="sm">
                               <Edit className="h-3 w-3" />
                             </Button>
                             {product.status === '待入库' && (
                               <Button 
                                 variant="default" 
                                 size="sm"
                                 onClick={() => handleInbound(product.id)}
                               >
                                 入库
                               </Button>
                             )}
                           </div>
                         </TableCell>
                       </TableRow>
                     ))}
                   </TableBody>
                         </Table>
           </CardContent>
         </Card>

         {/* 分页组件 */}
         <Card>
           <CardContent className="p-4">
             <div className="flex items-center justify-between">
               <div className="flex items-center space-x-2">
                 <span className="text-sm text-muted-foreground">每页显示</span>
                 <Select value={pageSize.toString()} onValueChange={(value) => handlePageSizeChange(Number(value))}>
                   <SelectTrigger className="w-20">
                     <SelectValue />
                   </SelectTrigger>
                   <SelectContent>
                     <SelectItem value="5">5</SelectItem>
                     <SelectItem value="10">10</SelectItem>
                     <SelectItem value="20">20</SelectItem>
                     <SelectItem value="50">50</SelectItem>
                   </SelectContent>
                 </Select>
                 <span className="text-sm text-muted-foreground">条</span>
               </div>
               
               <div className="flex items-center space-x-2">
                 <span className="text-sm text-muted-foreground">
                   第 {startIndex + 1}-{Math.min(endIndex, totalItems)} 条，共 {totalItems} 条
                 </span>
                 
                 <div className="flex items-center space-x-1">
                   <Button
                     variant="outline"
                     size="sm"
                     onClick={() => handlePageChange(currentPage - 1)}
                     disabled={currentPage === 1}
                   >
                     <ChevronLeft className="h-4 w-4" />
                   </Button>
                   
                   {Array.from({ length: Math.min(5, totalPages) }, (_, i) => {
                     let pageNum;
                     if (totalPages <= 5) {
                       pageNum = i + 1;
                     } else if (currentPage <= 3) {
                       pageNum = i + 1;
                     } else if (currentPage >= totalPages - 2) {
                       pageNum = totalPages - 4 + i;
                     } else {
                       pageNum = currentPage - 2 + i;
                     }
                     
                     return (
                       <Button
                         key={pageNum}
                         variant={currentPage === pageNum ? "default" : "outline"}
                         size="sm"
                         onClick={() => handlePageChange(pageNum)}
                         className="w-8 h-8 p-0"
                       >
                         {pageNum}
                       </Button>
                     );
                   })}
                   
                   <Button
                     variant="outline"
                     size="sm"
                     onClick={() => handlePageChange(currentPage + 1)}
                     disabled={currentPage === totalPages}
                   >
                     <ChevronRight className="h-4 w-4" />
                   </Button>
                 </div>
               </div>
             </div>
           </CardContent>
         </Card>
       </div>
     </div>
   );
 };

export default ProductLedger; 