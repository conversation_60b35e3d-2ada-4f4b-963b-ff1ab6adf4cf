import React from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { Package, Plus, Save, Search, X } from "lucide-react";

const ProductInbound = () => {
  // 模拟惯组入库数据
  const inboundRecords = [
    {
      id: "IN001",
      productCode: "IG001",
      productName: "光纤惯导组件A型",
      rfidTag: "RFID-IG001-2024-001",
      quantity: 5,
      unit: "套",
      warehouse: "A区-01-01",
      operator: "张三",
      date: "2024-01-15 14:30:00",
      status: "已完成"
    },
    {
      id: "IN002", 
      productCode: "IG002",
      productName: "激光惯导组件B型",
      rfidTag: "RFID-IG002-2024-002",
      quantity: 3,
      unit: "套",
      warehouse: "A区-01-02",
      operator: "李四",
      date: "2024-01-15 16:20:00",
      status: "进行中"
    }
  ];

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">入库操作</h1>
          <p className="text-muted-foreground">
            管理惯组产品入库流程，包括入库登记、存储位置分配等
          </p>
        </div>
        <Button className="flex items-center gap-2">
          <Plus className="h-4 w-4" />
          新增入库单
        </Button>
      </div>

      <div className="grid gap-6">
        {/* 入库登记表单 */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Package className="h-5 w-5" />
              入库登记
            </CardTitle>
            <CardDescription>
              填写惯组产品入库信息和存储位置分配
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="space-y-2">
                <Label htmlFor="inboundNo">入库单号</Label>
                <Input id="inboundNo" placeholder="系统自动生成" disabled />
              </div>
              <div className="space-y-2">
                <Label htmlFor="inboundType">入库类型</Label>
                <Select>
                  <SelectTrigger>
                    <SelectValue placeholder="请选择入库类型" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="purchase">采购入库</SelectItem>
                    <SelectItem value="return">退货入库</SelectItem>
                    <SelectItem value="transfer">调拨入库</SelectItem>
                    <SelectItem value="other">其他入库</SelectItem>
                  </SelectContent>
                </Select>
              </div>
                             <div className="space-y-2">
                 <Label htmlFor="productCode">惯组产品编码</Label>
                 <div className="flex gap-2">
                   <Input id="productCode" placeholder="请输入惯组产品编码" />
                   <Button variant="outline" size="icon">
                     <Search className="h-4 w-4" />
                   </Button>
                 </div>
               </div>
               <div className="space-y-2">
                 <Label htmlFor="productName">惯组产品名称</Label>
                 <Input id="productName" placeholder="自动填充" disabled />
               </div>
               <div className="space-y-2">
                 <Label htmlFor="rfidTag">RFID标签</Label>
                 <Input id="rfidTag" placeholder="自动填充" disabled />
               </div>
              <div className="space-y-2">
                <Label htmlFor="quantity">入库数量</Label>
                <Input id="quantity" type="number" placeholder="请输入数量" />
              </div>
              <div className="space-y-2">
                <Label htmlFor="unit">计量单位</Label>
                <Input id="unit" placeholder="自动填充" disabled />
              </div>
                             <div className="space-y-2">
                 <Label htmlFor="warehouse">存储位置</Label>
                 <Select>
                   <SelectTrigger>
                     <SelectValue placeholder="请选择存储位置" />
                   </SelectTrigger>
                   <SelectContent>
                                            <SelectItem value="A区-01-01">A区-01-01</SelectItem>
                       <SelectItem value="A区-01-02">A区-01-02</SelectItem>
                       <SelectItem value="A区-01-03">A区-01-03</SelectItem>
                       <SelectItem value="A区-02-01">A区-02-01</SelectItem>
                       <SelectItem value="A区-02-02">A区-02-02</SelectItem>
                       <SelectItem value="A区-03-01">A区-03-01</SelectItem>
                     <SelectItem value="B区-02-02">B区-02-02</SelectItem>
                   </SelectContent>
                 </Select>
               </div>
              <div className="space-y-2">
                <Label htmlFor="operator">操作员</Label>
                <Input id="operator" placeholder="当前用户" disabled />
              </div>
            </div>
            
            <div className="space-y-2">
              <Label htmlFor="remark">备注</Label>
              <Textarea 
                id="remark" 
                placeholder="请输入备注信息"
                rows={3}
              />
            </div>

            <div className="flex justify-end space-x-4 pt-6">
              <Button variant="outline" className="flex items-center gap-2">
                <X className="h-4 w-4" />
                取消
              </Button>
              <Button className="flex items-center gap-2">
                <Save className="h-4 w-4" />
                确认入库
              </Button>
            </div>
          </CardContent>
        </Card>

        {/* 入库记录列表 */}
        <Card>
          <CardHeader>
            <CardTitle>入库记录</CardTitle>
            <CardDescription>
              查看最近的入库操作记录
            </CardDescription>
          </CardHeader>
          <CardContent>
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>入库单号</TableHead>
                  <TableHead>惯组产品编码</TableHead>
                  <TableHead>惯组产品名称</TableHead>
                  <TableHead>RFID标签</TableHead>
                  <TableHead>数量</TableHead>
                  <TableHead>存储位置</TableHead>
                  <TableHead>操作员</TableHead>
                  <TableHead>入库时间</TableHead>
                  <TableHead>状态</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {inboundRecords.map((record) => (
                  <TableRow key={record.id}>
                    <TableCell className="font-medium">{record.id}</TableCell>
                    <TableCell>{record.productCode}</TableCell>
                    <TableCell>{record.productName}</TableCell>
                    <TableCell className="font-mono text-sm">{record.rfidTag}</TableCell>
                    <TableCell>{record.quantity} {record.unit}</TableCell>
                    <TableCell>{record.warehouse}</TableCell>
                    <TableCell>{record.operator}</TableCell>
                    <TableCell>{record.date}</TableCell>
                    <TableCell>
                      <span className={`px-2 py-1 rounded-full text-xs ${
                        record.status === '已完成' 
                          ? 'bg-green-100 text-green-800' 
                          : 'bg-yellow-100 text-yellow-800'
                      }`}>
                        {record.status}
                      </span>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </CardContent>
        </Card>
      </div>
    </div>
  );
};

export default ProductInbound; 