import { useState } from "react";
import { DashboardCard } from "@/components/DashboardCard";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Badge } from "@/components/ui/badge";
import { Ta<PERSON>, <PERSON><PERSON>Content, TabsList, TabsTrigger } from "@/components/ui/tabs";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import { Textarea } from "@/components/ui/textarea";
import { Checkbox } from "@/components/ui/checkbox";
import { Progress } from "@/components/ui/progress";
import {
  Plus,
  Search,
  Download,
  Upload,
  Package,
  MapPin,
  Scan,
  RotateCcw,
  CheckCircle,
  AlertTriangle,
  Clock,
} from "lucide-react";

interface Product {
  id: string;
  batch: string;
  registrationDate: string;
  status: "stored" | "testing" | "completed" | "shipped";
  location: string;
  testCount: number;
  maxTests: number;
  notes?: string;
}

interface StorageOperation {
  id: string;
  productId: string;
  operation: "in" | "out";
  fromLocation?: string;
  toLocation: string;
  timestamp: string;
  operator: string;
  status: "pending" | "completed" | "failed";
}

const mockProducts: Product[] = [
  {
    id: "A-2024-001",
    batch: "B240115-01",
    registrationDate: "2024-01-15",
    status: "stored",
          location: "A区-01-01",
    testCount: 2,
    maxTests: 4,
    notes: "正常入库",
  },
  {
    id: "A-2024-002",
    batch: "B240115-01",
    registrationDate: "2024-01-15",
    status: "testing",
    location: "测试台-01",
    testCount: 1,
    maxTests: 4,
    notes: "第2次测试中",
  },
  {
    id: "A-2024-003",
    batch: "B240116-01",
    registrationDate: "2024-01-16",
    status: "completed",
          location: "B区-01-05",
    testCount: 4,
    maxTests: 4,
    notes: "测试完成，待出库",
  },
];

const mockOperations: StorageOperation[] = [
  {
    id: "OP001",
    productId: "A-2024-001",
    operation: "in",
          toLocation: "A区-01-01",
    timestamp: "2024-01-15 09:30",
    operator: "张三",
    status: "completed",
  },
  {
    id: "OP002",
    productId: "A-2024-002",
    operation: "out",
    fromLocation: "货架1-A-02",
    toLocation: "测试台-01",
    timestamp: "2024-01-15 10:15",
    operator: "系统自动",
    status: "completed",
  },
  {
    id: "OP003",
    productId: "A-2024-004",
    operation: "in",
    toLocation: "货架3-C-08",
    timestamp: "2024-01-16 14:20",
    operator: "李四",
    status: "pending",
  },
];

// 静态模拟货架和货位数据
const shelfOptions = [
  { value: "01", label: "01" },
  { value: "02", label: "02" },
  { value: "03", label: "03" },
];
const positionOptionsMap: Record<string, { value: string; label: string }[]> = {
  "01": [
    { value: "0101", label: "0101" },
    { value: "0102", label: "0102" },
    { value: "0103", label: "0103" },
  ],
  "02": [
    { value: "0201", label: "0201" },
    { value: "0202", label: "0202" },
    { value: "0203", label: "0203" },
  ],
  "03": [
    { value: "0301", label: "0301" },
    { value: "0302", label: "0302" },
    { value: "0303", label: "0303" },
  ],
};

export default function ProductManagement() {
  const [products, setProducts] = useState(mockProducts);
  const [selectedProducts, setSelectedProducts] = useState<string[]>([]);
  const [inventoryProgress, setInventoryProgress] = useState(0);
  const [isInventoryRunning, setIsInventoryRunning] = useState(false);
  const [selectedShelf, setSelectedShelf] = useState<string>("");
  const [selectedPosition, setSelectedPosition] = useState<string>("");

  const getStatusBadge = (status: string) => {
    const statusConfig = {
      stored: { label: "已存储", variant: "secondary" as const, icon: Package },
      testing: { label: "测试中", variant: "default" as const, icon: Clock },
      completed: {
        label: "已完成",
        variant: "default" as const,
        icon: CheckCircle,
      },
      shipped: { label: "已出库", variant: "secondary" as const, icon: Upload },
    };

    const config = statusConfig[status as keyof typeof statusConfig];
    const Icon = config.icon;
    return (
      <Badge variant={config.variant} className="flex items-center gap-1">
        <Icon className="h-3 w-3" />
        {config.label}
      </Badge>
    );
  };

  const getOperationBadge = (operation: string) => {
    return operation === "in" ? (
      <Badge className="bg-green-100 text-green-800">入库</Badge>
    ) : (
      <Badge className="bg-blue-100 text-blue-800">出库</Badge>
    );
  };

  const getOperationStatusBadge = (status: string) => {
    const statusConfig = {
      pending: { label: "待执行", variant: "secondary" as const },
      completed: { label: "已完成", variant: "default" as const },
      failed: { label: "失败", variant: "destructive" as const },
    };

    const config = statusConfig[status as keyof typeof statusConfig];
    return <Badge variant={config.variant}>{config.label}</Badge>;
  };

  const handleInventoryStart = () => {
    setIsInventoryRunning(true);
    setInventoryProgress(0);

    // 模拟盘点进度
    const interval = setInterval(() => {
      setInventoryProgress((prev) => {
        if (prev >= 100) {
          clearInterval(interval);
          setIsInventoryRunning(false);
          return 100;
        }
        return prev + 10;
      });
    }, 500);
  };

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h2 className="text-3xl font-bold tracking-tight">产品管理</h2>
        <div className="flex items-center space-x-2">
          <Dialog>
            <DialogTrigger asChild>
              <Button>
                <Plus className="mr-2 h-4 w-4" />
                产品登记
              </Button>
            </DialogTrigger>
            <DialogContent className="max-w-2xl">
              <DialogHeader>
                <DialogTitle>新产品登记</DialogTitle>
                <DialogDescription>为新生产的惯组进行登记</DialogDescription>
              </DialogHeader>
              <div className="grid gap-4 py-4">
                <div className="grid grid-cols-5 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="productId">产品编号</Label>
                    <Input id="productId" placeholder="A-2024-XXX" />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="batch">批次号</Label>
                    <Input id="batch" placeholder="B240115-01" />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="tagId">电子标签编号</Label>
                    <Input id="tagId" placeholder="请输入电子标签编号" />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="shelfNumber">货架号</Label>
                    <Select
                      value={selectedShelf}
                      onValueChange={(value) => {
                        setSelectedShelf(value);
                        setSelectedPosition("");
                      }}
                    >
                      <SelectTrigger>
                        <SelectValue placeholder="选择货架号" />
                      </SelectTrigger>
                      <SelectContent>
                        {shelfOptions.map((shelf) => (
                          <SelectItem key={shelf.value} value={shelf.value}>
                            {shelf.label}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="positionNumber">货位号</Label>
                    <Select
                      value={selectedPosition}
                      onValueChange={setSelectedPosition}
                      disabled={!selectedShelf}
                    >
                      <SelectTrigger>
                        <SelectValue placeholder="选择货位号" />
                      </SelectTrigger>
                      <SelectContent>
                        {(positionOptionsMap[selectedShelf] || []).map((pos) => (
                          <SelectItem key={pos.value} value={pos.value}>
                            {pos.label}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>
                </div>
                <div className="space-y-2">
                  <Label htmlFor="notes">备注信息</Label>
                  <Textarea id="notes" placeholder="产品相关备注..." />
                </div>
              </div>
              <div className="flex justify-end space-x-2">
                <Button variant="outline">取消</Button>
                <Button>登记产品</Button>
              </div>
            </DialogContent>
          </Dialog>
        </div>
      </div>

      <Tabs defaultValue="registry" className="space-y-4">
        <TabsList>
          <TabsTrigger value="registry">产品登记</TabsTrigger>
          <TabsTrigger value="storage">入库操作</TabsTrigger>
          <TabsTrigger value="inventory">产品盘点</TabsTrigger>
          <TabsTrigger value="ledger">产品台账</TabsTrigger>
        </TabsList>

        <TabsContent value="registry">
          <div className="space-y-4">
            {/* Search and Filter */}
            <Card>
              <CardHeader>
                <CardTitle>产品查询</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid gap-4 md:grid-cols-4">
                  <div className="space-y-2">
                    <Label htmlFor="searchProduct">产品编号</Label>
                    <Input id="searchProduct" placeholder="A-2024-XXX" />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="searchBatch">批次号</Label>
                    <Input id="searchBatch" placeholder="B240115-01" />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="statusFilter">状态</Label>
                    <Select>
                      <SelectTrigger>
                        <SelectValue placeholder="选择状态" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="all">全部</SelectItem>
                        <SelectItem value="stored">已存储</SelectItem>
                        <SelectItem value="testing">测试中</SelectItem>
                        <SelectItem value="completed">已完成</SelectItem>
                        <SelectItem value="shipped">已出库</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                  <div className="flex items-end">
                    <Button className="w-full">
                      <Search className="mr-2 h-4 w-4" />
                      查询
                    </Button>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Products Table */}
            <Card>
              <CardHeader>
                <CardTitle>产品列表</CardTitle>
              </CardHeader>
              <CardContent>
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead className="w-[50px]">
                        <Checkbox />
                      </TableHead>
                      <TableHead>产品编号</TableHead>
                      <TableHead>批次号</TableHead>
                      <TableHead>登记日期</TableHead>
                      <TableHead>当前状态</TableHead>
                      <TableHead>位置</TableHead>
                      <TableHead>测试进度</TableHead>
                      <TableHead>备注</TableHead>
                      <TableHead>操作</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {products.map((product) => (
                      <TableRow key={product.id}>
                        <TableCell>
                          <Checkbox
                            checked={selectedProducts.includes(product.id)}
                            onCheckedChange={(checked) => {
                              if (checked) {
                                setSelectedProducts([
                                  ...selectedProducts,
                                  product.id,
                                ]);
                              } else {
                                setSelectedProducts(
                                  selectedProducts.filter(
                                    (id) => id !== product.id,
                                  ),
                                );
                              }
                            }}
                          />
                        </TableCell>
                        <TableCell className="font-medium">
                          {product.id}
                        </TableCell>
                        <TableCell>{product.batch}</TableCell>
                        <TableCell>{product.registrationDate}</TableCell>
                        <TableCell>{getStatusBadge(product.status)}</TableCell>
                        <TableCell>{product.location}</TableCell>
                        <TableCell>
                          <div className="flex items-center space-x-2">
                            <Progress
                              value={
                                (product.testCount / product.maxTests) * 100
                              }
                              className="w-16 h-2"
                            />
                            <span className="text-sm">
                              {product.testCount}/{product.maxTests}
                            </span>
                          </div>
                        </TableCell>
                        <TableCell className="max-w-[200px] truncate">
                          {product.notes}
                        </TableCell>
                        <TableCell>
                          <div className="flex space-x-2">
                            <Button size="sm" variant="outline">
                              编辑
                            </Button>
                            <Button size="sm" variant="outline">
                              <MapPin className="h-3 w-3" />
                            </Button>
                          </div>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="storage">
          <div className="space-y-6">
            {/* Storage Operations */}
            <div className="grid gap-6 md:grid-cols-2">
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center">
                    <Upload className="mr-2 h-4 w-4" />
                    产品入库操作
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="space-y-2">
                    <Label htmlFor="inboundProduct">产品编号</Label>
                    <Input
                      id="inboundProduct"
                      placeholder="扫描或输入产品编号"
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="targetLocation">目标货位</Label>
                    <Select>
                      <SelectTrigger>
                        <SelectValue placeholder="选择货位" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="shelf1-a-01">货架1-A-01</SelectItem>
                        <SelectItem value="shelf1-a-02">货架1-A-02</SelectItem>
                        <SelectItem value="shelf2-b-01">货架2-B-01</SelectItem>
                        <SelectItem value="auto">自动分配</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                  <Button className="w-full">调度桁架执行入库</Button>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center">
                    <Download className="mr-2 h-4 w-4" />
                    产品出库操作
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="space-y-2">
                    <Label htmlFor="outboundProduct">产品编号</Label>
                    <Input
                      id="outboundProduct"
                      placeholder="扫描或输入产品编号"
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="outboundReason">出库原因</Label>
                    <Select>
                      <SelectTrigger>
                        <SelectValue placeholder="选择出库原因" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="testing">转入测试</SelectItem>
                        <SelectItem value="shipping">最终出库</SelectItem>
                        <SelectItem value="maintenance">维护检查</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                  <Button className="w-full" variant="outline">
                    调度桁架执行出库
                  </Button>
                </CardContent>
              </Card>
            </div>

            {/* Recent Operations */}
            <Card>
              <CardHeader>
                <CardTitle>最近出入库操作</CardTitle>
              </CardHeader>
              <CardContent>
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>操作编号</TableHead>
                      <TableHead>产品编号</TableHead>
                      <TableHead>操作类型</TableHead>
                      <TableHead>源位置</TableHead>
                      <TableHead>目标位置</TableHead>
                      <TableHead>操作时间</TableHead>
                      <TableHead>操作员</TableHead>
                      <TableHead>状态</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {mockOperations.map((operation) => (
                      <TableRow key={operation.id}>
                        <TableCell className="font-medium">
                          {operation.id}
                        </TableCell>
                        <TableCell>{operation.productId}</TableCell>
                        <TableCell>
                          {getOperationBadge(operation.operation)}
                        </TableCell>
                        <TableCell>{operation.fromLocation || "-"}</TableCell>
                        <TableCell>{operation.toLocation}</TableCell>
                        <TableCell>{operation.timestamp}</TableCell>
                        <TableCell>{operation.operator}</TableCell>
                        <TableCell>
                          {getOperationStatusBadge(operation.status)}
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="inventory">
          <div className="space-y-6">
            {/* 操作说明 */}
            <Card>
              <CardContent>
                <ol className="list-decimal pl-5 space-y-1 text-sm text-gray-700">
                  <li>选择盘点范围和产品类型等筛选条件。</li>
                  <li>点击"开始盘点"按钮，系统自动统计当前库存。</li>
                  <li>通过扫码枪扫描产品条码，或手动输入产品编号。</li>
                  <li>核对盘点结果，系统自动标记异常。</li>
                  <li>对异常产品进行处理，保存盘点结果。</li>
                  <li>如需导出，可点击"导出"按钮生成盘点报告。</li>
                </ol>
              </CardContent>
            </Card>
            {/* 筛选条件与操作按钮区 */}
            <div className="grid gap-6 md:grid-cols-2">
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center">
                    <Scan className="mr-2 h-4 w-4" />
                    产品盘点操作
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="grid gap-4 md:grid-cols-2">
                    <div className="space-y-2">
                      <Label htmlFor="inventoryScope">盘点范围</Label>
                      <Select>
                        <SelectTrigger>
                          <SelectValue placeholder="选择盘点范围" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="all">全仓盘点</SelectItem>
                          <SelectItem value="shelf1">货架1</SelectItem>
                          <SelectItem value="shelf2">货架2</SelectItem>
                          <SelectItem value="shelf3">货架3</SelectItem>
                          <SelectItem value="specific">指定货位</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="productType">产品类型</Label>
                      <Select>
                        <SelectTrigger>
                          <SelectValue placeholder="选择产品类型" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="all">全部</SelectItem>
                          <SelectItem value="A">A类产品</SelectItem>
                          <SelectItem value="B">B类产品</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="scanInput">扫码/手动录入产品编号</Label>
                    <div className="flex space-x-2">
                      <Input id="scanInput" placeholder="扫描或输入产品编号" />
                      <Button size="icon" variant="outline">
                        <Scan className="h-4 w-4" />
                      </Button>
                    </div>
                  </div>
                  <div className="flex space-x-2">
                    <Button className="flex-1" onClick={handleInventoryStart} disabled={isInventoryRunning}>
                      <RotateCcw className="mr-2 h-4 w-4" />
                      开始盘点
                    </Button>
                    <Button className="flex-1" variant="outline">
                      保存盘点
                    </Button>
                    <Button className="flex-1" variant="outline">
                      <Download className="mr-2 h-4 w-4" />
                      导出报告
                    </Button>
                  </div>
                  {isInventoryRunning && (
                    <div className="space-y-4">
                      <div className="text-sm font-medium">盘点进行中...</div>
                      <Progress value={inventoryProgress} className="w-full" />
                      <div className="text-sm text-gray-500">
                        已完成: {inventoryProgress}%
                      </div>
                    </div>
                  )}
                </CardContent>
              </Card>
              <Card>
                <CardHeader>
                  <CardTitle>盘点结果统计</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="grid grid-cols-2 gap-4">
                    <div className="text-center">
                      <div className="text-2xl font-bold text-green-600">
                        456
                      </div>
                      <div className="text-sm text-gray-500">盘点一致</div>
                    </div>
                    <div className="text-center">
                      <div className="text-2xl font-bold text-red-600">3</div>
                      <div className="text-sm text-gray-500">盘点差异</div>
                    </div>
                    <div className="text-center">
                      <div className="text-2xl font-bold text-blue-600">
                        120
                      </div>
                      <div className="text-sm text-gray-500">空货位</div>
                    </div>
                    <div className="text-center">
                      <div className="text-2xl font-bold text-gray-600">21</div>
                      <div className="text-sm text-gray-500">未扫描</div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>
            {/* Inventory Discrepancies */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <AlertTriangle className="mr-2 h-4 w-4" />
                  盘点差异处理
                </CardTitle>
              </CardHeader>
              <CardContent>
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>货位</TableHead>
                      <TableHead>系统记录</TableHead>
                      <TableHead>实际扫描</TableHead>
                      <TableHead>差异类型</TableHead>
                      <TableHead>处理状态</TableHead>
                      <TableHead>异常说明</TableHead>
                      <TableHead>操作</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    <TableRow>
                      <TableCell>A区-01-05</TableCell>
                      <TableCell>A-2024-010</TableCell>
                      <TableCell>A-2024-011</TableCell>
                      <TableCell>
                        <Badge variant="destructive">产品不符</Badge>
                      </TableCell>
                      <TableCell>
                        <Badge variant="secondary">待处理</Badge>
                      </TableCell>
                      <TableCell>
                        <Input size={"sm"} placeholder="异常说明" />
                      </TableCell>
                      <TableCell>
                        <Button size="sm" variant="outline">
                          更新记录
                        </Button>
                      </TableCell>
                    </TableRow>
                    <TableRow>
                      <TableCell>B区-01-03</TableCell>
                      <TableCell>A-2024-012</TableCell>
                      <TableCell>空</TableCell>
                      <TableCell>
                        <Badge variant="destructive">产品缺失</Badge>
                      </TableCell>
                      <TableCell>
                        <Badge variant="secondary">待处理</Badge>
                      </TableCell>
                      <TableCell>
                        <Input size={"sm"} placeholder="异常说明" />
                      </TableCell>
                      <TableCell>
                        <Button size="sm" variant="outline">
                          查找产品
                        </Button>
                      </TableCell>
                    </TableRow>
                  </TableBody>
                </Table>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="ledger">
          <Card>
            <CardHeader>
              <CardTitle>产品台账</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {/* Summary Cards */}
                <div className="grid gap-4 md:grid-cols-4">
                  <Card>
                    <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                      <CardTitle className="text-sm font-medium">
                        总惯组数
                      </CardTitle>
                      <Package className="h-4 w-4 text-muted-foreground" />
                    </CardHeader>
                    <CardContent>
                      <div className="text-2xl font-bold  text-red-500">553</div>
                      <p className="text-xs text-muted-foreground">
                        本月新增 45
                      </p>
                    </CardContent>
                  </Card>

                  <Card>
                    <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                      <CardTitle className="text-sm font-medium">
                        在库产品
                      </CardTitle>
                      <Package className="h-4 w-4 text-muted-foreground" />
                    </CardHeader>
                    <CardContent>
                      <div className="text-2xl font-bold">550</div>
                      <p className="text-xs text-muted-foreground">
                        库存利用率 92%
                      </p>
                    </CardContent>
                  </Card>

                  <Card>
                    <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                      <CardTitle className="text-sm font-medium">
                        测试中产品
                      </CardTitle>
                      <Clock className="h-4 w-4 text-muted-foreground" />
                    </CardHeader>
                    <CardContent>
                      <div className="text-2xl font-bold">12</div>
                      <p className="text-xs text-muted-foreground">
                        12个测试台运行
                      </p>
                    </CardContent>
                  </Card>

                  <Card>
                    <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                      <CardTitle className="text-sm font-medium">
                        已出库产品
                      </CardTitle>
                      <CheckCircle className="h-4 w-4 text-muted-foreground" />
                    </CardHeader>
                    <CardContent>
                      <div className="text-2xl font-bold">120</div>
                      <p className="text-xs text-muted-foreground">
                        本月出库 35
                      </p>
                    </CardContent>
                  </Card>
                </div>

                {/* Detailed Ledger Table */}
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>产品编号</TableHead>
                      <TableHead>批次号</TableHead>
                      <TableHead>登记时间</TableHead>
                      <TableHead>当前状态</TableHead>
                      <TableHead>当前位置</TableHead>
                      <TableHead>测试次数</TableHead>
                      <TableHead>最后更新</TableHead>
                      <TableHead>操作历史</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {products.map((product) => (
                      <TableRow key={product.id}>
                        <TableCell className="font-medium">
                          {product.id}
                        </TableCell>
                        <TableCell>{product.batch}</TableCell>
                        <TableCell>{product.registrationDate}</TableCell>
                        <TableCell>{getStatusBadge(product.status)}</TableCell>
                        <TableCell>{product.location}</TableCell>
                        <TableCell>
                          {product.testCount}/{product.maxTests}
                        </TableCell>
                        <TableCell>2小时前</TableCell>
                        <TableCell>
                          <Button size="sm" variant="outline">
                            查看历史
                          </Button>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}
