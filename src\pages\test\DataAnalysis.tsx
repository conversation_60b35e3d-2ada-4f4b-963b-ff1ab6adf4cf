import { DashboardCard } from "@/components/DashboardCard";
import { StatusChart } from "@/components/StatusChart";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Badge } from "@/components/ui/badge";
import { DatePickerWithRange } from "@/components/ui/date-picker";
import {
  Search,
  Download,
  TrendingUp,
  Clock,
  AlertTriangle,
} from "lucide-react";

const testRecordsData = [
  {
    id: "TR001",
    productId: "A-2024-001",
    testType: "月稳定性测试",
    startTime: "2024-01-10 09:00",
    endTime: "2024-01-10 10:20",
    duration: "1小时20分",
    result: "pass",
    operator: "张三",
    testStation: "测试台-01",
  },
  {
    id: "TR002",
    productId: "A-2024-002",
    testType: "月稳定性测试",
    startTime: "2024-01-11 10:15",
    endTime: "2024-01-11 11:35",
    duration: "1小时20分",
    result: "fail",
    operator: "李四",
    testStation: "测试台-02",
  },
  {
    id: "TR003",
    productId: "A-2024-003",
    testType: "月稳定性测试",
    startTime: "2024-01-12 08:30",
    endTime: "2024-01-12 10:00",
    duration: "1小时30分",
    result: "pass",
    operator: "王五",
    testStation: "测试台-03",
  },
  {
    id: "TR001",
    productId: "A-2024-001",
    testType: "月稳定性测试",
    startTime: "2024-01-10 09:00",
    endTime: "2024-01-10 10:20",
    duration: "1小时20分",
    result: "pass",
    operator: "张三",
    testStation: "测试台-01",
  },
  {
    id: "TR001",
    productId: "A-2024-001",
    testType: "月稳定性测试",
    startTime: "2024-01-10 09:00",
    endTime: "2024-01-10 10:20",
    duration: "1小时20分",
    result: "pass",
    operator: "张三",
    testStation: "测试台-01",
  },
  {
    id: "TR001",
    productId: "A-2024-001",
    testType: "月稳定性测试",
    startTime: "2024-01-10 09:00",
    endTime: "2024-01-10 10:20",
    duration: "1小时20分",
    result: "pass",
    operator: "张三",
    testStation: "测试台-01",
  },
  {
    id: "TR001",
    productId: "A-2024-001",
    testType: "月稳定性测试",
    startTime: "2024-01-10 09:00",
    endTime: "2024-01-10 10:20",
    duration: "1小时20分",
    result: "pass",
    operator: "张三",
    testStation: "测试台-01",
  },
  {
    id: "TR001",
    productId: "A-2024-001",
    testType: "月稳定性测试",
    startTime: "2024-01-10 09:00",
    endTime: "2024-01-10 10:20",
    duration: "1小时20分",
    result: "pass",
    operator: "张三",
    testStation: "测试台-01",
  },
  {
    id: "TR001",
    productId: "A-2024-001",
    testType: "月稳定性测试",
    startTime: "2024-01-10 09:00",
    endTime: "2024-01-10 10:20",
    duration: "1小时20分",
    result: "pass",
    operator: "张三",
    testStation: "测试台-01",
  },
  {
    id: "TR001",
    productId: "A-2024-001",
    testType: "月稳定性测试",
    startTime: "2024-01-10 09:00",
    endTime: "2024-01-10 10:20",
    duration: "1小时20分",
    result: "pass",
    operator: "张三",
    testStation: "测试台-01",
  },
  {
    id: "TR001",
    productId: "A-2024-001",
    testType: "月稳定性测试",
    startTime: "2024-01-10 09:00",
    endTime: "2024-01-10 10:20",
    duration: "1小时20分",
    result: "pass",
    operator: "张三",
    testStation: "测试台-01",
  },
  {
    id: "TR001",
    productId: "A-2024-001",
    testType: "月稳定性测试",
    startTime: "2024-01-10 09:00",
    endTime: "2024-01-10 10:20",
    duration: "1小时20分",
    result: "pass",
    operator: "张三",
    testStation: "测试台-01",
  },
  {
    id: "TR001",
    productId: "A-2024-001",
    testType: "月稳定性测试",
    startTime: "2024-01-10 09:00",
    endTime: "2024-01-10 10:20",
    duration: "1小时20分",
    result: "pass",
    operator: "张三",
    testStation: "测试台-01",
  }
];

const stoplineData = [
  {
    id: "SL001",
    productId: "A-2024-001",
    reason: "设备故障",
    startTime: "2024-01-10 14:30",
    endTime: "2024-01-10 16:15",
    duration: "1小时45分",
    category: "设备问题",
  },
  {
    id: "SL002",
    productId: "A-2024-004",
    reason: "产品异常",
    startTime: "2024-01-12 11:20",
    endTime: "2024-01-12 12:40",
    duration: "1小时20分",
    category: "产品问题",
  },
  {
    id: "SL001",
    productId: "A-2024-001",
    reason: "设备故障",
    startTime: "2024-01-10 14:30",
    endTime: "2024-01-10 16:15",
    duration: "1小时45分",
    category: "设备问题",
  },
  {
    id: "SL002",
    productId: "A-2024-004",
    reason: "产品异常",
    startTime: "2024-01-12 11:20",
    endTime: "2024-01-12 12:40",
    duration: "1小时20分",
    category: "产品问题",
  },
  {
    id: "SL001",
    productId: "A-2024-001",
    reason: "设备故障",
    startTime: "2024-01-10 14:30",
    endTime: "2024-01-10 16:15",
    duration: "1小时45分",
    category: "设备问题",
  },
  {
    id: "SL002",
    productId: "A-2024-004",
    reason: "产品异常",
    startTime: "2024-01-12 11:20",
    endTime: "2024-01-12 12:40",
    duration: "1小时20分",
    category: "产品问题",
  },
  {
    id: "SL001",
    productId: "A-2024-001",
    reason: "设备故障",
    startTime: "2024-01-10 14:30",
    endTime: "2024-01-10 16:15",
    duration: "1小时45分",
    category: "设备问题",
  },
  {
    id: "SL002",
    productId: "A-2024-004",
    reason: "产品异常",
    startTime: "2024-01-12 11:20",
    endTime: "2024-01-12 12:40",
    duration: "1小时20分",
    category: "产品问题",
  },
  {
    id: "SL001",
    productId: "A-2024-001",
    reason: "设备故障",
    startTime: "2024-01-10 14:30",
    endTime: "2024-01-10 16:15",
    duration: "1小时45分",
    category: "设备问题",
  },
  {
    id: "SL002",
    productId: "A-2024-004",
    reason: "产品异常",
    startTime: "2024-01-12 11:20",
    endTime: "2024-01-12 12:40",
    duration: "1小时20分",
    category: "产品问题",
  },
];

const monthlyStoplineData = [
  { name: "1周", equipment: 15, product: 8, other: 3 },
  { name: "2周", equipment: 12, product: 10, other: 2 },
  { name: "3周", equipment: 18, product: 6, other: 4 },
  { name: "4周", equipment: 9, product: 12, other: 1 },
  { name: "5周", equipment: 14, product: 7, other: 5 },
  { name: "6周", equipment: 11, product: 9, other: 2 },
];

const equipmentEfficiencyData = [
  { name: "测试台-01", efficiency: 87 },
  { name: "测试台-02", efficiency: 92 },
  { name: "测试台-03", efficiency: 85 },
  { name: "测试台-04", efficiency: 89 },
  { name: "测试台-05", efficiency: 94 },
  { name: "测试台-06", efficiency: 81 },
  { name: "测试台-07", efficiency: 88 },
];

const testTrendData = [
  { name: "周一", completed: 150, failed: 1 },
  { name: "周二", completed: 206, failed: 2 },
  { name: "周三", completed: 189, failed: 0 },
  { name: "周四", completed: 180, failed: 1 },
  { name: "周五", completed: 210, failed: 1 },
  { name: "周六", completed: 203, failed: 0 },
  { name: "周日", completed: 199, failed: 1 },
];

export default function DataAnalysis() {
  const getResultBadge = (result: string) => {
    return result === "pass" ? (
      <Badge className="bg-green-100 text-green-800">通过</Badge>
    ) : (
      <Badge variant="destructive">失败</Badge>
    );
  };

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h2 className="text-3xl font-bold tracking-tight">数据分析查询</h2>
        <Button>
          <Download className="mr-2 h-4 w-4" />
          导出报告
        </Button>
      </div>

      <Tabs defaultValue="records" className="space-y-4">
        <TabsList>
          <TabsTrigger value="records">测试记录查询</TabsTrigger>
          <TabsTrigger value="stopline">停线详细查询</TabsTrigger>
          <TabsTrigger value="monthly">停线统计</TabsTrigger>
          <TabsTrigger value="efficiency">设备综合效率</TabsTrigger>
        </TabsList>

        <TabsContent value="records">
          <div className="space-y-4">
            {/* Search Filters */}
            <Card>
              <CardHeader>
                <CardTitle>查询条件</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid gap-4 md:grid-cols-4">
                  <div className="space-y-2">
                    <Label htmlFor="productId">产品编号</Label>
                    <Input id="productId" placeholder="A-2024-XXX" />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="testType">测试类型</Label>
                    <Select>
                      <SelectTrigger>
                        <SelectValue placeholder="选择测试类型" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="all">全部</SelectItem>
                        <SelectItem value="monthly">月稳测试</SelectItem>
                        <SelectItem value="stability">稳定性测试</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                  <div className="flex items-end">
                    <Button className="w-full">
                      <Search className="mr-2 h-4 w-4" />
                      查询
                    </Button>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Test Records Table */}
            <Card>
              <CardHeader>
                <CardTitle>测试记录</CardTitle>
              </CardHeader>
              <CardContent>
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>记录编号</TableHead>
                      <TableHead>产品编号</TableHead>
                      <TableHead>测试台</TableHead>
                      <TableHead>测试类型</TableHead>
                      <TableHead>开始时间</TableHead>
                      <TableHead>结束时间</TableHead>
                      <TableHead>持续时间</TableHead>
                      <TableHead>值班人员</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {testRecordsData.map((record) => (
                      <TableRow key={record.id}>
                        <TableCell className="font-medium">
                          {record.id}
                        </TableCell>
                        <TableCell>{record.productId}</TableCell>
                        <TableCell>{record.testStation}</TableCell>
                        <TableCell>{record.testType}</TableCell>
                        <TableCell>{record.startTime}</TableCell>
                        <TableCell>{record.endTime}</TableCell>
                        <TableCell>{record.duration}</TableCell>
                        <TableCell>{record.operator}</TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </CardContent>
            </Card>

            {/* Test Trend Chart */}
            <div className="grid gap-6 md:grid-cols-2">
              <DashboardCard
                title="测试数量统计"
                description="最近一周每天测试数据变化"
              >
                                 <StatusChart
                   type="bar"
                   data={testTrendData}
                   dataKey="completed"
                   nameKey="name"
                   barWidth={40}
                 />
              </DashboardCard>

              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center">
                    <TrendingUp className="mr-2 h-4 w-4" />
                    测试统计摘要
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="flex items-center justify-between">
                    <span className="text-sm font-medium">本月总测试数</span>
                    <span className="text-2xl font-bold">5600</span>
                  </div>
                  
                  <div className="flex items-center justify-between">
                    <span className="text-sm font-medium">平均测试时长</span>
                    <span className="text-2xl font-bold">1.2小时</span>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-sm font-medium">失败测试数</span>
                    <span className="text-2xl font-bold text-red-600">10</span>
                  </div>
                </CardContent>
              </Card>
            </div>
          </div>
        </TabsContent>

        <TabsContent value="stopline">
          <div className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle>停线记录详情</CardTitle>
              </CardHeader>
              <CardContent>
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>停线编号</TableHead>
                      <TableHead>产品编号</TableHead>
                      <TableHead>停线原因</TableHead>
                      <TableHead>开始时间</TableHead>
                      <TableHead>结束时间</TableHead>
                      <TableHead>持续时间</TableHead>
                      <TableHead>问题类别</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {stoplineData.map((record) => (
                      <TableRow key={record.id}>
                        <TableCell className="font-medium">
                          {record.id}
                        </TableCell>
                        <TableCell>{record.productId}</TableCell>
                        <TableCell>{record.reason}</TableCell>
                        <TableCell>{record.startTime}</TableCell>
                        <TableCell>{record.endTime}</TableCell>
                        <TableCell>{record.duration}</TableCell>
                        <TableCell>
                          <Badge
                            variant={
                              record.category === "设备问题"
                                ? "destructive"
                                : "secondary"
                            }
                          >
                            {record.category}
                          </Badge>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="monthly">
          <div className="space-y-6">
            <DashboardCard
              title="停线时间统计"
              description="按周统计不同类型的停线时间"
            >
              <StatusChart
                type="bar"
                data={monthlyStoplineData}
                dataKey="equipment"
                nameKey="name"
                barWidth={50}
              />
            </DashboardCard>

            <div className="grid gap-6 md:grid-cols-3">
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center">
                    <Clock className="mr-2 h-4 w-4" />
                    本月停线统计
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="flex items-center justify-between">
                    <span className="text-sm">设备问题</span>
                    <span className="font-semibold">11次</span>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-sm">产品问题</span>
                    <span className="font-semibold">9次</span>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-sm">其他问题</span>
                    <span className="font-semibold">2次</span>
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center">
                    <AlertTriangle className="mr-2 h-4 w-4" />
                    主要停线原因
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="space-y-2">
                    <div className="flex justify-between text-sm">
                      <span>机械臂故障</span>
                      <span>5次</span>
                    </div>
                    <div className="flex justify-between text-sm">
                      <span>产品异常</span>
                      <span>4次</span>
                    </div>
                    <div className="flex justify-between text-sm">
                      <span>传感器故障</span>
                      <span>3次</span>
                    </div>
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle>停线趋势分析</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="flex items-center justify-between">
                    <span className="text-sm">相比上月</span>
                    <Badge variant="secondary" className="text-green-600">
                      减少 15%
                    </Badge>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-sm">平均停线时长</span>
                    <span className="font-semibold">1.2小时</span>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-sm">最长停线时长</span>
                    <span className="font-semibold">4.5小时</span>
                  </div>
                </CardContent>
              </Card>
            </div>
          </div>
        </TabsContent>

        <TabsContent value="efficiency">
          <div className="space-y-6">
            <DashboardCard
              title="设备综合效率"
              description="各测试台的运行效率统计"
            >
              <StatusChart
                type="bar"
                data={equipmentEfficiencyData}
                dataKey="efficiency"
                nameKey="name"
                barWidth={60}
              />
            </DashboardCard>

            <div className="grid gap-6 md:grid-cols-2">
              <Card>
                <CardHeader>
                  <CardTitle>效率排名</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-3">
                    {equipmentEfficiencyData
                      .sort((a, b) => b.efficiency - a.efficiency)
                      .map((item, index) => (
                        <div
                          key={item.name}
                          className="flex items-center justify-between"
                        >
                          <div className="flex items-center space-x-2">
                            <span className="text-sm font-medium text-gray-500">
                              #{index + 1}
                            </span>
                            <span>{item.name}</span>
                          </div>
                          <div className="flex items-center space-x-2">
                            <div className="w-20 bg-gray-200 rounded-full h-2">
                              <div
                                className="bg-blue-600 h-2 rounded-full"
                                style={{ width: `${item.efficiency}%` }}
                              ></div>
                            </div>
                            <span className="text-sm font-semibold">
                              {item.efficiency}%
                            </span>
                          </div>
                        </div>
                      ))}
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle>效率统计摘要</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="flex items-center justify-between">
                    <span className="text-sm font-medium">平均效率</span>
                    <span className="text-2xl font-bold">88.0%</span>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-sm font-medium">最高效率</span>
                    <span className="text-2xl font-bold text-green-600">
                      94.0%
                    </span>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-sm font-medium">最低效率</span>
                    <span className="text-2xl font-bold text-red-600">
                      81.0%
                    </span>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-sm font-medium">效率目标</span>
                    <span className="text-2xl font-bold">90.0%</span>
                  </div>
                </CardContent>
              </Card>
            </div>
          </div>
        </TabsContent>
      </Tabs>
    </div>
  );
}
