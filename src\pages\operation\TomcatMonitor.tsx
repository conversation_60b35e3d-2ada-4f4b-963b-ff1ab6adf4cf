import { useState, useEffect } from "react";
import {
  <PERSON>,
  CardContent,
  CardDescription,
  Card<PERSON><PERSON>er,
  CardTitle,
} from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, Ta<PERSON>Trigger } from "@/components/ui/tabs";
import { StatusChart } from "@/components/StatusChart";
import { Progress } from "@/components/ui/progress";
import {
  Server,
  Activity,
  MemoryStick,
  Network,
  Clock,
  RefreshCw,
  Download,
  AlertTriangle,
  CheckCircle,
  TrendingUp,
  TrendingDown,
  Zap,
  Users,
  Database,
  FileText,
  Cpu,
  HardDrive,
  Globe,
  Settings,
} from "lucide-react";

interface TomcatMetrics {
  timestamp: string;
  server: {
    version: string;
    uptime: number;
    startTime: string;
    serverInfo: string;
    jvmVersion: string;
    osName: string;
    osVersion: string;
    processorCount: number;
  };
  threadPool: {
    currentThreadCount: number;
    currentThreadsBusy: number;
    maxThreads: number;
    minSpareThreads: number;
    maxSpareThreads: number;
    acceptCount: number;
    connectionCount: number;
    maxConnections: number;
  };
  memory: {
    freeMemory: number;
    totalMemory: number;
    maxMemory: number;
    heapMemoryUsage: {
      init: number;
      used: number;
      committed: number;
      max: number;
    };
    nonHeapMemoryUsage: {
      init: number;
      used: number;
      committed: number;
      max: number;
    };
  };
  requests: {
    requestCount: number;
    errorCount: number;
    bytesReceived: number;
    bytesSent: number;
    processingTime: number;
    maxTime: number;
    minTime: number;
    activeSessions: number;
    maxActiveSessions: number;
  };
  applications: {
    name: string;
    path: string;
    state: "running" | "stopped" | "failed";
    sessions: number;
    servlets: number;
    filters: number;
    startupTime: number;
  }[];
  performance: {
    requestsPerSecond: number;
    averageResponseTime: number;
    errorRate: number;
    throughput: number;
  };
}

export default function TomcatMonitor() {
  const [metrics, setMetrics] = useState<TomcatMetrics[]>([]);
  const [currentMetrics, setCurrentMetrics] = useState<TomcatMetrics | null>(null);
  const [isAutoRefresh, setIsAutoRefresh] = useState(true);

  // 模拟Tomcat数据
  useEffect(() => {
    const generateMetrics = (): TomcatMetrics => ({
      timestamp: new Date().toLocaleString(),
      server: {
        version: "10.1.13",
        uptime: 86400 + Math.random() * 3600,
        startTime: new Date(Date.now() - (86400 + Math.random() * 3600) * 1000).toLocaleString(),
        serverInfo: "Apache Tomcat/10.1.13",
        jvmVersion: "17.0.8+9",
        osName: "Linux",
        osVersion: "5.15.0-91-generic",
        processorCount: 8,
      },
      threadPool: {
        currentThreadCount: 50 + Math.random() * 30,
        currentThreadsBusy: 20 + Math.random() * 40,
        maxThreads: 200,
        minSpareThreads: 10,
        maxSpareThreads: 50,
        acceptCount: 100,
        connectionCount: 80 + Math.random() * 40,
        maxConnections: 8192,
      },
      memory: {
        freeMemory: 512 + Math.random() * 256,
        totalMemory: 2048,
        maxMemory: 4096,
        heapMemoryUsage: {
          init: 256,
          used: 1024 + Math.random() * 512,
          committed: 2048,
          max: 4096,
        },
        nonHeapMemoryUsage: {
          init: 64,
          used: 128 + Math.random() * 64,
          committed: 256,
          max: 512,
        },
      },
      requests: {
        requestCount: 100000 + Math.random() * 50000,
        errorCount: 100 + Math.random() * 50,
        bytesReceived: 1000000000 + Math.random() * 500000000,
        bytesSent: 500000000 + Math.random() * 200000000,
        processingTime: 50 + Math.random() * 100,
        maxTime: 5000 + Math.random() * 2000,
        minTime: 1 + Math.random() * 10,
        activeSessions: 100 + Math.random() * 200,
        maxActiveSessions: 1000,
      },
      applications: [
        {
          name: "ROOT",
          path: "/",
          state: "running",
          sessions: 50 + Math.random() * 30,
          servlets: 5,
          filters: 3,
          startupTime: 2000 + Math.random() * 1000,
        },
        {
          name: "wms-api",
          path: "/api",
          state: "running",
          sessions: 80 + Math.random() * 50,
          servlets: 12,
          filters: 8,
          startupTime: 3000 + Math.random() * 1500,
        },
        {
          name: "wms-admin",
          path: "/admin",
          state: "running",
          sessions: 30 + Math.random() * 20,
          servlets: 8,
          filters: 5,
          startupTime: 2500 + Math.random() * 1000,
        },
      ],
      performance: {
        requestsPerSecond: 100 + Math.random() * 50,
        averageResponseTime: 50 + Math.random() * 100,
        errorRate: (100 + Math.random() * 50) / (100000 + Math.random() * 50000) * 100,
        throughput: 1000 + Math.random() * 500,
      },
    });

    const updateMetrics = () => {
      const newMetrics = generateMetrics();
      setCurrentMetrics(newMetrics);
      setMetrics(prev => [...prev.slice(-59), newMetrics]);
    };

    updateMetrics();
    const interval = setInterval(updateMetrics, 5000);

    return () => clearInterval(interval);
  }, []);

  const getStatusBadge = (state: string) => {
    const statusConfig = {
      running: { label: "运行中", className: "bg-green-100 text-green-800 border-green-200" },
      stopped: { label: "已停止", className: "bg-red-100 text-red-800 border-red-200" },
      failed: { label: "失败", className: "bg-red-100 text-red-800 border-red-200" },
    };
    const config = statusConfig[state as keyof typeof statusConfig];
    return <Badge className={`${config.className} border`}>{config.label}</Badge>;
  };

  if (!currentMetrics) return <div>加载中...</div>;

  const memoryUsage = (currentMetrics.memory.heapMemoryUsage.used / currentMetrics.memory.heapMemoryUsage.max) * 100;
  const threadUsage = (currentMetrics.threadPool.currentThreadsBusy / currentMetrics.threadPool.maxThreads) * 100;
  const errorRate = currentMetrics.performance.errorRate;

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <div>
          <h2 className="text-3xl font-bold tracking-tight">Tomcat性能监控</h2>
          <p className="text-muted-foreground">
            监控Tomcat服务器的性能指标、线程池、内存使用和应用程序状态
          </p>
        </div>
        <div className="flex items-center space-x-3">
          <Button
            variant="outline"
            size="sm"
            onClick={() => setIsAutoRefresh(!isAutoRefresh)}
          >
            <RefreshCw className={`mr-2 h-4 w-4 ${isAutoRefresh ? 'animate-spin' : ''}`} />
            {isAutoRefresh ? '自动刷新' : '手动刷新'}
          </Button>
          <Button variant="outline" size="sm">
            <Download className="mr-2 h-4 w-4" />
            导出报告
          </Button>
        </div>
      </div>

      {/* 关键指标卡片 */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">内存使用率</CardTitle>
            <MemoryStick className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className={`text-2xl font-bold ${memoryUsage > 80 ? 'text-red-600' : memoryUsage > 60 ? 'text-yellow-600' : 'text-green-600'}`}>
              {memoryUsage.toFixed(1)}%
            </div>
            <div className="flex items-center justify-between mt-2">
              <Progress value={memoryUsage} className="flex-1 mr-2" />
              <Badge className={memoryUsage > 80 ? 'bg-red-100 text-red-800 border-red-200' : memoryUsage > 60 ? 'bg-yellow-100 text-yellow-800 border-yellow-200' : 'bg-green-100 text-green-800 border-green-200'}>
                {memoryUsage > 80 ? '高' : memoryUsage > 60 ? '中' : '正常'}
              </Badge>
            </div>
            <p className="text-xs text-muted-foreground mt-2">
              已用: {(currentMetrics.memory.heapMemoryUsage.used / 1024).toFixed(1)}GB / {(currentMetrics.memory.heapMemoryUsage.max / 1024).toFixed(1)}GB
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">线程使用率</CardTitle>
            <Activity className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className={`text-2xl font-bold ${threadUsage > 80 ? 'text-red-600' : threadUsage > 60 ? 'text-yellow-600' : 'text-green-600'}`}>
              {threadUsage.toFixed(1)}%
            </div>
            <div className="flex items-center justify-between mt-2">
              <Progress value={threadUsage} className="flex-1 mr-2" />
              <Badge className={threadUsage > 80 ? 'bg-red-100 text-red-800 border-red-200' : threadUsage > 60 ? 'bg-yellow-100 text-yellow-800 border-yellow-200' : 'bg-green-100 text-green-800 border-green-200'}>
                {threadUsage > 80 ? '高' : threadUsage > 60 ? '中' : '正常'}
              </Badge>
            </div>
            <p className="text-xs text-muted-foreground mt-2">
              忙碌: {currentMetrics.threadPool.currentThreadsBusy} / 总数: {currentMetrics.threadPool.currentThreadCount}
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">请求/秒</CardTitle>
            <Network className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-blue-600">
              {currentMetrics.performance.requestsPerSecond.toFixed(0)}
            </div>
            <div className="flex items-center justify-between mt-2">
              <Progress value={Math.min(currentMetrics.performance.requestsPerSecond / 200 * 100, 100)} className="flex-1 mr-2" />
              <Badge className="bg-blue-100 text-blue-800 border-blue-200">正常</Badge>
            </div>
            <p className="text-xs text-muted-foreground mt-2">
              总请求: {currentMetrics.requests.requestCount.toLocaleString()}
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">错误率</CardTitle>
            <AlertTriangle className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className={`text-2xl font-bold ${errorRate > 5 ? 'text-red-600' : errorRate > 1 ? 'text-yellow-600' : 'text-green-600'}`}>
              {errorRate.toFixed(2)}%
            </div>
            <div className="flex items-center justify-between mt-2">
              <Progress value={Math.min(errorRate * 10, 100)} className="flex-1 mr-2" />
              <Badge className={errorRate > 5 ? 'bg-red-100 text-red-800 border-red-200' : errorRate > 1 ? 'bg-yellow-100 text-yellow-800 border-yellow-200' : 'bg-green-100 text-green-800 border-green-200'}>
                {errorRate > 5 ? '高' : errorRate > 1 ? '中' : '低'}
              </Badge>
            </div>
            <p className="text-xs text-muted-foreground mt-2">
              错误: {currentMetrics.requests.errorCount.toLocaleString()}
            </p>
          </CardContent>
        </Card>
      </div>

      {/* 主要内容区域 */}
      <Tabs defaultValue="overview" className="space-y-4">
        <TabsList>
          <TabsTrigger value="overview">概览</TabsTrigger>
          <TabsTrigger value="threads">线程池</TabsTrigger>
          <TabsTrigger value="memory">内存监控</TabsTrigger>
          <TabsTrigger value="requests">请求监控</TabsTrigger>
          <TabsTrigger value="applications">应用程序</TabsTrigger>
          <TabsTrigger value="performance">性能分析</TabsTrigger>
        </TabsList>

        {/* 概览 */}
        <TabsContent value="overview" className="space-y-4">
          <div className="grid gap-4 md:grid-cols-2">
            <Card>
              <CardHeader>
                <CardTitle>Tomcat性能趋势</CardTitle>
                <CardDescription>内存使用、线程使用和请求频率变化</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="h-80">
                  <StatusChart
                    type="line"
                    data={metrics.map(m => ({
                      time: m.timestamp,
                      memory: (m.memory.heapMemoryUsage.used / m.memory.heapMemoryUsage.max) * 100,
                      threads: (m.threadPool.currentThreadsBusy / m.threadPool.maxThreads) * 100,
                      requests: m.performance.requestsPerSecond,
                    }))}
                    dataKey="memory"
                    nameKey="time"
                    colors={["#ef4444", "#3b82f6", "#10b981"]}
                  />
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>服务器信息</CardTitle>
                <CardDescription>Tomcat服务器基本配置信息</CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <label className="text-sm font-medium">Tomcat版本</label>
                    <p className="text-lg font-semibold">{currentMetrics.server.version}</p>
                  </div>
                  <div>
                    <label className="text-sm font-medium">JVM版本</label>
                    <p className="text-lg font-semibold">{currentMetrics.server.jvmVersion}</p>
                  </div>
                  <div>
                    <label className="text-sm font-medium">操作系统</label>
                    <p className="text-lg font-semibold">{currentMetrics.server.osName}</p>
                  </div>
                  <div>
                    <label className="text-sm font-medium">CPU核心数</label>
                    <p className="text-lg font-semibold">{currentMetrics.server.processorCount}</p>
                  </div>
                </div>
                <div className="space-y-3">
                  <div className="flex justify-between">
                    <span>运行时间</span>
                    <span>{Math.floor(currentMetrics.server.uptime / 3600)}小时</span>
                  </div>
                  <div className="flex justify-between">
                    <span>启动时间</span>
                    <span>{currentMetrics.server.startTime}</span>
                  </div>
                  <div className="flex justify-between">
                    <span>活跃会话</span>
                    <span>{currentMetrics.requests.activeSessions}</span>
                  </div>
                  <div className="flex justify-between">
                    <span>最大会话数</span>
                    <span>{currentMetrics.requests.maxActiveSessions}</span>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        {/* 线程池 */}
        <TabsContent value="threads" className="space-y-4">
          <div className="grid gap-4 md:grid-cols-2">
            <Card>
              <CardHeader>
                <CardTitle>线程池状态</CardTitle>
                <CardDescription>线程池使用情况实时监控</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="h-80">
                  <StatusChart
                    type="line"
                    data={metrics.map(m => ({
                      time: m.timestamp,
                      current: m.threadPool.currentThreadCount,
                      busy: m.threadPool.currentThreadsBusy,
                      max: m.threadPool.maxThreads,
                    }))}
                    dataKey="current"
                    nameKey="time"
                    colors={["#3b82f6", "#ef4444", "#10b981"]}
                  />
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>线程池配置</CardTitle>
                <CardDescription>线程池详细配置信息</CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-3">
                  <div className="flex justify-between">
                    <span>当前线程数</span>
                    <span>{currentMetrics.threadPool.currentThreadCount}</span>
                  </div>
                  <Progress value={(currentMetrics.threadPool.currentThreadCount / currentMetrics.threadPool.maxThreads) * 100} />
                  
                  <div className="flex justify-between">
                    <span>忙碌线程数</span>
                    <span>{currentMetrics.threadPool.currentThreadsBusy}</span>
                  </div>
                  <Progress value={(currentMetrics.threadPool.currentThreadsBusy / currentMetrics.threadPool.maxThreads) * 100} />
                  
                  <div className="flex justify-between">
                    <span>最大线程数</span>
                    <span>{currentMetrics.threadPool.maxThreads}</span>
                  </div>
                  <Progress value={100} />
                  
                  <div className="flex justify-between">
                    <span>最小空闲线程</span>
                    <span>{currentMetrics.threadPool.minSpareThreads}</span>
                  </div>
                  <Progress value={(currentMetrics.threadPool.minSpareThreads / currentMetrics.threadPool.maxThreads) * 100} />
                  
                  <div className="flex justify-between">
                    <span>最大空闲线程</span>
                    <span>{currentMetrics.threadPool.maxSpareThreads}</span>
                  </div>
                  <Progress value={(currentMetrics.threadPool.maxSpareThreads / currentMetrics.threadPool.maxThreads) * 100} />
                </div>
                
                <div className="pt-4 border-t">
                  <div className="flex justify-between">
                    <span>连接数</span>
                    <span>{currentMetrics.threadPool.connectionCount} / {currentMetrics.threadPool.maxConnections}</span>
                  </div>
                  <Progress value={(currentMetrics.threadPool.connectionCount / currentMetrics.threadPool.maxConnections) * 100} />
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        {/* 内存监控 */}
        <TabsContent value="memory" className="space-y-4">
          <div className="grid gap-4 md:grid-cols-2">
            <Card>
              <CardHeader>
                <CardTitle>内存使用趋势</CardTitle>
                <CardDescription>堆内存和非堆内存使用情况</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="h-80">
                  <StatusChart
                    type="line"
                    data={metrics.map(m => ({
                      time: m.timestamp,
                      heapUsed: m.memory.heapMemoryUsage.used / 1024,
                      heapCommitted: m.memory.heapMemoryUsage.committed / 1024,
                      nonHeapUsed: m.memory.nonHeapMemoryUsage.used / 1024,
                    }))}
                    dataKey="heapUsed"
                    nameKey="time"
                    colors={["#3b82f6", "#10b981", "#f59e0b"]}
                  />
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>内存详细信息</CardTitle>
                <CardDescription>JVM内存使用情况详细分析</CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div>
                  <h4 className="font-medium mb-3">堆内存</h4>
                  <div className="space-y-3">
                    <div className="flex justify-between">
                      <span>已用内存</span>
                      <span>{(currentMetrics.memory.heapMemoryUsage.used / 1024).toFixed(1)}GB</span>
                    </div>
                    <Progress value={(currentMetrics.memory.heapMemoryUsage.used / currentMetrics.memory.heapMemoryUsage.max) * 100} />
                    
                    <div className="flex justify-between">
                      <span>已提交内存</span>
                      <span>{(currentMetrics.memory.heapMemoryUsage.committed / 1024).toFixed(1)}GB</span>
                    </div>
                    <Progress value={(currentMetrics.memory.heapMemoryUsage.committed / currentMetrics.memory.heapMemoryUsage.max) * 100} />
                    
                    <div className="flex justify-between">
                      <span>最大内存</span>
                      <span>{(currentMetrics.memory.heapMemoryUsage.max / 1024).toFixed(1)}GB</span>
                    </div>
                    <Progress value={100} />
                  </div>
                </div>
                
                <div className="pt-4 border-t">
                  <h4 className="font-medium mb-3">非堆内存</h4>
                  <div className="space-y-3">
                    <div className="flex justify-between">
                      <span>已用内存</span>
                      <span>{(currentMetrics.memory.nonHeapMemoryUsage.used / 1024).toFixed(1)}GB</span>
                    </div>
                    <Progress value={(currentMetrics.memory.nonHeapMemoryUsage.used / currentMetrics.memory.nonHeapMemoryUsage.max) * 100} />
                    
                    <div className="flex justify-between">
                      <span>已提交内存</span>
                      <span>{(currentMetrics.memory.nonHeapMemoryUsage.committed / 1024).toFixed(1)}GB</span>
                    </div>
                    <Progress value={(currentMetrics.memory.nonHeapMemoryUsage.committed / currentMetrics.memory.nonHeapMemoryUsage.max) * 100} />
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        {/* 请求监控 */}
        <TabsContent value="requests" className="space-y-4">
          <div className="grid gap-4 md:grid-cols-2">
            <Card>
              <CardHeader>
                <CardTitle>请求处理统计</CardTitle>
                <CardDescription>请求数量和响应时间趋势</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="h-80">
                  <StatusChart
                    type="line"
                    data={metrics.map(m => ({
                      time: m.timestamp,
                      requests: m.performance.requestsPerSecond,
                      responseTime: m.performance.averageResponseTime,
                      errors: m.performance.errorRate,
                    }))}
                    dataKey="requests"
                    nameKey="time"
                    colors={["#3b82f6", "#f59e0b", "#ef4444"]}
                  />
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>请求详细信息</CardTitle>
                <CardDescription>请求处理性能指标</CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <label className="text-sm font-medium">总请求数</label>
                    <p className="text-2xl font-bold">{currentMetrics.requests.requestCount.toLocaleString()}</p>
                  </div>
                  <div>
                    <label className="text-sm font-medium">错误请求数</label>
                    <p className="text-2xl font-bold text-red-600">{currentMetrics.requests.errorCount.toLocaleString()}</p>
                  </div>
                  <div>
                    <label className="text-sm font-medium">平均响应时间</label>
                    <p className="text-2xl font-bold">{currentMetrics.performance.averageResponseTime.toFixed(0)}ms</p>
                  </div>
                  <div>
                    <label className="text-sm font-medium">最大响应时间</label>
                    <p className="text-2xl font-bold">{currentMetrics.requests.maxTime.toFixed(0)}ms</p>
                  </div>
                </div>
                <div className="space-y-3">
                  <div className="flex justify-between">
                    <span>接收字节数</span>
                    <span>{(currentMetrics.requests.bytesReceived / 1024 / 1024).toFixed(1)}MB</span>
                  </div>
                  <Progress value={Math.min((currentMetrics.requests.bytesReceived / 1024 / 1024 / 1024) * 100, 100)} />
                  
                  <div className="flex justify-between">
                    <span>发送字节数</span>
                    <span>{(currentMetrics.requests.bytesSent / 1024 / 1024).toFixed(1)}MB</span>
                  </div>
                  <Progress value={Math.min((currentMetrics.requests.bytesSent / 1024 / 1024 / 1024) * 100, 100)} />
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        {/* 应用程序 */}
        <TabsContent value="applications" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>部署的应用程序</CardTitle>
              <CardDescription>Tomcat中部署的Web应用程序状态</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {currentMetrics.applications.map((app) => (
                  <div
                    key={app.name}
                    className="flex items-center justify-between p-4 border rounded-lg"
                  >
                    <div className="flex items-center space-x-4">
                      <div>
                        <p className="font-medium">{app.name}</p>
                        <p className="text-sm text-muted-foreground">
                          路径: {app.path}
                        </p>
                      </div>
                      {getStatusBadge(app.state)}
                    </div>
                    <div className="text-right">
                      <div className="grid grid-cols-2 gap-4 text-sm">
                        <div>
                          <p className="font-medium">{app.sessions}</p>
                          <p className="text-xs text-muted-foreground">会话数</p>
                        </div>
                        <div>
                          <p className="font-medium">{app.servlets}</p>
                          <p className="text-xs text-muted-foreground">Servlet</p>
                        </div>
                        <div>
                          <p className="font-medium">{app.filters}</p>
                          <p className="text-xs text-muted-foreground">过滤器</p>
                        </div>
                        <div>
                          <p className="font-medium">{app.startupTime}ms</p>
                          <p className="text-xs text-muted-foreground">启动时间</p>
                        </div>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* 性能分析 */}
        <TabsContent value="performance" className="space-y-4">
          <div className="grid gap-4 md:grid-cols-2">
            <Card>
              <CardHeader>
                <CardTitle>性能指标趋势</CardTitle>
                <CardDescription>关键性能指标的历史变化</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="h-80">
                  <StatusChart
                    type="line"
                    data={metrics.map(m => ({
                      time: m.timestamp,
                      throughput: m.performance.throughput,
                      responseTime: m.performance.averageResponseTime,
                      errorRate: m.performance.errorRate * 100,
                    }))}
                    dataKey="throughput"
                    nameKey="time"
                    colors={["#10b981", "#f59e0b", "#ef4444"]}
                  />
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>性能统计</CardTitle>
                <CardDescription>当前性能指标汇总</CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <label className="text-sm font-medium">吞吐量</label>
                    <p className="text-2xl font-bold">{currentMetrics.performance.throughput.toFixed(0)} req/s</p>
                  </div>
                  <div>
                    <label className="text-sm font-medium">平均响应时间</label>
                    <p className="text-2xl font-bold">{currentMetrics.performance.averageResponseTime.toFixed(0)}ms</p>
                  </div>
                  <div>
                    <label className="text-sm font-medium">错误率</label>
                    <p className="text-2xl font-bold">{currentMetrics.performance.errorRate.toFixed(3)}%</p>
                  </div>
                  <div>
                    <label className="text-sm font-medium">请求/秒</label>
                    <p className="text-2xl font-bold">{currentMetrics.performance.requestsPerSecond.toFixed(0)}</p>
                  </div>
                </div>
                <div className="space-y-3">
                  <div className="flex justify-between">
                    <span>最小响应时间</span>
                    <span>{currentMetrics.requests.minTime.toFixed(0)}ms</span>
                  </div>
                  <div className="flex justify-between">
                    <span>最大响应时间</span>
                    <span>{currentMetrics.requests.maxTime.toFixed(0)}ms</span>
                  </div>
                  <div className="flex justify-between">
                    <span>处理时间</span>
                    <span>{currentMetrics.requests.processingTime.toFixed(0)}ms</span>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>
      </Tabs>
    </div>
  );
} 