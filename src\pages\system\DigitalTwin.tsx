import { useState, useEffect } from "react";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/tabs";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Network,
  Activity,
  Package,
  ArrowRightLeft,
  Battery,
  Clock,
  CheckCircle,
  AlertCircle,
  XCircle,
  RefreshCw,
  Download,
  Filter,
  ArrowUp,
  ArrowDown,
  Truck,
  Bot,
  TestTube2,
  Zap,
} from "lucide-react";

interface InterfaceLog {
  id: string;
  timestamp: string;
  type: "outbound_test" | "inbound_storage" | "vehicle_charging";
  productId?: string;
  vehicleId?: string;
  step: number;
  totalSteps: number;
  stepName: string;
  status: "success" | "running" | "failed" | "pending";
  message: string;
  duration: number;
  details?: string;
}

export default function DigitalTwin() {
  const [selectedType, setSelectedType] = useState("all");
  const [selectedStatus, setSelectedStatus] = useState("all");
  const [autoRefresh, setAutoRefresh] = useState(true);
  const [refreshInterval, setRefreshInterval] = useState(5000);

  // 接口日志数据
  const [interfaceLogs] = useState<InterfaceLog[]>([
    // 产品出库测试日志
    {
      id: "LOG-001",
      timestamp: "2024-01-10 14:25:33",
      type: "outbound_test",
      productId: "P12345",
      step: 1,
      totalSteps: 6,
      stepName: "通知MES产品出库",
      status: "success",
      message: "ICS通知MES产品P12345开始出库测试",
      duration: 1.2,
      details: "MES响应码: 200, 执行时间: 1.2s",
    },
    {
      id: "LOG-002",
      timestamp: "2024-01-10 14:25:35",
      type: "outbound_test",
      productId: "P12345",
      step: 2,
      totalSteps: 6,
      stepName: "桁架执行出库操作",
      status: "success",
      message: "桁架系统将产品P12345从货位A-01搬运到接驳台-02",
      duration: 45.3,
      details: "桁架编号: TRUSS-01, 起始货位: A-01, 目标接驳台: 接驳台-02",
    },
    {
      id: "LOG-003",
      timestamp: "2024-01-10 14:26:20",
      type: "outbound_test",
      productId: "P12345",
      step: 3,
      totalSteps: 6,
      stepName: "AMR小车执行出库操作",
      status: "running",
      message: "AMR-03将产品P12345从接驳台-02搬运到测试台-01",
      duration: 12.8,
      details: "AMR编号: AMR-03, 起始位置: 接驳台-02, 目标位置: 测试台-01",
    },
    {
      id: "LOG-004",
      timestamp: "2024-01-10 14:24:15",
      type: "outbound_test",
      productId: "P67890",
      step: 4,
      totalSteps: 6,
      stepName: "测试台机械臂连接电缆",
      status: "success",
      message: "测试台-05机械臂连接测试电脑和产品P67890之间的电缆线",
      duration: 8.5,
      details: "测试台编号: 测试台-05, 机械臂状态: 连接完成",
    },
    {
      id: "LOG-005",
      timestamp: "2024-01-10 14:24:24",
      type: "outbound_test",
      productId: "P67890",
      step: 5,
      totalSteps: 6,
      stepName: "ICS通知MES产品就绪",
      status: "success",
      message: "ICS通知MES产品P67890就绪，可开始测试",
      duration: 0.8,
      details: "MES响应码: 200, 产品状态: 就绪",
    },
    {
      id: "LOG-006",
      timestamp: "2024-01-10 14:24:25",
      type: "outbound_test",
      productId: "P67890",
      step: 6,
      totalSteps: 6,
      stepName: "MES通知产品开始测试",
      status: "success",
      message: "MES通知产品P67890开始测试",
      duration: 0.5,
      details: "测试程序: AUTO_TEST_V2.1, 预计测试时间: 25分钟",
    },

    // 产品测试完毕入库日志
    {
      id: "LOG-007",
      timestamp: "2024-01-10 14:22:10",
      type: "inbound_storage",
      productId: "P11111",
      step: 1,
      totalSteps: 6,
      stepName: "入库通知",
      status: "success",
      message: "MES通知ICS产品P11111测试完毕",
      duration: 0.3,
      details: "测试结果: PASS, 测试时间: 24分钟",
    },
    {
      id: "LOG-008",
      timestamp: "2024-01-10 14:22:11",
      type: "inbound_storage",
      productId: "P11111",
      step: 2,
      totalSteps: 6,
      stepName: "告知MES入库",
      status: "success",
      message: "ICS告知MES开始执行P11111入库操作",
      duration: 0.5,
      details: "入库模式: 自动入库, 目标货位: B-15",
    },
    {
      id: "LOG-009",
      timestamp: "2024-01-10 14:22:12",
      type: "inbound_storage",
      productId: "P11111",
      step: 3,
      totalSteps: 6,
      stepName: "测试系统断开电缆连接",
      status: "success",
      message: "测试系统断开测试电脑与产品P11111之间的电缆",
      duration: 6.2,
      details: "测试台编号: 测试台-03, 断开状态: 成功",
    },
    {
      id: "LOG-010",
      timestamp: "2024-01-10 14:22:18",
      type: "inbound_storage",
      productId: "P11111",
      step: 4,
      totalSteps: 6,
      stepName: "AMR执行入库",
      status: "failed",
      message: "AMR车将产品P11111从测试台-03搬运到接驳台-01失败",
      duration: 15.2,
      details: "AMR编号: AMR-02, 错误代码: NAV_ERROR_001, 原因: 导航路径异常",
    },

    // 车辆充电日志
    {
      id: "LOG-011",
      timestamp: "2024-01-10 14:20:05",
      type: "vehicle_charging",
      vehicleId: "AMR-04",
      step: 1,
      totalSteps: 5,
      stepName: "WCS设置车辆不可用",
      status: "success",
      message: "WCS根据AMR-04电量18%将车状态改为不可用",
      duration: 0.2,
      details: "当前电量: 18%, 阈值: 20%, 车辆状态: 不可用",
    },
    {
      id: "LOG-012",
      timestamp: "2024-01-10 14:20:06",
      type: "vehicle_charging",
      vehicleId: "AMR-04",
      step: 2,
      totalSteps: 5,
      stepName: "WCS调度AMR回待命点",
      status: "success",
      message: "WCS调度AMR-04回到待命点",
      duration: 32.5,
      details: "当前位置: 测试台-07, 目标位置: 待命点-04, 路径: 最优路径",
    },
    {
      id: "LOG-013",
      timestamp: "2024-01-10 14:20:38",
      type: "vehicle_charging",
      vehicleId: "AMR-04",
      step: 3,
      totalSteps: 5,
      stepName: "WCS调度AMR到充电桩",
      status: "success",
      message: "WCS调度AMR-04从待命点到充电桩-02进行充电",
      duration: 18.7,
      details: "起始位置: 待命点-04, 充电桩: 充电桩-02, 充电功率: 5kW",
    },
    {
      id: "LOG-014",
      timestamp: "2024-01-10 14:20:57",
      type: "vehicle_charging",
      vehicleId: "AMR-04",
      step: 4,
      totalSteps: 5,
      stepName: "AMR充电完成自动返回",
      status: "pending",
      message: "AMR-04充电至100%后自动返回待命点",
      duration: 0,
      details: "当前电量: 85%, 预计充电完成时间: 约12分钟",
    },
    {
      id: "LOG-015",
      timestamp: "2024-01-10 14:19:15",
      type: "vehicle_charging",
      vehicleId: "AMR-01",
      step: 5,
      totalSteps: 5,
      stepName: "AMR通知WCS充电完成",
      status: "success",
      message: "AMR-01通知WCS充电完成，WCS将车辆状态改为可用",
      duration: 0.3,
      details: "充电完成电量: 100%, 充电时间: 28分钟, 车辆状态: 可用",
    },
  ]);

  const filteredLogs = interfaceLogs.filter((log) => {
    const typeMatch = selectedType === "all" || log.type === selectedType;
    const statusMatch = selectedStatus === "all" || log.status === statusMatch;
    return typeMatch && statusMatch;
  });

  const getStatusBadge = (status: string) => {
    const statusConfig = {
      success: {
        label: "成功",
        className: "bg-green-100 text-green-800 border-green-200",
        icon: CheckCircle,
      },
      running: {
        label: "进行中",
        className: "bg-blue-100 text-blue-800 border-blue-200",
        icon: Activity,
      },
      failed: {
        label: "失败",
        className: "bg-red-100 text-red-800 border-red-200",
        icon: XCircle,
      },
      pending: {
        label: "等待中",
        className: "bg-yellow-100 text-yellow-800 border-yellow-200",
        icon: Clock,
      },
    };

    const config = statusConfig[status as keyof typeof statusConfig];
    const IconComponent = config.icon;
    return (
      <Badge className={`${config.className} border flex items-center gap-1`}>
        <IconComponent className="h-3 w-3" />
        {config.label}
      </Badge>
    );
  };

  const getTypeInfo = (type: string) => {
    const typeConfig = {
      outbound_test: {
        label: "产品出库测试",
        icon: ArrowUp,
        color: "text-blue-600",
        bgColor: "bg-blue-50",
      },
      inbound_storage: {
        label: "产品测试完毕入库",
        icon: ArrowDown,
        color: "text-green-600",
        bgColor: "bg-green-50",
      },
      vehicle_charging: {
        label: "车辆充电",
        icon: Battery,
        color: "text-orange-600",
        bgColor: "bg-orange-50",
      },
    };

    return typeConfig[type as keyof typeof typeConfig];
  };

  const getStepIcon = (type: string, step: number) => {
    if (type === "outbound_test") {
      const icons = [Network, Bot, Truck, TestTube2, Network, Activity];
      return icons[step - 1] || Activity;
    } else if (type === "inbound_storage") {
      const icons = [Network, Network, TestTube2, Truck, Bot, Network];
      return icons[step - 1] || Activity;
    } else if (type === "vehicle_charging") {
      const icons = [Network, Truck, Zap, Battery, Network];
      return icons[step - 1] || Activity;
    }
    return Activity;
  };

  // 统计数据
  const totalLogs = interfaceLogs.length;
  const successLogs = interfaceLogs.filter(
    (log) => log.status === "success",
  ).length;
  const runningLogs = interfaceLogs.filter(
    (log) => log.status === "running",
  ).length;
  const failedLogs = interfaceLogs.filter(
    (log) => log.status === "failed",
  ).length;

  useEffect(() => {
    let interval: NodeJS.Timeout;
    if (autoRefresh) {
      interval = setInterval(() => {
        // 模拟数据刷新
        console.log("Auto refreshing data...");
      }, refreshInterval);
    }
    return () => clearInterval(interval);
  }, [autoRefresh, refreshInterval]);

  return (
    <div className="min-h-screen bg-white text-gray-900 p-6">
      <div className="space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div>
            <h2 className="text-3xl font-bold tracking-tight bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
              数字孪生接口日志
            </h2>
            <p className="text-gray-600 mt-2">
              实时监控推送给数字孪生系统的接口调用日志
            </p>
          </div>
          <div className="flex items-center space-x-3">
            <Button
              variant={autoRefresh ? "default" : "outline"}
              size="sm"
              onClick={() => setAutoRefresh(!autoRefresh)}
              className="flex items-center gap-2"
            >
              <RefreshCw
                className={`h-4 w-4 ${autoRefresh ? "animate-spin" : ""}`}
              />
              {autoRefresh ? "自动刷新" : "手动刷新"}
            </Button>

            <Button
              variant="outline"
              size="sm"
              className="flex items-center gap-2"
            >
              <Download className="h-4 w-4" />
              导出日志
            </Button>
          </div>
        </div>

        {/* Statistics Cards */}
        <div className="grid gap-6 md:grid-cols-4">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium text-gray-700">
                总日志数
              </CardTitle>
              <Network className="h-4 w-4 text-blue-600" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-blue-600">
                {totalLogs}
              </div>
              <p className="text-xs text-gray-500">今日接口调用次数</p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium text-gray-700">
                成功执行
              </CardTitle>
              <CheckCircle className="h-4 w-4 text-green-600" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-green-600">
                {successLogs}
              </div>
              <p className="text-xs text-gray-500">
                成功率 {Math.round((successLogs / totalLogs) * 100)}%
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium text-gray-700">
                进行中
              </CardTitle>
              <Activity className="h-4 w-4 text-blue-600" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-blue-600">
                {runningLogs}
              </div>
              <p className="text-xs text-gray-500">正在执行的任务</p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium text-gray-700">
                执行失败
              </CardTitle>
              <XCircle className="h-4 w-4 text-red-600" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-red-600">
                {failedLogs}
              </div>
              <p className="text-xs text-gray-500">需要处理的异常</p>
            </CardContent>
          </Card>
        </div>

        {/* Main Content */}
        <Tabs defaultValue="all_logs" className="space-y-4">
          <TabsList className="bg-gray-100">
            <TabsTrigger
              value="all_logs"
              className="data-[state=active]:bg-white"
            >
              所有日志
            </TabsTrigger>
            <TabsTrigger
              value="outbound"
              className="data-[state=active]:bg-white"
            >
              出库测试
            </TabsTrigger>
            <TabsTrigger
              value="inbound"
              className="data-[state=active]:bg-white"
            >
              入库存储
            </TabsTrigger>
            <TabsTrigger
              value="charging"
              className="data-[state=active]:bg-white"
            >
              车辆充电
            </TabsTrigger>
          </TabsList>

          {/* All Logs Tab */}
          <TabsContent value="all_logs" className="space-y-4">
            {/* Filters */}
            <Card>
              <CardHeader>
                <CardTitle className="text-gray-800">筛选条件</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid gap-4 md:grid-cols-3">
                  <div className="space-y-2">
                    <label className="text-sm font-medium text-gray-700">
                      日志类型
                    </label>
                    <Select
                      value={selectedType}
                      onValueChange={setSelectedType}
                    >
                      <SelectTrigger className="bg-white border-gray-300">
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="all">全部类型</SelectItem>
                        <SelectItem value="outbound_test">
                          产品出库测试
                        </SelectItem>
                        <SelectItem value="inbound_storage">
                          产品测试完毕入库
                        </SelectItem>
                        <SelectItem value="vehicle_charging">
                          车辆充电
                        </SelectItem>
                      </SelectContent>
                    </Select>
                  </div>

                  <div className="space-y-2">
                    <label className="text-sm font-medium text-gray-700">
                      执行状态
                    </label>
                    <Select
                      value={selectedStatus}
                      onValueChange={setSelectedStatus}
                    >
                      <SelectTrigger className="bg-white border-gray-300">
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="all">全部状态</SelectItem>
                        <SelectItem value="success">成功</SelectItem>
                        <SelectItem value="running">进行中</SelectItem>
                        <SelectItem value="failed">失败</SelectItem>
                        <SelectItem value="pending">等待中</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>

                  <div className="flex items-end">
                    <Button className="w-full bg-blue-600 hover:bg-blue-700">
                      <Filter className="h-4 w-4 mr-2" />
                      应用筛选
                    </Button>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Logs Table */}
            <Card>
              <CardHeader>
                <CardTitle className="text-gray-800">接口日志详情</CardTitle>
                <CardDescription className="text-gray-600">
                  显示 {filteredLogs.length} 条日志记录
                </CardDescription>
              </CardHeader>
              <CardContent>
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead className="text-gray-700">时间</TableHead>
                      <TableHead className="text-gray-700">类型</TableHead>
                      <TableHead className="text-gray-700">步骤</TableHead>
                      <TableHead className="text-gray-700">对象ID</TableHead>
                      <TableHead className="text-gray-700">执行内容</TableHead>
                      <TableHead className="text-gray-700">状态</TableHead>
                      <TableHead className="text-gray-700">耗时</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {filteredLogs.map((log) => {
                      const typeInfo = getTypeInfo(log.type);
                      const StepIcon = getStepIcon(log.type, log.step);
                      return (
                        <TableRow key={log.id} className="hover:bg-gray-50">
                          <TableCell className="text-gray-900">
                            {log.timestamp}
                          </TableCell>
                          <TableCell>
                            <div
                              className={`flex items-center space-x-2 p-2 rounded-lg ${typeInfo.bgColor}`}
                            >
                              <typeInfo.icon
                                className={`h-4 w-4 ${typeInfo.color}`}
                              />
                              <span
                                className={`text-sm font-medium ${typeInfo.color}`}
                              >
                                {typeInfo.label}
                              </span>
                            </div>
                          </TableCell>
                          <TableCell>
                            <div className="flex items-center space-x-2">
                              <StepIcon className="h-4 w-4 text-gray-600" />
                              <span className="text-sm text-gray-900">
                                {log.step}/{log.totalSteps}
                              </span>
                              <span className="text-xs text-gray-500">
                                {log.stepName}
                              </span>
                            </div>
                          </TableCell>
                          <TableCell className="text-gray-900">
                            {log.productId || log.vehicleId || "-"}
                          </TableCell>
                          <TableCell className="max-w-md">
                            <div className="text-sm text-gray-900">
                              {log.message}
                            </div>
                            {log.details && (
                              <div className="text-xs text-gray-500 mt-1">
                                {log.details}
                              </div>
                            )}
                          </TableCell>
                          <TableCell>{getStatusBadge(log.status)}</TableCell>
                          <TableCell className="text-gray-900">
                            {log.duration > 0 ? `${log.duration}s` : "-"}
                          </TableCell>
                        </TableRow>
                      );
                    })}
                  </TableBody>
                </Table>
              </CardContent>
            </Card>
          </TabsContent>

          {/* Outbound Tab */}
          <TabsContent value="outbound" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle className="text-gray-800 flex items-center gap-2">
                  <ArrowUp className="h-5 w-5 text-blue-600" />
                  产品出库测试流程
                </CardTitle>
                <CardDescription className="text-gray-600">
                  显示产品出库测试的6个执行步骤
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {interfaceLogs
                    .filter((log) => log.type === "outbound_test")
                    .map((log) => {
                      const StepIcon = getStepIcon(log.type, log.step);
                      return (
                        <div
                          key={log.id}
                          className="flex items-center space-x-4 p-4 bg-blue-50 rounded-lg border border-blue-200"
                        >
                          <div className="flex-shrink-0">
                            <div className="w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center">
                              <StepIcon className="h-5 w-5 text-blue-600" />
                            </div>
                          </div>
                          <div className="flex-1">
                            <div className="flex items-center space-x-2 mb-1">
                              <span className="text-sm font-medium text-blue-600">
                                步骤 {log.step}: {log.stepName}
                              </span>
                              {getStatusBadge(log.status)}
                            </div>
                            <div className="text-sm text-gray-700">
                              {log.message}
                            </div>
                            <div className="text-xs text-gray-500 mt-1">
                              {log.timestamp} • 耗时: {log.duration}s
                            </div>
                          </div>
                        </div>
                      );
                    })}
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          {/* Inbound Tab */}
          <TabsContent value="inbound" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle className="text-gray-800 flex items-center gap-2">
                  <ArrowDown className="h-5 w-5 text-green-600" />
                  产品测试完毕入库流程
                </CardTitle>
                <CardDescription className="text-gray-600">
                  显示产品测试完毕入库的6个执行步骤
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {interfaceLogs
                    .filter((log) => log.type === "inbound_storage")
                    .map((log) => {
                      const StepIcon = getStepIcon(log.type, log.step);
                      return (
                        <div
                          key={log.id}
                          className="flex items-center space-x-4 p-4 bg-green-50 rounded-lg border border-green-200"
                        >
                          <div className="flex-shrink-0">
                            <div className="w-10 h-10 bg-green-100 rounded-full flex items-center justify-center">
                              <StepIcon className="h-5 w-5 text-green-600" />
                            </div>
                          </div>
                          <div className="flex-1">
                            <div className="flex items-center space-x-2 mb-1">
                              <span className="text-sm font-medium text-green-600">
                                步骤 {log.step}: {log.stepName}
                              </span>
                              {getStatusBadge(log.status)}
                            </div>
                            <div className="text-sm text-gray-700">
                              {log.message}
                            </div>
                            <div className="text-xs text-gray-500 mt-1">
                              {log.timestamp} • 耗时: {log.duration}s
                            </div>
                          </div>
                        </div>
                      );
                    })}
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          {/* Charging Tab */}
          <TabsContent value="charging" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle className="text-gray-800 flex items-center gap-2">
                  <Battery className="h-5 w-5 text-orange-600" />
                  车辆充电流程
                </CardTitle>
                <CardDescription className="text-gray-600">
                  显示AMR车辆充电的5个执行步骤
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {interfaceLogs
                    .filter((log) => log.type === "vehicle_charging")
                    .map((log) => {
                      const StepIcon = getStepIcon(log.type, log.step);
                      return (
                        <div
                          key={log.id}
                          className="flex items-center space-x-4 p-4 bg-orange-50 rounded-lg border border-orange-200"
                        >
                          <div className="flex-shrink-0">
                            <div className="w-10 h-10 bg-orange-100 rounded-full flex items-center justify-center">
                              <StepIcon className="h-5 w-5 text-orange-600" />
                            </div>
                          </div>
                          <div className="flex-1">
                            <div className="flex items-center space-x-2 mb-1">
                              <span className="text-sm font-medium text-orange-600">
                                步骤 {log.step}: {log.stepName}
                              </span>
                              {getStatusBadge(log.status)}
                            </div>
                            <div className="text-sm text-gray-700">
                              {log.message}
                            </div>
                            <div className="text-xs text-gray-500 mt-1">
                              {log.timestamp} • 耗时:{" "}
                              {log.duration > 0 ? `${log.duration}s` : "进行中"}
                            </div>
                          </div>
                        </div>
                      );
                    })}
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </div>
    </div>
  );
}
