import { useState, useEffect } from "react";
import { useNavigate, useLocation } from "react-router-dom";

export default function Diagnostic() {
  const [status, setStatus] = useState<string[]>([]);
  const navigate = useNavigate();
  const location = useLocation();

  useEffect(() => {
    const checks = [];

    // Basic React check
    checks.push("✅ React 组件渲染正常");

    // React Router check
    checks.push(`✅ 当前路由: ${location.pathname}`);

    // State management check
    try {
      useState();
      checks.push("✅ React Hooks 工作正常");
    } catch (error) {
      checks.push("❌ React Hooks 错误");
    }

    // CSS check
    checks.push("✅ CSS 样式加载正常");

    // Async operation check
    Promise.resolve().then(() => {
      checks.push("✅ Promise 支持正常");
      setStatus([...checks]);
    });

    setStatus(checks);
  }, [location.pathname]);

  const testNavigation = () => {
    try {
      navigate("/");
      setStatus((prev) => [...prev, "✅ 导航功能正常"]);
    } catch (error) {
      setStatus((prev) => [...prev, `❌ 导航错误: ${error}`]);
    }
  };

  return (
    <div className="min-h-screen bg-gray-100 p-8">
      <div className="max-w-4xl mx-auto">
        <h1 className="text-3xl font-bold mb-6 text-gray-800">
          🔧 系统诊断页面
        </h1>

        <div className="bg-white rounded-lg shadow-lg p-6 mb-6">
          <h2 className="text-xl font-semibold mb-4">系统状态检查</h2>
          <div className="space-y-2">
            {status.map((item, index) => (
              <div key={index} className="font-mono text-sm">
                {item}
              </div>
            ))}
          </div>
        </div>

        <div className="bg-white rounded-lg shadow-lg p-6 mb-6">
          <h2 className="text-xl font-semibold mb-4">功能测试</h2>
          <div className="space-x-4">
            <button
              onClick={testNavigation}
              className="bg-blue-500 text-white px-4 py-2 rounded hover:bg-blue-600"
            >
              测试导航
            </button>
            <button
              onClick={() => window.location.reload()}
              className="bg-green-500 text-white px-4 py-2 rounded hover:bg-green-600"
            >
              刷新页面
            </button>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow-lg p-6">
          <h2 className="text-xl font-semibold mb-4">环境信息</h2>
          <div className="grid grid-cols-2 gap-4 text-sm">
            <div>
              <strong>用户代理:</strong>
              <div className="font-mono text-xs break-all">
                {navigator.userAgent}
              </div>
            </div>
            <div>
              <strong>页面URL:</strong>
              <div className="font-mono text-xs break-all">
                {window.location.href}
              </div>
            </div>
            <div>
              <strong>本地存储支持:</strong>
              <div className="font-mono text-xs">
                {typeof Storage !== "undefined" ? "✅ 支持" : "❌ 不支持"}
              </div>
            </div>
            <div>
              <strong>控制台错误:</strong>
              <div className="font-mono text-xs">
                检查浏览器开发者工具控制台
              </div>
            </div>
          </div>
        </div>

        <div className="mt-6 text-center">
          <p className="text-gray-600">
            如果此页面正常显示，说明基础 React 应用运行正常。
            <br />
            如果主应用不工作，请检查浏览器控制台是否有JavaScript错误。
          </p>
        </div>
      </div>
    </div>
  );
}
