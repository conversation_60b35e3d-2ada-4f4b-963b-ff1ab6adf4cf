import React, { useState } from "react";
import { <PERSON>, Card<PERSON><PERSON>nt, Card<PERSON><PERSON>er, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Textarea } from "@/components/ui/textarea";
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog";
import { Badge } from "@/components/ui/badge";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { 
  Package, 
  Plus, 
  MapPin, 
  Tag, 
  Calendar,
  CheckCircle,
  Clock,
  AlertCircle,
  Warehouse,
  Truck
} from "lucide-react";

// 模拟待入库产品数据
const mockPendingProducts = [
  {
    id: "SP004",
    productCode: "SP-2024-004",
    productType: "精密仪器",
          area: "B区",
    shelfNumber: "E-01",
    positionNumber: "E-01-02",
    registrationTime: "2024-01-18 10:15:00",
    rfidTag: "RFID-E001-002",
    description: "高精度测量仪器",
    status: "待入库"
  },
  {
    id: "SP005",
    productCode: "SP-2024-005",
    productType: "光学设备",
          area: "C区",
    shelfNumber: "F-03",
    positionNumber: "F-03-01",
    registrationTime: "2024-01-18 14:30:00",
    rfidTag: "RFID-F003-001",
    description: "光学检测设备",
    status: "待入库"
  }
];

export default function SpecialProductInbound() {
  const [pendingProducts, setPendingProducts] = useState(mockPendingProducts);
  const [isRegistrationDialogOpen, setIsRegistrationDialogOpen] = useState(false);
  const [newProduct, setNewProduct] = useState({
    productCode: "",
    productType: "",
    area: "",
    shelfNumber: "",
    positionNumber: "",
    description: "",
    rfidTag: ""
  });

  // 生成RFID标签
  const generateRfidTag = (area: string, shelf: string, position: string) => {
    return `RFID-${area.replace('区', '')}${shelf.split('-')[1]}-${position.split('-')[2]}`;
  };

  // 处理产品登记
  const handleProductRegistration = () => {
    if (!newProduct.productCode || !newProduct.productType || !newProduct.area || 
        !newProduct.shelfNumber || !newProduct.positionNumber) {
      alert("请填写所有必填字段");
      return;
    }

    const rfidTag = generateRfidTag(newProduct.area, newProduct.shelfNumber, newProduct.positionNumber);
    const product = {
      id: `SP${Date.now()}`,
      productCode: newProduct.productCode,
      productType: newProduct.productType,
      area: newProduct.area,
      shelfNumber: newProduct.shelfNumber,
      positionNumber: newProduct.positionNumber,
      description: newProduct.description,
      rfidTag: rfidTag,
      registrationTime: new Date().toLocaleString(),
      status: "待入库"
    };

    setPendingProducts([...pendingProducts, product]);
    setNewProduct({
      productCode: "",
      productType: "",
      area: "",
      shelfNumber: "",
      positionNumber: "",
      description: "",
      rfidTag: ""
    });
    setIsRegistrationDialogOpen(false);
  };

  // 处理入库操作
  const handleInboundOperation = (productId: string) => {
    setPendingProducts(pendingProducts.map(product => 
      product.id === productId 
        ? { ...product, status: "入库中" }
        : product
    ));

    // 模拟入库过程
    setTimeout(() => {
      setPendingProducts(pendingProducts.map(product => 
        product.id === productId 
          ? { ...product, status: "正常" }
          : product
      ));
    }, 3000);
  };

  const getStatusBadge = (status: string) => {
    switch (status) {
      case "待入库":
        return <Badge className="bg-yellow-100 text-yellow-800 border-yellow-200">待入库</Badge>;
      case "入库中":
        return <Badge className="bg-blue-100 text-blue-800 border-blue-200">入库中</Badge>;
      case "正常":
        return <Badge className="bg-green-100 text-green-800 border-green-200">正常</Badge>;
      default:
        return <Badge variant="outline">{status}</Badge>;
    }
  };

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h2 className="text-3xl font-bold tracking-tight text-slate-800">特殊产品入库登记</h2>
        <Dialog open={isRegistrationDialogOpen} onOpenChange={setIsRegistrationDialogOpen}>
          <DialogTrigger asChild>
            <Button className="flex items-center gap-2">
              <Plus className="h-4 w-4" />
              产品登记
            </Button>
          </DialogTrigger>
          <DialogContent className="max-w-2xl">
            <DialogHeader>
              <DialogTitle>产品信息录入</DialogTitle>
            </DialogHeader>
            <div className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="productCode">产品编号 *</Label>
                  <Input
                    id="productCode"
                    value={newProduct.productCode}
                    onChange={(e) => setNewProduct({...newProduct, productCode: e.target.value})}
                    placeholder="请输入产品编号"
                  />
                </div>
                <div>
                  <Label htmlFor="productType">产品类型 *</Label>
                  <Select value={newProduct.productType} onValueChange={(value) => setNewProduct({...newProduct, productType: value})}>
                    <SelectTrigger>
                      <SelectValue placeholder="选择产品类型" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="精密仪器">精密仪器</SelectItem>
                      <SelectItem value="光学设备">光学设备</SelectItem>
                      <SelectItem value="电子元件">电子元件</SelectItem>
                      <SelectItem value="机械零件">机械零件</SelectItem>
                      <SelectItem value="其他">其他</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>

              <div className="grid grid-cols-3 gap-4">
                <div>
                  <Label htmlFor="area">存储区域 *</Label>
                  <Select value={newProduct.area} onValueChange={(value) => setNewProduct({...newProduct, area: value})}>
                    <SelectTrigger>
                      <SelectValue placeholder="选择区域" />
                    </SelectTrigger>
                    <SelectContent>
                                             <SelectItem value="B区">B区</SelectItem>
                       <SelectItem value="C区">C区</SelectItem>
                      <SelectItem value="G区">G区</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <div>
                  <Label htmlFor="shelfNumber">货架号 *</Label>
                  <Select value={newProduct.shelfNumber} onValueChange={(value) => setNewProduct({...newProduct, shelfNumber: value})}>
                    <SelectTrigger>
                      <SelectValue placeholder="选择货架" />
                    </SelectTrigger>
                    <SelectContent>
                      {newProduct.area === "B区" && (
                        <>
                          <SelectItem value="B-01">B-01</SelectItem>
                          <SelectItem value="B-02">B-02</SelectItem>
                          <SelectItem value="B-03">B-03</SelectItem>
                          <SelectItem value="B-04">B-04</SelectItem>
                          <SelectItem value="B-05">B-05</SelectItem>
                        </>
                      )}
                      {newProduct.area === "C区" && (
                        <>
                          <SelectItem value="C-01">C-01</SelectItem>
                          <SelectItem value="C-02">C-02</SelectItem>
                          <SelectItem value="C-03">C-03</SelectItem>
                          <SelectItem value="C-04">C-04</SelectItem>
                          <SelectItem value="C-05">C-05</SelectItem>
                        </>
                      )}
                      {newProduct.area === "G区" && (
                        <>
                          <SelectItem value="G-01">G-01</SelectItem>
                          <SelectItem value="G-02">G-02</SelectItem>
                          <SelectItem value="G-03">G-03</SelectItem>
                        </>
                      )}
                    </SelectContent>
                  </Select>
                </div>
                <div>
                  <Label htmlFor="positionNumber">货位号 *</Label>
                  <Select value={newProduct.positionNumber} onValueChange={(value) => setNewProduct({...newProduct, positionNumber: value})}>
                    <SelectTrigger>
                      <SelectValue placeholder="选择货位" />
                    </SelectTrigger>
                    <SelectContent>
                      {newProduct.shelfNumber && (
                        <>
                          <SelectItem value={`${newProduct.shelfNumber}-01`}>{newProduct.shelfNumber}-01</SelectItem>
                          <SelectItem value={`${newProduct.shelfNumber}-02`}>{newProduct.shelfNumber}-02</SelectItem>
                          <SelectItem value={`${newProduct.shelfNumber}-03`}>{newProduct.shelfNumber}-03</SelectItem>
                          <SelectItem value={`${newProduct.shelfNumber}-04`}>{newProduct.shelfNumber}-04</SelectItem>
                        </>
                      )}
                    </SelectContent>
                  </Select>
                </div>
              </div>

              <div>
                <Label htmlFor="description">产品描述</Label>
                <Textarea
                  id="description"
                  value={newProduct.description}
                  onChange={(e) => setNewProduct({...newProduct, description: e.target.value})}
                  placeholder="请输入产品描述"
                  rows={3}
                />
              </div>

              <div className="flex justify-end space-x-2">
                <Button variant="outline" onClick={() => setIsRegistrationDialogOpen(false)}>
                  取消
                </Button>
                <Button onClick={handleProductRegistration}>
                  确认创建
                </Button>
              </div>
            </div>
          </DialogContent>
        </Dialog>
      </div>

      {/* 统计信息 */}
      <div className="grid gap-4 md:grid-cols-4">
        <Card className="blue-tech-card">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium text-slate-700">待入库产品</CardTitle>
            <Clock className="h-4 w-4 text-yellow-500" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-slate-900">
              {pendingProducts.filter(p => p.status === "待入库").length}
            </div>
            <p className="text-xs text-slate-500">等待入库操作</p>
          </CardContent>
        </Card>

        <Card className="blue-tech-card">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium text-slate-700">入库中</CardTitle>
            <Truck className="h-4 w-4 text-blue-500" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-slate-900">
              {pendingProducts.filter(p => p.status === "入库中").length}
            </div>
            <p className="text-xs text-slate-500">正在入库</p>
          </CardContent>
        </Card>

        <Card className="blue-tech-card">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium text-slate-700">今日入库</CardTitle>
            <CheckCircle className="h-4 w-4 text-green-500" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-slate-900">5</div>
            <p className="text-xs text-slate-500">已完成入库</p>
          </CardContent>
        </Card>

        <Card className="blue-tech-card">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium text-slate-700">可用货位</CardTitle>
            <Warehouse className="h-4 w-4 text-purple-500" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-slate-900">24</div>
            <p className="text-xs text-slate-500">E/F/G区可用</p>
          </CardContent>
        </Card>
      </div>

      {/* 待入库产品列表 */}
      <Card className="blue-tech-card">
        <CardHeader>
          <CardTitle className="text-slate-800">待入库产品列表</CardTitle>
        </CardHeader>
        <CardContent>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>产品编号</TableHead>
                <TableHead>产品类型</TableHead>
                <TableHead>RFID标签</TableHead>
                <TableHead>目标位置</TableHead>
                <TableHead>状态</TableHead>
                <TableHead>登记时间</TableHead>
                <TableHead>操作</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {pendingProducts.map((product) => (
                <TableRow key={product.id}>
                  <TableCell className="font-medium">{product.productCode}</TableCell>
                  <TableCell>{product.productType}</TableCell>
                  <TableCell className="font-mono text-sm">{product.rfidTag}</TableCell>
                  <TableCell>
                    <div className="flex items-center gap-1">
                      <MapPin className="h-3 w-3 text-slate-500" />
                      <span>{product.area} {product.shelfNumber} {product.positionNumber}</span>
                    </div>
                  </TableCell>
                  <TableCell>{getStatusBadge(product.status)}</TableCell>
                  <TableCell>
                    <div className="flex items-center gap-1">
                      <Calendar className="h-3 w-3 text-slate-500" />
                      <span className="text-sm">{product.registrationTime}</span>
                    </div>
                  </TableCell>
                  <TableCell>
                    {product.status === "待入库" && (
                      <Button 
                        size="sm" 
                        className="bg-blue-600 hover:bg-blue-700"
                        onClick={() => handleInboundOperation(product.id)}
                      >
                        入库操作
                      </Button>
                    )}
                    {product.status === "入库中" && (
                      <div className="flex items-center gap-2">
                        <AlertCircle className="h-4 w-4 text-blue-500 animate-pulse" />
                        <span className="text-sm text-blue-600">入库中...</span>
                      </div>
                    )}
                    {product.status === "正常" && (
                      <div className="flex items-center gap-2">
                        <CheckCircle className="h-4 w-4 text-green-500" />
                        <span className="text-sm text-green-600">入库完成</span>
                      </div>
                    )}
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </CardContent>
      </Card>
    </div>
  );
} 