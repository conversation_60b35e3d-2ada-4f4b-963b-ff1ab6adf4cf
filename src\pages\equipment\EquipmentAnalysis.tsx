import { useState, useEffect } from "react";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Progress } from "@/components/ui/progress";
import { Tabs, <PERSON>bs<PERSON>ontent, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { StatusChart } from "@/components/StatusChart";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  TrendingUp,
  TrendingDown,
  BarChart3,
  Activity,
  Clock,
  AlertTriangle,
  CheckCircle,
  Zap,
  Thermometer,
  Settings,
  Bot,
  Truck,
  TestTube2,
  Calendar,
  Download,
  RefreshCw,
  Target,
} from "lucide-react";

interface PerformanceMetric {
  equipmentId: string;
  equipmentName: string;
  equipmentType: "truss" | "amr" | "test";
  uptime: number;
  efficiency: number;
  utilization: number;
  mtbf: number; // Mean Time Between Failures
  mttr: number; // Mean Time To Repair
  powerConsumption: number;
  throughput: number;
  lastMaintenanceDate: string;
  nextMaintenanceDate: string;
  status: "excellent" | "good" | "average" | "poor";
}

interface TrendData {
  date: string;
  trussEfficiency: number;
  amrUtilization: number;
  testThroughput: number;
  overallOEE: number;
}

interface AlarmData {
  id: string;
  equipmentId: string;
  equipmentName: string;
  alarmType: "warning" | "error" | "critical";
  message: string;
  timestamp: string;
  status: "active" | "acknowledged" | "resolved";
}

export default function EquipmentAnalysis() {
  const [selectedTimeRange, setSelectedTimeRange] = useState("7d");
  const [selectedEquipmentType, setSelectedEquipmentType] = useState("all");
  const [isRefreshing, setIsRefreshing] = useState(false);

  // 性能指标数据
  const [performanceMetrics] = useState<PerformanceMetric[]>([
    {
      equipmentId: "TRUSS-01",
      equipmentName: "桁架系统-01",
      equipmentType: "truss",
      uptime: 97.5,
      efficiency: 89.3,
      utilization: 76.8,
      mtbf: 168, // hours
      mttr: 2.3, // hours
      powerConsumption: 45.2, // kW
      throughput: 156, // operations/day
      lastMaintenanceDate: "2024-01-05",
      nextMaintenanceDate: "2024-04-05",
      status: "excellent",
    },
    {
          equipmentId: "AMR-02",
    equipmentName: "AMR运输车-02",
    equipmentType: "amr",
      uptime: 88.2,
      efficiency: 82.4,
      utilization: 71.2,
      mtbf: 187,
      mttr: 4.2,
      powerConsumption: 13.7,
      throughput: 76,
      lastMaintenanceDate: "2023-12-28",
      nextMaintenanceDate: "2024-01-28",
      status: "average",
    },
    {
      equipmentId: "TEST-01",
      equipmentName: "测试台-01",
      equipmentType: "test",
      uptime: 96.3,
      efficiency: 88.9,
      utilization: 85.4,
      mtbf: 234,
      mttr: 2.7,
      powerConsumption: 28.4,
      throughput: 24,
      lastMaintenanceDate: "2024-01-08",
      nextMaintenanceDate: "2024-04-08",
      status: "excellent",
    },
    {
      equipmentId: "TEST-03",
      equipmentName: "测试台-03",
      equipmentType: "test",
      uptime: 91.7,
      efficiency: 84.1,
      utilization: 79.6,
      mtbf: 198,
      mttr: 3.5,
      powerConsumption: 31.2,
      throughput: 21,
      lastMaintenanceDate: "2023-11-15",
      nextMaintenanceDate: "2024-02-15",
      status: "good",
    },
  ]);

  // 趋势数据
  const [trendData] = useState<TrendData[]>([
    {
      date: "2024-01-01",
      trussEfficiency: 87.2,
      amrUtilization: 72.1,
      testThroughput: 22.3,
      overallOEE: 83.5,
    },
    {
      date: "2024-01-02",
      trussEfficiency: 89.1,
      amrUtilization: 74.8,
      testThroughput: 23.1,
      overallOEE: 85.2,
    },
    {
      date: "2024-01-03",
      trussEfficiency: 86.4,
      amrUtilization: 71.2,
      testThroughput: 21.8,
      overallOEE: 82.7,
    },
    {
      date: "2024-01-04",
      trussEfficiency: 90.3,
      amrUtilization: 76.5,
      testThroughput: 24.2,
      overallOEE: 86.8,
    },
    {
      date: "2024-01-05",
      trussEfficiency: 88.7,
      amrUtilization: 73.9,
      testThroughput: 23.5,
      overallOEE: 84.9,
    },
    {
      date: "2024-01-06",
      trussEfficiency: 91.5,
      amrUtilization: 78.2,
      testThroughput: 25.1,
      overallOEE: 87.6,
    },
    {
      date: "2024-01-07",
      trussEfficiency: 89.8,
      amrUtilization: 75.4,
      testThroughput: 24.8,
      overallOEE: 86.3,
    },
  ]);

  // 告警数据
  const [alarmData] = useState<AlarmData[]>([
    {
      id: "ALM-001",
      equipmentId: "TRUSS-01",
      equipmentName: "桁架系统-01",
      alarmType: "warning",
      message: "润滑油压力低于标准值",
      timestamp: "2024-01-10 14:25:33",
      status: "active",
    },
    {
      id: "ALM-002",
          equipmentId: "AMR-02",
    equipmentName: "AMR运输车-02",
      alarmType: "error",
      message: "电池电量低，需要充电",
      timestamp: "2024-01-10 13:45:12",
      status: "acknowledged",
    },
    {
      id: "ALM-003",
      equipmentId: "TEST-03",
      equipmentName: "测试台-03",
      alarmType: "critical",
      message: "温度传感器异常，测试暂停",
      timestamp: "2024-01-10 12:18:45",
      status: "resolved",
    },
    {
      id: "ALM-004",
          equipmentId: "AMR-04",
    equipmentName: "AMR运输车-04",
      alarmType: "warning",
      message: "导航精度偏差超出阈值",
      timestamp: "2024-01-10 11:32:21",
      status: "active",
    },
  ]);

  const handleRefresh = async () => {
    setIsRefreshing(true);
    // Simulate data refresh
    await new Promise((resolve) => setTimeout(resolve, 2000));
    setIsRefreshing(false);
  };

  const filteredMetrics = performanceMetrics.filter(
    (metric) =>
      selectedEquipmentType === "all" ||
      metric.equipmentType === selectedEquipmentType,
  );

  const getStatusBadge = (status: string) => {
    const statusConfig = {
      excellent: { label: "优秀", className: "bg-green-100 text-green-700 border-green-200" },
      good: { label: "良好", className: "bg-blue-100 text-blue-700 border-blue-200" },
      average: { label: "一般", className: "bg-yellow-100 text-yellow-700 border-yellow-200" },
      poor: { label: "较差", className: "bg-red-100 text-red-700 border-red-200" },
    };

    const config = statusConfig[status as keyof typeof statusConfig];
    return (
      <Badge className={`${config.className} border`}>{config.label}</Badge>
    );
  };

  const getAlarmBadge = (type: string) => {
    const alarmConfig = {
      warning: { label: "警告", className: "bg-yellow-100 text-yellow-700 border-yellow-200" },
      error: { label: "错误", className: "bg-orange-100 text-orange-700 border-orange-200" },
      critical: { label: "严重", className: "bg-red-100 text-red-700 border-red-200" },
    };

    const config = alarmConfig[type as keyof typeof alarmConfig];
    return (
      <Badge className={`${config.className} border`}>{config.label}</Badge>
    );
  };

  const getEquipmentIcon = (type: string) => {
    switch (type) {
      case "truss":
        return <Bot className="h-4 w-4 text-purple-400" />;
      case "amr":
        return <Truck className="h-4 w-4 text-blue-400" />;
      case "test":
        return <TestTube2 className="h-4 w-4 text-green-400" />;
      default:
        return <Settings className="h-4 w-4 text-gray-400" />;
    }
  };

  // 计算综合指标
  const avgUptime =
    filteredMetrics.reduce((sum, m) => sum + m.uptime, 0) /
    filteredMetrics.length;
  const avgEfficiency =
    filteredMetrics.reduce((sum, m) => sum + m.efficiency, 0) /
    filteredMetrics.length;
  const totalPowerConsumption = filteredMetrics.reduce(
    (sum, m) => sum + m.powerConsumption,
    0,
  );
  const activeAlarms = alarmData.filter((a) => a.status === "active").length;

  return (
    <div className="min-h-screen bg-white text-gray-900 p-6">
      <div className="space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div>
            <h2 className="text-3xl font-bold tracking-tight bg-gradient-to-r from-cyan-200 to-blue-200 bg-clip-text text-transparent">
              设备数据分析
            </h2>
            <p className="text-gray-500 mt-2">设备性能监控与数据分析平台</p>
          </div>
          <div className="flex items-center space-x-3">
            <Select
              value={selectedTimeRange}
              onValueChange={setSelectedTimeRange}
            >
              <SelectTrigger className="bg-white border-gray-300 text-gray-900 w-32">
                <SelectValue />
              </SelectTrigger>
              <SelectContent className="bg-white border-gray-200">
                <SelectItem value="1d">今日</SelectItem>
                <SelectItem value="7d">7天</SelectItem>
                <SelectItem value="30d">30天</SelectItem>
                <SelectItem value="90d">90天</SelectItem>
              </SelectContent>
            </Select>

            <Button
              variant="outline"
              size="sm"
              className="border-gray-300 bg-white text-gray-700 hover:bg-gray-100"
              onClick={handleRefresh}
              disabled={isRefreshing}
            >
              {isRefreshing ? (
                <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
              ) : (
                <RefreshCw className="h-4 w-4 mr-2" />
              )}
              刷新数据
            </Button>

            <Button
              variant="outline"
              size="sm"
              className="border-gray-300 bg-white text-gray-700 hover:bg-gray-100"
            >
              <Download className="h-4 w-4 mr-2" />
              导出报告
            </Button>
          </div>
        </div>

        {/* Key Performance Indicators */}
        <div className="grid gap-6 md:grid-cols-4">
          <Card className="bg-white border border-gray-200 shadow-sm">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium text-gray-700">
                平均运行时间
              </CardTitle>
              <Clock className="h-4 w-4 text-green-400" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-green-700">
                {avgUptime.toFixed(1)}%
              </div>
              <div className="flex items-center text-xs text-gray-500 mt-1">
                <TrendingUp className="h-3 w-3 mr-1" />
                较上周 +2.3%
              </div>
            </CardContent>
          </Card>

          <Card className="bg-white border border-gray-200 shadow-sm">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium text-gray-700">
                综合效率
              </CardTitle>
              <Target className="h-4 w-4 text-blue-400" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-blue-700">
                {avgEfficiency.toFixed(1)}%
              </div>
              <div className="flex items-center text-xs text-gray-500 mt-1">
                <TrendingUp className="h-3 w-3 mr-1" />
                较上周 +1.8%
              </div>
            </CardContent>
          </Card>

          <Card className="bg-white border border-gray-200 shadow-sm">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium text-gray-700">
                能耗总计
              </CardTitle>
              <Zap className="h-4 w-4 text-yellow-400" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-yellow-700">
                {totalPowerConsumption.toFixed(1)}kW
              </div>
              <div className="flex items-center text-xs text-gray-500 mt-1">
                <TrendingDown className="h-3 w-3 mr-1" />
                较上周 -3.2%
              </div>
            </CardContent>
          </Card>

          <Card className="bg-white border border-gray-200 shadow-sm">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium text-gray-700">
                活跃告警
              </CardTitle>
              <AlertTriangle className="h-4 w-4 text-red-400" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-red-700">
                {activeAlarms}
              </div>
              <div className="text-xs text-gray-500 mt-1">需要处理</div>
            </CardContent>
          </Card>
        </div>

        {/* Main Content Tabs */}
        <Tabs defaultValue="overview" className="space-y-4">
          <TabsList className="bg-gray-800/50 border-gray-600">
            <TabsTrigger
              value="overview"
              className="data-[state=active]:bg-gray-700 text-white"
            >
              综合概览
            </TabsTrigger>
            <TabsTrigger
              value="performance"
              className="data-[state=active]:bg-gray-700 text-white"
            >
              性能分析
            </TabsTrigger>
            <TabsTrigger
              value="trends"
              className="data-[state=active]:bg-gray-700 text-white"
            >
              趋势分析
            </TabsTrigger>
            <TabsTrigger
              value="alarms"
              className="data-[state=active]:bg-gray-700 text-white"
            >
              告警监控
            </TabsTrigger>
          </TabsList>

          {/* Overview Tab */}
          <TabsContent value="overview" className="space-y-4">
            <div className="grid gap-6 md:grid-cols-2">
              <Card className="bg-white border border-gray-200 shadow-sm">
                <CardHeader>
                  <CardTitle className="text-gray-200">设备类型分布</CardTitle>
                  <CardDescription className="text-gray-500">
                    各类型设备数量统计
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="h-80">
                    <StatusChart
                      type="pie"
                      data={[
                        { name: "桁架系统", value: 2 },
                        { name: "AMR车队", value: 4 },
                        { name: "测试系统", value: 7 },
                      ]}
                      dataKey="value"
                      nameKey="name"
                      colors={["#8b5cf6", "#3b82f6", "#10b981"]}
                      pieRadius={120}
                    />
                  </div>
                </CardContent>
              </Card>

              <Card className="bg-white border border-gray-200 shadow-sm">
                <CardHeader>
                  <CardTitle className="text-gray-200">运行状态分布</CardTitle>
                  <CardDescription className="text-gray-500">
                    当前设备运行状态统计
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="h-80">
                    <StatusChart
                      type="pie"
                      data={[
                        { name: "正常运行", value: 10 },
                        { name: "维护中", value: 2 },
                        { name: "故障", value: 1 },
                      ]}
                      dataKey="value"
                      nameKey="name"
                      colors={["#10b981", "#f59e0b", "#ef4444"]}
                      pieRadius={120}
                    />
                  </div>
                </CardContent>
              </Card>
            </div>

            {/* Real-time Status */}
            <Card className="bg-white border border-gray-200 shadow-sm">
              <CardHeader>
                <CardTitle className="text-gray-200">实时运行状态</CardTitle>
                <CardDescription className="text-gray-500">
                  设备关键指标实时监控
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="grid gap-4 md:grid-cols-3">
                  {performanceMetrics.slice(0, 6).map((metric) => (
                    <div
                      key={metric.equipmentId}
                      className="p-4 bg-gray-700/30 rounded-lg border border-gray-600/50"
                    >
                      <div className="flex items-center justify-between mb-3">
                        <div className="flex items-center space-x-2">
                          {getEquipmentIcon(metric.equipmentType)}
                          <span className="font-medium text-gray-200">
                            {metric.equipmentName}
                          </span>
                        </div>
                        {getStatusBadge(metric.status)}
                      </div>

                      <div className="space-y-2">
                        <div>
                          <div className="flex justify-between text-sm mb-1">
                            <span className="text-gray-400">运行时间</span>
                            <span className="text-gray-300">
                              {metric.uptime}%
                            </span>
                          </div>
                          <Progress value={metric.uptime} className="h-1" />
                        </div>

                        <div>
                          <div className="flex justify-between text-sm mb-1">
                            <span className="text-gray-400">效率</span>
                            <span className="text-gray-300">
                              {metric.efficiency}%
                            </span>
                          </div>
                          <Progress value={metric.efficiency} className="h-1" />
                        </div>

                        <div>
                          <div className="flex justify-between text-sm mb-1">
                            <span className="text-gray-400">利用率</span>
                            <span className="text-gray-300">
                              {metric.utilization}%
                            </span>
                          </div>
                          <Progress
                            value={metric.utilization}
                            className="h-1"
                          />
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          {/* Performance Tab */}
          <TabsContent value="performance" className="space-y-4">
            {/* Filters */}
            <Card className="bg-white border border-gray-200 shadow-sm">
              <CardHeader>
                <CardTitle className="text-gray-200">筛选条件</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="flex items-center space-x-4">
                  <div className="space-y-2">
                    <label className="text-sm font-medium text-gray-300">
                      设备类型
                    </label>
                    <Select
                      value={selectedEquipmentType}
                      onValueChange={setSelectedEquipmentType}
                    >
                      <SelectTrigger className="bg-white border-gray-300 text-gray-900 w-40">
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent className="bg-white border-gray-200">
                        <SelectItem value="all">全部设备</SelectItem>
                        <SelectItem value="truss">桁架系统</SelectItem>
                        <SelectItem value="amr">AMR车队</SelectItem>
                        <SelectItem value="test">测试系统</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Performance Table */}
            <Card className="bg-white border border-gray-200 shadow-sm">
              <CardHeader>
                <CardTitle className="text-gray-200">设备性能详情</CardTitle>
                <CardDescription className="text-gray-500">
                  显示 {filteredMetrics.length} 台设备性能数据
                </CardDescription>
              </CardHeader>
              <CardContent>
                <Table>
                  <TableHeader>
                    <TableRow className="border-gray-600">
                      <TableHead className="text-gray-300">设备信息</TableHead>
                      <TableHead className="text-gray-300">运行时间</TableHead>
                      <TableHead className="text-gray-300">效率</TableHead>
                      <TableHead className="text-gray-300">利用率</TableHead>
                      <TableHead className="text-gray-300">MTBF</TableHead>
                      <TableHead className="text-gray-300">MTTR</TableHead>
                      <TableHead className="text-gray-300">功耗</TableHead>
                      <TableHead className="text-gray-300">产能</TableHead>
                      <TableHead className="text-gray-300">状态</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {filteredMetrics.map((metric) => (
                      <TableRow
                        key={metric.equipmentId}
                        className="border-gray-600 hover:bg-gray-800/50"
                      >
                        <TableCell>
                          <div className="flex items-center space-x-2">
                            {getEquipmentIcon(metric.equipmentType)}
                            <div>
                              <div className="font-medium text-gray-200">
                                {metric.equipmentName}
                              </div>
                              <div className="text-xs text-gray-400">
                                {metric.equipmentId}
                              </div>
                            </div>
                          </div>
                        </TableCell>
                        <TableCell>
                          <div className="flex items-center space-x-2">
                            <div className="w-12 h-2 bg-gray-700 rounded-full overflow-hidden">
                              <div
                                className="h-full bg-green-500 transition-all duration-300"
                                style={{ width: `${metric.uptime}%` }}
                              />
                            </div>
                            <span className="text-gray-300 text-sm">
                              {metric.uptime}%
                            </span>
                          </div>
                        </TableCell>
                        <TableCell>
                          <div className="flex items-center space-x-2">
                            <div className="w-12 h-2 bg-gray-700 rounded-full overflow-hidden">
                              <div
                                className="h-full bg-blue-500 transition-all duration-300"
                                style={{ width: `${metric.efficiency}%` }}
                              />
                            </div>
                            <span className="text-gray-300 text-sm">
                              {metric.efficiency}%
                            </span>
                          </div>
                        </TableCell>
                        <TableCell>
                          <div className="flex items-center space-x-2">
                            <div className="w-12 h-2 bg-gray-700 rounded-full overflow-hidden">
                              <div
                                className="h-full bg-purple-500 transition-all duration-300"
                                style={{ width: `${metric.utilization}%` }}
                              />
                            </div>
                            <span className="text-gray-300 text-sm">
                              {metric.utilization}%
                            </span>
                          </div>
                        </TableCell>
                        <TableCell className="text-gray-300">
                          {metric.mtbf}h
                        </TableCell>
                        <TableCell className="text-gray-300">
                          {metric.mttr}h
                        </TableCell>
                        <TableCell className="text-gray-300">
                          {metric.powerConsumption}kW
                        </TableCell>
                        <TableCell className="text-gray-300">
                          {metric.throughput}
                          {metric.equipmentType === "test" ? "/天" : "/天"}
                        </TableCell>
                        <TableCell>{getStatusBadge(metric.status)}</TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </CardContent>
            </Card>
          </TabsContent>

          {/* Trends Tab */}
          <TabsContent value="trends" className="space-y-4">
            <div className="grid gap-6 md:grid-cols-2">
              <Card className="bg-white border border-gray-200 shadow-sm">
                <CardHeader>
                  <CardTitle className="text-gray-200">效率趋势</CardTitle>
                  <CardDescription className="text-gray-500">
                    过去7天各系统效率变化
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="h-80">
                    <StatusChart
                      type="line"
                      data={trendData}
                      dataKey="trussEfficiency"
                      nameKey="date"
                      colors={["#8b5cf6", "#3b82f6", "#10b981", "#f59e0b"]}
                    />
                  </div>
                </CardContent>
              </Card>

              <Card className="bg-white border border-gray-200 shadow-sm">
                <CardHeader>
                  <CardTitle className="text-gray-200">综合OEE指标</CardTitle>
                  <CardDescription className="text-gray-500">
                    整体设备效率趋势分析
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="h-80">
                    <StatusChart
                      type="bar"
                      data={trendData}
                      dataKey="overallOEE"
                      nameKey="date"
                      colors={["#22d3ee"]}
                      barWidth={40}
                    />
                  </div>
                </CardContent>
              </Card>
            </div>

            {/* Performance Comparison */}
            <Card className="bg-white border border-gray-200 shadow-sm">
              <CardHeader>
                <CardTitle className="text-gray-200">性能对比分析</CardTitle>
                <CardDescription className="text-gray-500">
                  各系统关键指标对比
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="grid gap-6 md:grid-cols-3">
                  <div className="text-center p-4 bg-gray-700/30 rounded-lg">
                    <div className="text-2xl font-bold text-purple-300 mb-2">
                      {performanceMetrics
                        .filter((m) => m.equipmentType === "truss")
                        .reduce((sum, m) => sum + m.efficiency, 0) /
                        performanceMetrics.filter(
                          (m) => m.equipmentType === "truss",
                        ).length}
                      %
                    </div>
                    <div className="text-sm text-gray-400">
                      桁架系统平均效率
                    </div>
                  </div>

                  <div className="text-center p-4 bg-gray-700/30 rounded-lg">
                    <div className="text-2xl font-bold text-blue-300 mb-2">
                      {performanceMetrics
                        .filter((m) => m.equipmentType === "amr")
                        .reduce((sum, m) => sum + m.efficiency, 0) /
                        performanceMetrics.filter(
                          (m) => m.equipmentType === "amr",
                        ).length}
                      %
                    </div>
                    <div className="text-sm text-gray-400">
                      AMR系统平均效率
                    </div>
                  </div>

                  <div className="text-center p-4 bg-gray-700/30 rounded-lg">
                    <div className="text-2xl font-bold text-green-300 mb-2">
                      {performanceMetrics
                        .filter((m) => m.equipmentType === "test")
                        .reduce((sum, m) => sum + m.efficiency, 0) /
                        performanceMetrics.filter(
                          (m) => m.equipmentType === "test",
                        ).length}
                      %
                    </div>
                    <div className="text-sm text-gray-400">
                      测试系统平均效率
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          {/* Alarms Tab */}
          <TabsContent value="alarms" className="space-y-4">
            <Card className="bg-white border border-gray-200 shadow-sm">
              <CardHeader>
                <CardTitle className="text-gray-200">告警统计</CardTitle>
                <CardDescription className="text-gray-500">
                  当前系统告警情况概览
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="grid gap-4 md:grid-cols-4">
                  <div className="text-center p-4 bg-gray-700/30 rounded-lg">
                    <div className="text-2xl font-bold text-red-300">
                      {
                        alarmData.filter((a) => a.alarmType === "critical")
                          .length
                      }
                    </div>
                    <div className="text-sm text-gray-400">严重告警</div>
                  </div>
                  <div className="text-center p-4 bg-gray-700/30 rounded-lg">
                    <div className="text-2xl font-bold text-orange-300">
                      {alarmData.filter((a) => a.alarmType === "error").length}
                    </div>
                    <div className="text-sm text-gray-400">错误告警</div>
                  </div>
                  <div className="text-center p-4 bg-gray-700/30 rounded-lg">
                    <div className="text-2xl font-bold text-yellow-300">
                      {
                        alarmData.filter((a) => a.alarmType === "warning")
                          .length
                      }
                    </div>
                    <div className="text-sm text-gray-400">警告告警</div>
                  </div>
                  <div className="text-center p-4 bg-gray-700/30 rounded-lg">
                    <div className="text-2xl font-bold text-green-300">
                      {alarmData.filter((a) => a.status === "resolved").length}
                    </div>
                    <div className="text-sm text-gray-400">已解决</div>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Alarms List */}
            <Card className="bg-white border border-gray-200 shadow-sm">
              <CardHeader>
                <CardTitle className="text-gray-200">告警列表</CardTitle>
                <CardDescription className="text-gray-500">
                  系统实时告警信息
                </CardDescription>
              </CardHeader>
              <CardContent>
                <Table>
                  <TableHeader>
                    <TableRow className="border-gray-600">
                      <TableHead className="text-gray-300">设备信息</TableHead>
                      <TableHead className="text-gray-300">告警类型</TableHead>
                      <TableHead className="text-gray-300">告警信息</TableHead>
                      <TableHead className="text-gray-300">发生时间</TableHead>
                      <TableHead className="text-gray-300">状态</TableHead>
                      <TableHead className="text-gray-300">操作</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {alarmData.map((alarm) => (
                      <TableRow
                        key={alarm.id}
                        className="border-gray-600 hover:bg-gray-800/50"
                      >
                        <TableCell>
                          <div>
                            <div className="font-medium text-gray-200">
                              {alarm.equipmentName}
                            </div>
                            <div className="text-xs text-gray-400">
                              {alarm.equipmentId}
                            </div>
                          </div>
                        </TableCell>
                        <TableCell>{getAlarmBadge(alarm.alarmType)}</TableCell>
                        <TableCell className="text-gray-300 max-w-xs">
                          {alarm.message}
                        </TableCell>
                        <TableCell className="text-gray-300">
                          {alarm.timestamp}
                        </TableCell>
                        <TableCell>
                          <Badge
                            className={
                              alarm.status === "active"
                                ? "bg-red-100 text-red-700 border-red-200"
                                : alarm.status === "acknowledged"
                                  ? "bg-yellow-100 text-yellow-700 border-yellow-200"
                                  : "bg-green-100 text-green-700 border-green-200"
                            }
                          >
                            {alarm.status === "active"
                              ? "活跃"
                              : alarm.status === "acknowledged"
                                ? "已确认"
                                : "已解决"}
                          </Badge>
                        </TableCell>
                        <TableCell>
                          <div className="flex space-x-1">
                            {alarm.status === "active" && (
                              <Button
                                variant="outline"
                                size="sm"
                                className="border-gray-300 bg-gray-700/50 text-gray-200 hover:bg-gray-600"
                              >
                                确认
                              </Button>
                            )}
                            {alarm.status === "acknowledged" && (
                              <Button
                                variant="outline"
                                size="sm"
                                className="border-gray-300 bg-gray-700/50 text-gray-200 hover:bg-gray-600"
                              >
                                解决
                              </Button>
                            )}
                          </div>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </div>
    </div>
  );
}
