import { useState, useEffect } from "react";
import {
  <PERSON>,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Ta<PERSON>Trigger } from "@/components/ui/tabs";
import { StatusChart } from "@/components/StatusChart";
import { Progress } from "@/components/ui/progress";
import {
  Activity,
  Cpu,
  MemoryStick,
  HardDrive,
  Network,
  TrendingUp,
  TrendingDown,
  AlertTriangle,
  CheckCircle,
  Clock,
  RefreshCw,
  Download,
  Settings,
} from "lucide-react";

interface SystemMetrics {
  timestamp: string;
  cpu: {
    usage: number;
    cores: number;
    temperature: number;
    loadAverage: number[];
  };
  memory: {
    total: number;
    used: number;
    free: number;
    cached: number;
    swapTotal: number;
    swapUsed: number;
  };
  disk: {
    total: number;
    used: number;
    free: number;
    readSpeed: number;
    writeSpeed: number;
    iops: number;
  };
  network: {
    bytesIn: number;
    bytesOut: number;
    packetsIn: number;
    packetsOut: number;
    connections: number;
  };
  requests: {
    total: number;
    success: number;
    error: number;
    avgResponseTime: number;
    activeConnections: number;
  };
}

interface Alert {
  id: string;
  type: "warning" | "error" | "info";
  message: string;
  timestamp: string;
  severity: "low" | "medium" | "high";
  resolved: boolean;
}

export default function PerformanceMonitor() {
  const [metrics, setMetrics] = useState<SystemMetrics[]>([]);
  const [currentMetrics, setCurrentMetrics] = useState<SystemMetrics | null>(null);
  const [alerts, setAlerts] = useState<Alert[]>([]);
  const [refreshInterval, setRefreshInterval] = useState(5000);
  const [isAutoRefresh, setIsAutoRefresh] = useState(true);

  // 模拟实时数据
  useEffect(() => {
    const generateMetrics = (): SystemMetrics => ({
      timestamp: new Date().toLocaleString(),
      cpu: {
        usage: Math.random() * 100,
        cores: 8,
        temperature: 45 + Math.random() * 20,
        loadAverage: [Math.random() * 2, Math.random() * 2, Math.random() * 2],
      },
      memory: {
        total: 16384,
        used: 8192 + Math.random() * 4000,
        free: 4096 - Math.random() * 2000,
        cached: 2048 + Math.random() * 1000,
        swapTotal: 8192,
        swapUsed: Math.random() * 1000,
      },
      disk: {
        total: 1000000,
        used: 600000 + Math.random() * 50000,
        free: 400000 - Math.random() * 50000,
        readSpeed: Math.random() * 100,
        writeSpeed: Math.random() * 50,
        iops: Math.random() * 1000,
      },
      network: {
        bytesIn: Math.random() * 1000000,
        bytesOut: Math.random() * 500000,
        packetsIn: Math.random() * 10000,
        packetsOut: Math.random() * 8000,
        connections: 100 + Math.random() * 200,
      },
      requests: {
        total: 1000 + Math.random() * 500,
        success: 950 + Math.random() * 100,
        error: Math.random() * 50,
        avgResponseTime: 50 + Math.random() * 100,
        activeConnections: 50 + Math.random() * 100,
      },
    });

    const updateMetrics = () => {
      const newMetrics = generateMetrics();
      setCurrentMetrics(newMetrics);
      setMetrics(prev => [...prev.slice(-59), newMetrics]); // 保留最近60个数据点
    };

    updateMetrics();
    const interval = setInterval(updateMetrics, refreshInterval);

    return () => clearInterval(interval);
  }, [refreshInterval]);

  // 模拟告警数据
  useEffect(() => {
    const mockAlerts: Alert[] = [
      {
        id: "1",
        type: "warning",
        message: "CPU使用率超过80%",
        timestamp: "2024-01-15 15:30:00",
        severity: "medium",
        resolved: false,
      },
      {
        id: "2",
        type: "error",
        message: "内存使用率超过90%",
        timestamp: "2024-01-15 15:25:00",
        severity: "high",
        resolved: false,
      },
      {
        id: "3",
        type: "info",
        message: "磁盘空间不足警告",
        timestamp: "2024-01-15 15:20:00",
        severity: "low",
        resolved: true,
      },
    ];
    setAlerts(mockAlerts);
  }, []);

  const getStatusColor = (value: number, thresholds: { warning: number; critical: number }) => {
    if (value >= thresholds.critical) return "text-red-600";
    if (value >= thresholds.warning) return "text-yellow-600";
    return "text-green-600";
  };

  const getStatusBadge = (value: number, thresholds: { warning: number; critical: number }) => {
    if (value >= thresholds.critical) {
      return <Badge className="bg-red-100 text-red-800 border-red-200">严重</Badge>;
    }
    if (value >= thresholds.warning) {
      return <Badge className="bg-yellow-100 text-yellow-800 border-yellow-200">警告</Badge>;
    }
    return <Badge className="bg-green-100 text-green-800 border-green-200">正常</Badge>;
  };

  const getAlertIcon = (type: string) => {
    switch (type) {
      case "error":
        return <AlertTriangle className="h-4 w-4 text-red-600" />;
      case "warning":
        return <AlertTriangle className="h-4 w-4 text-yellow-600" />;
      case "info":
        return <CheckCircle className="h-4 w-4 text-blue-600" />;
      default:
        return <Info className="h-4 w-4 text-gray-600" />;
    }
  };

  if (!currentMetrics) return <div>加载中...</div>;

  const cpuUsage = currentMetrics.cpu.usage;
  const memoryUsage = (currentMetrics.memory.used / currentMetrics.memory.total) * 100;
  const diskUsage = (currentMetrics.disk.used / currentMetrics.disk.total) * 100;

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <div>
          <h2 className="text-3xl font-bold tracking-tight">性能监控</h2>
          <p className="text-muted-foreground">
            实时监控系统性能指标：CPU、内存、磁盘、网络和请求统计
          </p>
        </div>
        <div className="flex items-center space-x-3">
          <Button
            variant="outline"
            size="sm"
            onClick={() => setIsAutoRefresh(!isAutoRefresh)}
          >
            <RefreshCw className={`mr-2 h-4 w-4 ${isAutoRefresh ? 'animate-spin' : ''}`} />
            {isAutoRefresh ? '自动刷新' : '手动刷新'}
          </Button>
          <Button variant="outline" size="sm">
            <Download className="mr-2 h-4 w-4" />
            导出报告
          </Button>
        </div>
      </div>

      {/* 关键指标卡片 */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">CPU使用率</CardTitle>
            <Cpu className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className={`text-2xl font-bold ${getStatusColor(cpuUsage, { warning: 70, critical: 90 })}`}>
              {cpuUsage.toFixed(1)}%
            </div>
            <div className="flex items-center justify-between mt-2">
              <Progress value={cpuUsage} className="flex-1 mr-2" />
              {getStatusBadge(cpuUsage, { warning: 70, critical: 90 })}
            </div>
            <p className="text-xs text-muted-foreground mt-2">
              温度: {currentMetrics.cpu.temperature.toFixed(1)}°C
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">内存使用率</CardTitle>
            <MemoryStick className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className={`text-2xl font-bold ${getStatusColor(memoryUsage, { warning: 80, critical: 90 })}`}>
              {memoryUsage.toFixed(1)}%
            </div>
            <div className="flex items-center justify-between mt-2">
              <Progress value={memoryUsage} className="flex-1 mr-2" />
              {getStatusBadge(memoryUsage, { warning: 80, critical: 90 })}
            </div>
            <p className="text-xs text-muted-foreground mt-2">
              已用: {(currentMetrics.memory.used / 1024).toFixed(1)}GB / {(currentMetrics.memory.total / 1024).toFixed(1)}GB
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">磁盘使用率</CardTitle>
            <HardDrive className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className={`text-2xl font-bold ${getStatusColor(diskUsage, { warning: 85, critical: 95 })}`}>
              {diskUsage.toFixed(1)}%
            </div>
            <div className="flex items-center justify-between mt-2">
              <Progress value={diskUsage} className="flex-1 mr-2" />
              {getStatusBadge(diskUsage, { warning: 85, critical: 95 })}
            </div>
            <p className="text-xs text-muted-foreground mt-2">
              已用: {(currentMetrics.disk.used / 1024).toFixed(1)}GB / {(currentMetrics.disk.total / 1024).toFixed(1)}GB
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">请求成功率</CardTitle>
            <Network className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-green-600">
              {((currentMetrics.requests.success / currentMetrics.requests.total) * 100).toFixed(1)}%
            </div>
            <div className="flex items-center justify-between mt-2">
              <Progress 
                value={(currentMetrics.requests.success / currentMetrics.requests.total) * 100} 
                className="flex-1 mr-2" 
              />
              <Badge className="bg-green-100 text-green-800 border-green-200">正常</Badge>
            </div>
            <p className="text-xs text-muted-foreground mt-2">
              平均响应: {currentMetrics.requests.avgResponseTime.toFixed(0)}ms
            </p>
          </CardContent>
        </Card>
      </div>

      {/* 主要内容区域 */}
      <Tabs defaultValue="overview" className="space-y-4">
        <TabsList>
          <TabsTrigger value="overview">系统概览</TabsTrigger>
          <TabsTrigger value="cpu">CPU监控</TabsTrigger>
          <TabsTrigger value="memory">内存监控</TabsTrigger>
          <TabsTrigger value="disk">磁盘监控</TabsTrigger>
          <TabsTrigger value="network">网络监控</TabsTrigger>
          <TabsTrigger value="requests">请求监控</TabsTrigger>
          <TabsTrigger value="alerts">告警信息</TabsTrigger>
        </TabsList>

        {/* 系统概览 */}
        <TabsContent value="overview" className="space-y-4">
          <div className="grid gap-4 md:grid-cols-2">
            <Card>
              <CardHeader>
                <CardTitle>系统负载趋势</CardTitle>
                <CardDescription>最近1小时的系统负载变化</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="h-80">
                  <StatusChart
                    type="line"
                    data={metrics.map(m => ({
                      time: m.timestamp,
                      cpu: m.cpu.usage,
                      memory: (m.memory.used / m.memory.total) * 100,
                      disk: (m.disk.used / m.disk.total) * 100,
                    }))}
                    dataKey="cpu"
                    nameKey="time"
                    colors={["#ef4444", "#3b82f6", "#10b981"]}
                  />
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>请求统计</CardTitle>
                <CardDescription>请求数量和响应时间趋势</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="h-80">
                  <StatusChart
                    type="line"
                    data={metrics.map(m => ({
                      time: m.timestamp,
                      requests: m.requests.total,
                      responseTime: m.requests.avgResponseTime,
                    }))}
                    dataKey="requests"
                    nameKey="time"
                    colors={["#3b82f6", "#f59e0b"]}
                  />
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        {/* CPU监控 */}
        <TabsContent value="cpu" className="space-y-4">
          <div className="grid gap-4 md:grid-cols-2">
            <Card>
              <CardHeader>
                <CardTitle>CPU使用率趋势</CardTitle>
                <CardDescription>CPU使用率实时变化</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="h-80">
                  <StatusChart
                    type="line"
                    data={metrics.map(m => ({
                      time: m.timestamp,
                      usage: m.cpu.usage,
                    }))}
                    dataKey="usage"
                    nameKey="time"
                    colors={["#ef4444"]}
                  />
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>CPU详细信息</CardTitle>
                <CardDescription>CPU核心数和负载信息</CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <label className="text-sm font-medium">CPU核心数</label>
                    <p className="text-2xl font-bold">{currentMetrics.cpu.cores}</p>
                  </div>
                  <div>
                    <label className="text-sm font-medium">当前温度</label>
                    <p className="text-2xl font-bold">{currentMetrics.cpu.temperature.toFixed(1)}°C</p>
                  </div>
                </div>
                <div>
                  <label className="text-sm font-medium">负载平均值</label>
                  <div className="grid grid-cols-3 gap-2 mt-2">
                    <div className="text-center">
                      <p className="text-xs text-muted-foreground">1分钟</p>
                      <p className="text-lg font-semibold">{currentMetrics.cpu.loadAverage[0].toFixed(2)}</p>
                    </div>
                    <div className="text-center">
                      <p className="text-xs text-muted-foreground">5分钟</p>
                      <p className="text-lg font-semibold">{currentMetrics.cpu.loadAverage[1].toFixed(2)}</p>
                    </div>
                    <div className="text-center">
                      <p className="text-xs text-muted-foreground">15分钟</p>
                      <p className="text-lg font-semibold">{currentMetrics.cpu.loadAverage[2].toFixed(2)}</p>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        {/* 内存监控 */}
        <TabsContent value="memory" className="space-y-4">
          <div className="grid gap-4 md:grid-cols-2">
            <Card>
              <CardHeader>
                <CardTitle>内存使用趋势</CardTitle>
                <CardDescription>内存使用率实时变化</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="h-80">
                  <StatusChart
                    type="line"
                    data={metrics.map(m => ({
                      time: m.timestamp,
                      used: (m.memory.used / m.memory.total) * 100,
                      cached: (m.memory.cached / m.memory.total) * 100,
                    }))}
                    dataKey="used"
                    nameKey="time"
                    colors={["#3b82f6", "#10b981"]}
                  />
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>内存详细信息</CardTitle>
                <CardDescription>内存使用情况详细统计</CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-3">
                  <div className="flex justify-between">
                    <span>已用内存</span>
                    <span>{(currentMetrics.memory.used / 1024).toFixed(1)}GB</span>
                  </div>
                  <Progress value={(currentMetrics.memory.used / currentMetrics.memory.total) * 100} />
                  
                  <div className="flex justify-between">
                    <span>缓存内存</span>
                    <span>{(currentMetrics.memory.cached / 1024).toFixed(1)}GB</span>
                  </div>
                  <Progress value={(currentMetrics.memory.cached / currentMetrics.memory.total) * 100} />
                  
                  <div className="flex justify-between">
                    <span>空闲内存</span>
                    <span>{(currentMetrics.memory.free / 1024).toFixed(1)}GB</span>
                  </div>
                  <Progress value={(currentMetrics.memory.free / currentMetrics.memory.total) * 100} />
                </div>
                
                <div className="pt-4 border-t">
                  <div className="flex justify-between">
                    <span>交换分区使用</span>
                    <span>{((currentMetrics.memory.swapUsed / currentMetrics.memory.swapTotal) * 100).toFixed(1)}%</span>
                  </div>
                  <Progress value={(currentMetrics.memory.swapUsed / currentMetrics.memory.swapTotal) * 100} />
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        {/* 磁盘监控 */}
        <TabsContent value="disk" className="space-y-4">
          <div className="grid gap-4 md:grid-cols-2">
            <Card>
              <CardHeader>
                <CardTitle>磁盘使用趋势</CardTitle>
                <CardDescription>磁盘使用率实时变化</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="h-80">
                  <StatusChart
                    type="line"
                    data={metrics.map(m => ({
                      time: m.timestamp,
                      usage: (m.disk.used / m.disk.total) * 100,
                    }))}
                    dataKey="usage"
                    nameKey="time"
                    colors={["#10b981"]}
                  />
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>磁盘性能</CardTitle>
                <CardDescription>磁盘读写速度和IOPS</CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <label className="text-sm font-medium">读取速度</label>
                    <p className="text-2xl font-bold">{currentMetrics.disk.readSpeed.toFixed(1)} MB/s</p>
                  </div>
                  <div>
                    <label className="text-sm font-medium">写入速度</label>
                    <p className="text-2xl font-bold">{currentMetrics.disk.writeSpeed.toFixed(1)} MB/s</p>
                  </div>
                </div>
                <div>
                  <label className="text-sm font-medium">IOPS</label>
                  <p className="text-2xl font-bold">{currentMetrics.disk.iops.toFixed(0)}</p>
                </div>
                <div className="space-y-3">
                  <div className="flex justify-between">
                    <span>已用空间</span>
                    <span>{(currentMetrics.disk.used / 1024).toFixed(1)}GB</span>
                  </div>
                  <Progress value={(currentMetrics.disk.used / currentMetrics.disk.total) * 100} />
                  
                  <div className="flex justify-between">
                    <span>可用空间</span>
                    <span>{(currentMetrics.disk.free / 1024).toFixed(1)}GB</span>
                  </div>
                  <Progress value={(currentMetrics.disk.free / currentMetrics.disk.total) * 100} />
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        {/* 网络监控 */}
        <TabsContent value="network" className="space-y-4">
          <div className="grid gap-4 md:grid-cols-2">
            <Card>
              <CardHeader>
                <CardTitle>网络流量趋势</CardTitle>
                <CardDescription>网络流量实时变化</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="h-80">
                  <StatusChart
                    type="line"
                    data={metrics.map(m => ({
                      time: m.timestamp,
                      bytesIn: m.network.bytesIn / 1024 / 1024,
                      bytesOut: m.network.bytesOut / 1024 / 1024,
                    }))}
                    dataKey="bytesIn"
                    nameKey="time"
                    colors={["#3b82f6", "#ef4444"]}
                  />
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>网络统计</CardTitle>
                <CardDescription>网络连接和数据包统计</CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <label className="text-sm font-medium">入站流量</label>
                    <p className="text-2xl font-bold">{(currentMetrics.network.bytesIn / 1024 / 1024).toFixed(1)} MB/s</p>
                  </div>
                  <div>
                    <label className="text-sm font-medium">出站流量</label>
                    <p className="text-2xl font-bold">{(currentMetrics.network.bytesOut / 1024 / 1024).toFixed(1)} MB/s</p>
                  </div>
                  <div>
                    <label className="text-sm font-medium">入站数据包</label>
                    <p className="text-2xl font-bold">{currentMetrics.network.packetsIn.toFixed(0)}/s</p>
                  </div>
                  <div>
                    <label className="text-sm font-medium">出站数据包</label>
                    <p className="text-2xl font-bold">{currentMetrics.network.packetsOut.toFixed(0)}/s</p>
                  </div>
                </div>
                <div>
                  <label className="text-sm font-medium">活跃连接数</label>
                  <p className="text-2xl font-bold">{currentMetrics.network.connections.toFixed(0)}</p>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        {/* 请求监控 */}
        <TabsContent value="requests" className="space-y-4">
          <div className="grid gap-4 md:grid-cols-2">
            <Card>
              <CardHeader>
                <CardTitle>请求统计趋势</CardTitle>
                <CardDescription>请求数量和成功率变化</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="h-80">
                  <StatusChart
                    type="line"
                    data={metrics.map(m => ({
                      time: m.timestamp,
                      total: m.requests.total,
                      success: m.requests.success,
                      error: m.requests.error,
                    }))}
                    dataKey="total"
                    nameKey="time"
                    colors={["#3b82f6", "#10b981", "#ef4444"]}
                  />
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>请求性能</CardTitle>
                <CardDescription>响应时间和连接统计</CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <label className="text-sm font-medium">总请求数</label>
                    <p className="text-2xl font-bold">{currentMetrics.requests.total.toFixed(0)}/s</p>
                  </div>
                  <div>
                    <label className="text-sm font-medium">成功请求</label>
                    <p className="text-2xl font-bold text-green-600">{currentMetrics.requests.success.toFixed(0)}/s</p>
                  </div>
                  <div>
                    <label className="text-sm font-medium">错误请求</label>
                    <p className="text-2xl font-bold text-red-600">{currentMetrics.requests.error.toFixed(0)}/s</p>
                  </div>
                  <div>
                    <label className="text-sm font-medium">活跃连接</label>
                    <p className="text-2xl font-bold">{currentMetrics.requests.activeConnections.toFixed(0)}</p>
                  </div>
                </div>
                <div>
                  <label className="text-sm font-medium">平均响应时间</label>
                  <p className="text-2xl font-bold">{currentMetrics.requests.avgResponseTime.toFixed(0)}ms</p>
                </div>
                <div className="space-y-3">
                  <div className="flex justify-between">
                    <span>成功率</span>
                    <span>{((currentMetrics.requests.success / currentMetrics.requests.total) * 100).toFixed(1)}%</span>
                  </div>
                  <Progress value={(currentMetrics.requests.success / currentMetrics.requests.total) * 100} />
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        {/* 告警信息 */}
        <TabsContent value="alerts" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>系统告警</CardTitle>
              <CardDescription>当前活跃的系统告警信息</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {alerts.map((alert) => (
                  <div
                    key={alert.id}
                    className={`flex items-center justify-between p-4 rounded-lg border ${
                      alert.resolved ? 'bg-gray-50' : 'bg-red-50 border-red-200'
                    }`}
                  >
                    <div className="flex items-center space-x-3">
                      {getAlertIcon(alert.type)}
                      <div>
                        <p className="font-medium">{alert.message}</p>
                        <p className="text-sm text-muted-foreground">
                          {alert.timestamp} - 严重程度: {alert.severity}
                        </p>
                      </div>
                    </div>
                    <div className="flex items-center space-x-2">
                      {alert.resolved ? (
                        <Badge className="bg-green-100 text-green-800 border-green-200">已解决</Badge>
                      ) : (
                        <Badge className="bg-red-100 text-red-800 border-red-200">未解决</Badge>
                      )}
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
} 