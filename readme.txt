项目概述
这是一个基于 React + TypeScript + Vite 的智能仓储管理系统，使用了现代化的前端技术栈。项目名称为"A产品智能化存储及月稳测试系统"，主要用于管理工业设备、测试流程和仓储操作。
技术栈
前端框架：React 18.3.1 + TypeScript
构建工具：Vite 6.2.2
UI组件库：
基于 Radix UI 的自定义组件
TailwindCSS 用于样式管理
Lucide React 图标库
状态管理：React Query (@tanstack/react-query)
路由：React Router DOM
图表库：Recharts
3D可视化：Three.js (@react-three/fiber, @react-three/drei)
表单处理：React Hook Form + Zod
主要功能模块
工业大屏可视化
3D仓储可视化展示
实时设备状态监控
环境数据展示
报警信息管理
测试控制中心
自动/手动模式切换
设备控制（桁架、AGV）
一键故障还原功能
实时操作监控
三色灯控制管理
12个控制板管理（每板50灯）
IP地址配置
货架映射关系
实时状态监控
设备管理
设备基准信息
AGV充电管理
年度保养计划
设备数据分析
出入库管理
出入库操作
出入库记录
系统管理
用户管理
角色管理
系统设置
项目结构
项目采用了清晰的目录结构：
src/components/：包含UI组件
ui/ 目录包含基础UI组件
自定义业务组件如DashboardCard、Layout、StatusChart等
src/pages/：页面组件
按功能模块组织，如equipment/、test/、warehouse/等
src/lib/：工具函数和通用逻辑
特色功能
3D仓库可视化：使用Three.js实现的仓库3D可视化，展示货架、AGV和测试台的实时状态
一键还原功能：在测试控制中心，提供一键还原功能，可快速重置故障设备和任务
三色灯控制系统：管理600个货位的三色灯状态，用于指示产品状态（绿色表示有产品，黄色闪烁表示测试中，红色表示空货位）
自动/手动模式切换：系统支持自动和手动两种控制模式，适应不同场景需求
设计风格
项目使用深蓝工业风格的UI设计：
主色调：深蓝工业系列
背景：slate-950, blue-950, indigo-950
边框：blue-400/30, indigo-400/40
文字：blue-100, blue-200, blue-300
状态颜色：绿色(运行)、黄色(暂停)、红色(故障)
总结
这是一个功能完善的工业级智能仓储管理系统，专注于A产品的存储和测试管理。系统集成了3D可视化、设备控制、测试管理等多种功能，界面设计符合工业应用的专业性和实用性需求。项目使用了现代化的前端技术栈，代码结构清晰，组件化程度高，便于维护和扩展。