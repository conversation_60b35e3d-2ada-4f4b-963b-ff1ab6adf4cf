import React, { useState } from "react";
import { <PERSON>, Card<PERSON>ontent, Card<PERSON><PERSON>er, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Input } from "@/components/ui/input";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog";
import { 
  Package, 
  Search, 
  Filter, 
  Download, 
  Eye,
  MapPin,
  Calendar,
  Tag,
  Warehouse,
  Layers
} from "lucide-react";

// 模拟特殊产品数据
const mockSpecialProducts = [
  {
    id: "SP001",
    productCode: "SP-2024-001",
    productType: "精密仪器",
    status: "正常",
          area: "B区",
    shelfNumber: "E-01",
    positionNumber: "E-01-01",
    registrationTime: "2024-01-15 09:30:00",
    rfidTag: "RFID-E001-001",
    description: "高精度测量仪器"
  },
  {
    id: "SP002",
    productCode: "SP-2024-002",
    productType: "光学设备",
    status: "正常",
          area: "C区",
    shelfNumber: "F-02",
    positionNumber: "F-02-03",
    registrationTime: "2024-01-16 14:20:00",
    rfidTag: "RFID-F002-003",
    description: "光学检测设备"
  },
  {
    id: "SP003",
    productCode: "SP-2024-003",
    productType: "电子元件",
    status: "待入库",
          area: "G区",
    shelfNumber: "G-03",
    positionNumber: "G-03-02",
    registrationTime: "2024-01-17 11:45:00",
    rfidTag: "RFID-G003-002",
    description: "高价值电子元件"
  }
];

export default function SpecialProductInventory() {
  const [products, setProducts] = useState(mockSpecialProducts);
  const [searchTerm, setSearchTerm] = useState("");
  const [statusFilter, setStatusFilter] = useState("全部");
  const [areaFilter, setAreaFilter] = useState("全部");

  // 统计数据
  const totalProducts = products.length;
  const normalProducts = products.filter(p => p.status === "正常").length;
  const pendingProducts = products.filter(p => p.status === "待入库").length;

  // 过滤产品
  const filteredProducts = products.filter(product => {
    const matchesSearch = product.productCode.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         product.productType.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         product.rfidTag.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesStatus = statusFilter === "全部" || product.status === statusFilter;
    const matchesArea = areaFilter === "全部" || product.area === areaFilter;
    
    return matchesSearch && matchesStatus && matchesArea;
  });

  const getStatusBadge = (status: string) => {
    switch (status) {
      case "正常":
        return <Badge className="bg-green-100 text-green-800 border-green-200">正常</Badge>;
      case "待入库":
        return <Badge className="bg-yellow-100 text-yellow-800 border-yellow-200">待入库</Badge>;
      default:
        return <Badge variant="outline">{status}</Badge>;
    }
  };

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h2 className="text-3xl font-bold tracking-tight text-slate-800">特殊产品台账</h2>
        <div className="flex items-center space-x-2">
          <Button variant="outline" className="flex items-center gap-2">
            <Download className="h-4 w-4" />
            导出台账
          </Button>
        </div>
      </div>

      {/* 统计卡片 */}
      <div className="grid gap-4 md:grid-cols-3">
        <Card className="blue-tech-card">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium text-slate-700">总产品数量</CardTitle>
            <Package className="h-4 w-4 text-blue-500" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-slate-900">{totalProducts}</div>
            <p className="text-xs text-slate-500">特殊产品总数</p>
          </CardContent>
        </Card>

        <Card className="blue-tech-card">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium text-slate-700">在库产品</CardTitle>
            <Warehouse className="h-4 w-4 text-green-500" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-slate-900">{normalProducts}</div>
            <p className="text-xs text-slate-500">正常状态产品</p>
          </CardContent>
        </Card>

        <Card className="blue-tech-card">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium text-slate-700">待入库</CardTitle>
            <Layers className="h-4 w-4 text-yellow-500" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-slate-900">{pendingProducts}</div>
            <p className="text-xs text-slate-500">等待入库产品</p>
          </CardContent>
        </Card>
      </div>

      {/* 搜索和筛选 */}
      <Card className="blue-tech-card">
        <CardHeader>
          <CardTitle className="text-slate-800">搜索和筛选</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex flex-col md:flex-row gap-4">
            <div className="flex-1">
              <div className="relative">
                <Search className="absolute left-3 top-3 h-4 w-4 text-slate-400" />
                <Input
                  placeholder="搜索产品编号、类型或RFID标签..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10"
                />
              </div>
            </div>
            <Select value={statusFilter} onValueChange={setStatusFilter}>
              <SelectTrigger className="w-full md:w-40">
                <SelectValue placeholder="状态筛选" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="全部">全部状态</SelectItem>
                <SelectItem value="正常">正常</SelectItem>
                <SelectItem value="待入库">待入库</SelectItem>
              </SelectContent>
            </Select>
            <Select value={areaFilter} onValueChange={setAreaFilter}>
              <SelectTrigger className="w-full md:w-40">
                <SelectValue placeholder="区域筛选" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="全部">全部区域</SelectItem>
                                                            <SelectItem value="B区">B区</SelectItem>
                       <SelectItem value="C区">C区</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </CardContent>
      </Card>

      {/* 产品列表 */}
      <Card className="blue-tech-card">
        <CardHeader>
          <CardTitle className="text-slate-800">特殊产品列表</CardTitle>
        </CardHeader>
        <CardContent>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>产品编号</TableHead>
                <TableHead>产品类型</TableHead>
                <TableHead>RFID标签</TableHead>
                <TableHead>状态</TableHead>
                <TableHead>当前位置</TableHead>
                <TableHead>登记时间</TableHead>
                <TableHead>操作</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {filteredProducts.map((product) => (
                <TableRow key={product.id}>
                  <TableCell className="font-medium">{product.productCode}</TableCell>
                  <TableCell>{product.productType}</TableCell>
                  <TableCell className="font-mono text-sm">{product.rfidTag}</TableCell>
                  <TableCell>{getStatusBadge(product.status)}</TableCell>
                  <TableCell>
                    <div className="flex items-center gap-1">
                      <MapPin className="h-3 w-3 text-slate-500" />
                      <span>{product.area} {product.shelfNumber} {product.positionNumber}</span>
                    </div>
                  </TableCell>
                  <TableCell>
                    <div className="flex items-center gap-1">
                      <Calendar className="h-3 w-3 text-slate-500" />
                      <span className="text-sm">{product.registrationTime}</span>
                    </div>
                  </TableCell>
                  <TableCell>
                    <div className="flex items-center gap-2">
                      <Dialog>
                        <DialogTrigger asChild>
                          <Button variant="outline" size="sm">
                            <Eye className="h-4 w-4" />
                          </Button>
                        </DialogTrigger>
                        <DialogContent>
                          <DialogHeader>
                            <DialogTitle>产品详情</DialogTitle>
                          </DialogHeader>
                          <div className="space-y-4">
                            <div className="grid grid-cols-2 gap-4">
                              <div>
                                <label className="text-sm font-medium text-slate-600">产品编号</label>
                                <p className="text-sm">{product.productCode}</p>
                              </div>
                              <div>
                                <label className="text-sm font-medium text-slate-600">产品类型</label>
                                <p className="text-sm">{product.productType}</p>
                              </div>
                              <div>
                                <label className="text-sm font-medium text-slate-600">RFID标签</label>
                                <p className="text-sm font-mono">{product.rfidTag}</p>
                              </div>
                              <div>
                                <label className="text-sm font-medium text-slate-600">状态</label>
                                <div>{getStatusBadge(product.status)}</div>
                              </div>
                              <div>
                                <label className="text-sm font-medium text-slate-600">区域</label>
                                <p className="text-sm">{product.area}</p>
                              </div>
                              <div>
                                <label className="text-sm font-medium text-slate-600">货架号</label>
                                <p className="text-sm">{product.shelfNumber}</p>
                              </div>
                              <div>
                                <label className="text-sm font-medium text-slate-600">货位号</label>
                                <p className="text-sm">{product.positionNumber}</p>
                              </div>
                              <div>
                                <label className="text-sm font-medium text-slate-600">登记时间</label>
                                <p className="text-sm">{product.registrationTime}</p>
                              </div>
                            </div>
                            <div>
                              <label className="text-sm font-medium text-slate-600">产品描述</label>
                              <p className="text-sm">{product.description}</p>
                            </div>
                          </div>
                        </DialogContent>
                      </Dialog>
                      {product.status === "待入库" && (
                        <Button size="sm" className="bg-blue-600 hover:bg-blue-700">
                          入库操作
                        </Button>
                      )}
                      {product.status === "正常" && (
                        <Button size="sm" variant="outline" className="text-orange-600 border-orange-200 hover:bg-orange-50">
                          出库操作
                        </Button>
                      )}
                    </div>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </CardContent>
      </Card>
    </div>
  );
} 