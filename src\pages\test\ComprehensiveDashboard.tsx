import { useState } from "react";
import { DashboardCard } from "@/components/DashboardCard";
import { StatusChart } from "@/components/StatusChart";
import { WarehouseVisualization } from "@/components/WarehouseVisualization";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Tabs, Ta<PERSON><PERSON>ontent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Button } from "@/components/ui/button";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  Activity,
  AlertTriangle,
  CheckCircle,
  Clock,
  Package,
  Play,
  Pause,
  RotateCcw,
  Eye,
  Maximize,
} from "lucide-react";

const realTimeTestData = [
  {
    id: "TP001",
    productId: "A-2024-001",
    testStation: "测试台-01",
    progress: 75,
    startTime: "09:30",
    estimatedCompletion: "17:30",
    status: "running",
    operator: "张三",
  },
  {
    id: "TP002",
    productId: "A-2024-002",
    testStation: "测试台-03",
    progress: 45,
    startTime: "10:15",
    estimatedCompletion: "18:45",
    status: "running",
    operator: "李四",
  },
  {
    id: "TP003",
    productId: "A-2024-003",
    testStation: "测试台-05",
    progress: 90,
    startTime: "08:45",
    estimatedCompletion: "16:30",
    status: "running",
    operator: "王五",
  },
  {
    id: "TP004",
    productId: "A-2024-004",
    testStation: "测试台-07",
    progress: 100,
    startTime: "08:00",
    estimatedCompletion: "16:00",
    status: "completed",
    operator: "赵六",
  },
];

const testPlanStatusData = [
  { name: "已完成", value: 156 },
  { name: "进行中", value: 12 },
  { name: "待开始", value: 28 },
  { name: "已延期", value: 4 },
];

const alertsData = [
  {
    id: "AL001",
    type: "equipment",
    message: "测试台-02 机械臂异常",
    severity: "high",
    time: "14:30",
    status: "active",
  },
  {
    id: "AL002",
    type: "test",
    message: "A-2024-005 测试超时",
    severity: "medium",
    time: "13:45",
    status: "active",
  },
  {
    id: "AL003",
    type: "system",
            message: "AMR-02 电量不足",
    severity: "low",
    time: "12:20",
    status: "resolved",
  },
];

const warehouseProducts = [
  {
    id: "A-2024-001",
    position: [-9, 0.5, -4] as [number, number, number],
    status: "testing" as const,
  },
  {
    id: "A-2024-002",
    position: [-8, 0.5, -4] as [number, number, number],
    status: "stored" as const,
  },
  {
    id: "A-2024-003",
    position: [-7, 0.5, -4] as [number, number, number],
    status: "testing" as const,
  },
  {
    id: "A-2024-004",
    position: [9, 0.5, -4] as [number, number, number],
    status: "stored" as const,
  },
  {
    id: "A-2024-005",
    position: [8, 0.5, -4] as [number, number, number],
    status: "stored" as const,
  },
];

export default function ComprehensiveDashboard() {
  const [selectedView, setSelectedView] = useState("3d");

  const getStatusBadge = (status: string) => {
    const statusConfig = {
      running: { label: "进行中", variant: "default" as const, icon: Play },
      completed: {
        label: "已完成",
        variant: "secondary" as const,
        icon: CheckCircle,
      },
      paused: { label: "已暂停", variant: "destructive" as const, icon: Pause },
      pending: { label: "待开始", variant: "secondary" as const, icon: Clock },
    };

    const config = statusConfig[status as keyof typeof statusConfig];
    const Icon = config.icon;
    return (
      <Badge variant={config.variant} className="flex items-center gap-1">
        <Icon className="h-3 w-3" />
        {config.label}
      </Badge>
    );
  };

  const getSeverityBadge = (severity: string) => {
    const severityConfig = {
      high: { label: "高", variant: "destructive" as const },
      medium: { label: "中", variant: "default" as const },
      low: { label: "低", variant: "secondary" as const },
    };

    const config = severityConfig[severity as keyof typeof severityConfig];
    return <Badge variant={config.variant}>{config.label}</Badge>;
  };

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h2 className="text-3xl font-bold tracking-tight">综合看板</h2>
        <div className="flex items-center space-x-2">
          <Button variant="outline" size="sm">
            <RotateCcw className="mr-2 h-4 w-4" />
            刷新
          </Button>
          <Button variant="outline" size="sm">
            <Maximize className="mr-2 h-4 w-4" />
            全屏
          </Button>
        </div>
      </div>

      <Tabs defaultValue="monitoring" className="space-y-4">
        <TabsList>
          <TabsTrigger value="monitoring">测试监控看板</TabsTrigger>
          <TabsTrigger value="alerts">异常看板</TabsTrigger>
          <TabsTrigger value="plans">测试计划状态</TabsTrigger>
        </TabsList>

        <TabsContent value="monitoring">
          <div className="space-y-6">
            {/* 3D Visualization */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center justify-between">
                  <span>仓储与测试系统实时监控</span>
                  <div className="flex items-center space-x-2">
                    <Button
                      variant={selectedView === "3d" ? "default" : "outline"}
                      size="sm"
                      onClick={() => setSelectedView("3d")}
                    >
                      3D视图
                    </Button>
                    <Button
                      variant={selectedView === "table" ? "default" : "outline"}
                      size="sm"
                      onClick={() => setSelectedView("table")}
                    >
                      表格视图
                    </Button>
                  </div>
                </CardTitle>
              </CardHeader>
              <CardContent>
                {selectedView === "3d" ? (
                  <WarehouseVisualization
                    products={warehouseProducts}
                    className="border rounded-lg"
                  />
                ) : (
                  <Table>
                    <TableHeader>
                      <TableRow>
                        <TableHead>产品编号</TableHead>
                        <TableHead>位置</TableHead>
                        <TableHead>状态</TableHead>
                        <TableHead>操作</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {warehouseProducts.map((product) => (
                        <TableRow key={product.id}>
                          <TableCell className="font-medium">
                            {product.id}
                          </TableCell>
                          <TableCell>
                            货位 [{product.position[0].toFixed(0)},{" "}
                            {product.position[2].toFixed(0)}]
                          </TableCell>
                          <TableCell>
                            <Badge
                              variant={
                                product.status === "testing"
                                  ? "default"
                                  : "secondary"
                              }
                            >
                              {product.status === "testing"
                                ? "测试中"
                                : "已存储"}
                            </Badge>
                          </TableCell>
                          <TableCell>
                            <Button size="sm" variant="outline">
                              <Eye className="h-3 w-3" />
                            </Button>
                          </TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                )}
              </CardContent>
            </Card>

            {/* Real-time Test Status */}
            <div className="grid gap-6 md:grid-cols-2">
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center">
                    <Activity className="mr-2 h-4 w-4" />
                    实时测试状态
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    {realTimeTestData.map((test) => (
                      <div key={test.id} className="border rounded-lg p-4">
                        <div className="flex items-center justify-between mb-2">
                          <div className="flex items-center space-x-2">
                            <span className="font-medium">
                              {test.productId}
                            </span>
                            <span className="text-sm text-gray-500">
                              ({test.testStation})
                            </span>
                          </div>
                          {getStatusBadge(test.status)}
                        </div>

                        <div className="space-y-2">
                          <div className="flex justify-between text-sm">
                            <span>测试进度:</span>
                            <span>{test.progress}%</span>
                          </div>
                          <div className="w-full bg-gray-200 rounded-full h-2">
                            <div
                              className={`h-2 rounded-full ${
                                test.status === "completed"
                                  ? "bg-green-600"
                                  : "bg-blue-600"
                              }`}
                              style={{ width: `${test.progress}%` }}
                            ></div>
                          </div>

                          <div className="grid grid-cols-2 gap-4 text-sm">
                            <div>
                              <span className="text-gray-500">开始时间:</span>
                              <div>{test.startTime}</div>
                            </div>
                            <div>
                              <span className="text-gray-500">预计完成:</span>
                              <div>{test.estimatedCompletion}</div>
                            </div>
                          </div>

                          <div className="text-sm">
                            <span className="text-gray-500">操作员:</span>
                            <span className="ml-1">{test.operator}</span>
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center">
                    <Package className="mr-2 h-4 w-4" />
                    测试设备状态
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="grid grid-cols-2 gap-3">
                    {Array.from({ length: 7 }, (_, i) => {
                      const stationNumber = i + 1;
                      const isActive = [1, 3, 5, 7].includes(stationNumber);
                      const currentTest = realTimeTestData.find(
                        (t) =>
                          t.testStation ===
                          `测试台-${String(stationNumber).padStart(2, "0")}`,
                      );

                      return (
                        <div
                          key={stationNumber}
                          className="border rounded-lg p-3"
                        >
                          <div className="flex items-center justify-between mb-1">
                            <span className="text-sm font-medium">
                              测试台-{String(stationNumber).padStart(2, "0")}
                            </span>
                            <Badge
                              variant={isActive ? "default" : "secondary"}
                              className={
                                isActive ? "bg-green-100 text-green-800" : ""
                              }
                            >
                              {isActive ? "运行中" : "待机"}
                            </Badge>
                          </div>

                          {currentTest && (
                            <div className="text-xs text-gray-500">
                              {currentTest.productId}
                            </div>
                          )}

                          {isActive && currentTest && (
                            <div className="mt-2">
                              <div className="w-full bg-gray-200 rounded-full h-1">
                                <div
                                  className="bg-blue-600 h-1 rounded-full"
                                  style={{ width: `${currentTest.progress}%` }}
                                ></div>
                              </div>
                            </div>
                          )}
                        </div>
                      );
                    })}
                  </div>
                </CardContent>
              </Card>
            </div>

            {/* Summary Statistics */}
            <div className="grid gap-4 md:grid-cols-4">
              <Card>
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium">
                    正在测试
                  </CardTitle>
                  <Activity className="h-4 w-4 text-muted-foreground" />
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">4</div>
                  <p className="text-xs text-muted-foreground">7台设备中</p>
                </CardContent>
              </Card>

              <Card>
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium">
                    今日完成
                  </CardTitle>
                  <CheckCircle className="h-4 w-4 text-muted-foreground" />
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">12</div>
                  <p className="text-xs text-muted-foreground">+3 较昨日</p>
                </CardContent>
              </Card>

              <Card>
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium">
                    平均效率
                  </CardTitle>
                  <Package className="h-4 w-4 text-muted-foreground" />
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">87%</div>
                  <p className="text-xs text-muted-foreground">本周平均</p>
                </CardContent>
              </Card>

              <Card>
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium">
                    待处理异常
                  </CardTitle>
                  <AlertTriangle className="h-4 w-4 text-muted-foreground" />
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">2</div>
                  <p className="text-xs text-muted-foreground">需要关注</p>
                </CardContent>
              </Card>
            </div>
          </div>
        </TabsContent>

        <TabsContent value="alerts">
          <div className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <AlertTriangle className="mr-2 h-4 w-4" />
                  系统异常告警
                </CardTitle>
              </CardHeader>
              <CardContent>
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>告警编号</TableHead>
                      <TableHead>类型</TableHead>
                      <TableHead>告警信息</TableHead>
                      <TableHead>严重程度</TableHead>
                      <TableHead>发生时间</TableHead>
                      <TableHead>状态</TableHead>
                      <TableHead>操作</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {alertsData.map((alert) => (
                      <TableRow key={alert.id}>
                        <TableCell className="font-medium">
                          {alert.id}
                        </TableCell>
                        <TableCell>
                          <Badge variant="outline">
                            {alert.type === "equipment"
                              ? "设备"
                              : alert.type === "test"
                                ? "测试"
                                : "系统"}
                          </Badge>
                        </TableCell>
                        <TableCell>{alert.message}</TableCell>
                        <TableCell>
                          {getSeverityBadge(alert.severity)}
                        </TableCell>
                        <TableCell>{alert.time}</TableCell>
                        <TableCell>
                          <Badge
                            variant={
                              alert.status === "active"
                                ? "destructive"
                                : "secondary"
                            }
                          >
                            {alert.status === "active" ? "激活" : "已解决"}
                          </Badge>
                        </TableCell>
                        <TableCell>
                          <Button size="sm" variant="outline">
                            查看详情
                          </Button>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </CardContent>
            </Card>

            <div className="grid gap-6 md:grid-cols-3">
              <Card>
                <CardHeader>
                  <CardTitle>告警统计</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="flex items-center justify-between">
                    <span className="text-sm">今日告警</span>
                    <span className="font-semibold">3</span>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-sm">本周告警</span>
                    <span className="font-semibold">15</span>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-sm">未处理告警</span>
                    <span className="font-semibold text-red-600">2</span>
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle>告警趋势</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="flex items-center justify-between">
                    <span className="text-sm">设备告警</span>
                    <span className="text-sm text-red-600">↑ 20%</span>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-sm">测试告警</span>
                    <span className="text-sm text-green-600">↓ 10%</span>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-sm">系统告警</span>
                    <span className="text-sm text-gray-600">→ 0%</span>
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle>处理效率</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="flex items-center justify-between">
                    <span className="text-sm">平均响应时间</span>
                    <span className="font-semibold">15分钟</span>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-sm">平均解决时间</span>
                    <span className="font-semibold">2小时</span>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-sm">解决率</span>
                    <span className="font-semibold text-green-600">95%</span>
                  </div>
                </CardContent>
              </Card>
            </div>
          </div>
        </TabsContent>

        <TabsContent value="plans">
          <div className="space-y-6">
            <DashboardCard
              title="测试计划状态分布"
              description="当前所有测试计划的状态分布"
            >
              <StatusChart type="pie" data={testPlanStatusData} />
            </DashboardCard>

            <div className="grid gap-6 md:grid-cols-2">
              <Card>
                <CardHeader>
                  <CardTitle>计划执行统计</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="flex items-center justify-between">
                    <span className="text-sm font-medium">总计划数</span>
                    <span className="text-2xl font-bold">200</span>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-sm font-medium">完成率</span>
                    <span className="text-2xl font-bold text-green-600">
                      78%
                    </span>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-sm font-medium">准时完成率</span>
                    <span className="text-2xl font-bold">92%</span>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-sm font-medium">延期计划</span>
                    <span className="text-2xl font-bold text-red-600">4</span>
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle>本周计划概览</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="space-y-3">
                    <div className="flex items-center justify-between">
                      <span className="text-sm">周一</span>
                      <div className="flex items-center space-x-2">
                        <div className="w-20 bg-gray-200 rounded-full h-2">
                          <div
                            className="bg-green-600 h-2 rounded-full"
                            style={{ width: "100%" }}
                          ></div>
                        </div>
                        <span className="text-sm">8/8</span>
                      </div>
                    </div>
                    <div className="flex items-center justify-between">
                      <span className="text-sm">周二</span>
                      <div className="flex items-center space-x-2">
                        <div className="w-20 bg-gray-200 rounded-full h-2">
                          <div
                            className="bg-green-600 h-2 rounded-full"
                            style={{ width: "90%" }}
                          ></div>
                        </div>
                        <span className="text-sm">9/10</span>
                      </div>
                    </div>
                    <div className="flex items-center justify-between">
                      <span className="text-sm">周三</span>
                      <div className="flex items-center space-x-2">
                        <div className="w-20 bg-gray-200 rounded-full h-2">
                          <div
                            className="bg-blue-600 h-2 rounded-full"
                            style={{ width: "70%" }}
                          ></div>
                        </div>
                        <span className="text-sm">7/10</span>
                      </div>
                    </div>
                    <div className="flex items-center justify-between">
                      <span className="text-sm">周四</span>
                      <div className="flex items-center space-x-2">
                        <div className="w-20 bg-gray-200 rounded-full h-2">
                          <div
                            className="bg-blue-600 h-2 rounded-full"
                            style={{ width: "60%" }}
                          ></div>
                        </div>
                        <span className="text-sm">6/10</span>
                      </div>
                    </div>
                    <div className="flex items-center justify-between">
                      <span className="text-sm">周五</span>
                      <div className="flex items-center space-x-2">
                        <div className="w-20 bg-gray-200 rounded-full h-2">
                          <div
                            className="bg-gray-300 h-2 rounded-full"
                            style={{ width: "20%" }}
                          ></div>
                        </div>
                        <span className="text-sm">2/10</span>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>
          </div>
        </TabsContent>
      </Tabs>
    </div>
  );
}
